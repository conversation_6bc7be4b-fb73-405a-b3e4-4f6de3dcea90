﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Chọn màu</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>Hủy bỏ</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="lblAlpha.Text" xml:space="preserve">
    <value>Alpha:</value>
  </data>
  <data name="lblCyan.Text" xml:space="preserve">
    <value>Màu lục lam:</value>
  </data>
  <data name="lblDecimal.Text" xml:space="preserve">
    <value>Số thập phân:</value>
  </data>
  <data name="lblKey.Text" xml:space="preserve">
    <value>Key:</value>
  </data>
  <data name="lblMagenta.Text" xml:space="preserve">
    <value>Màu đỏ tươi:</value>
  </data>
  <data name="lblNew.Text" xml:space="preserve">
    <value>Mới:</value>
  </data>
  <data name="lblOld.Text" xml:space="preserve">
    <value>Cũ:</value>
  </data>
  <data name="lblYellow.Text" xml:space="preserve">
    <value>Vàng:</value>
  </data>
  <data name="rbBlue.Text" xml:space="preserve">
    <value>Blue (Xanh dương):</value>
  </data>
  <data name="rbBrightness.Text" xml:space="preserve">
    <value>Độ sáng:</value>
  </data>
  <data name="rbGreen.Text" xml:space="preserve">
    <value>Xanh lá cây:</value>
  </data>
  <data name="rbHue.Text" xml:space="preserve">
    <value>Hue:</value>
  </data>
  <data name="rbRed.Text" xml:space="preserve">
    <value>Đỏ (Red):</value>
  </data>
  <data name="rbSaturation.Text" xml:space="preserve">
    <value>Tương phản:</value>
  </data>
  <data name="btnClose.Text" xml:space="preserve">
    <value>Đóng</value>
  </data>
  <data name="mbCopy.Text" xml:space="preserve">
    <value>Sao chép</value>
  </data>
  <data name="tsmiCopyAll.Text" xml:space="preserve">
    <value>Sao chép tất cả</value>
  </data>
  <data name="tsmiCopyCMYK.Text" xml:space="preserve">
    <value>Sao chép mã CMYK</value>
  </data>
  <data name="tsmiCopyDecimal.Text" xml:space="preserve">
    <value>Sao chép mã thập phân</value>
  </data>
  <data name="tsmiCopyHexadecimal.Text" xml:space="preserve">
    <value>Sao chép mã hệ 16 (hex)</value>
  </data>
  <data name="tsmiCopyHSB.Text" xml:space="preserve">
    <value>Sao chép mã HSB</value>
  </data>
  <data name="tsmiCopyPosition.Text" xml:space="preserve">
    <value>Sao chép vị trí</value>
  </data>
  <data name="tsmiCopyRGB.Text" xml:space="preserve">
    <value>Sao chép mã RGB</value>
  </data>
  <data name="lblCursorPosition.Text" xml:space="preserve">
    <value>Vị trí con trỏ:</value>
  </data>
  <data name="cbTransparent.ToolTip" xml:space="preserve">
    <value>Trong suốt</value>
  </data>
  <data name="lblHex.Text" xml:space="preserve">
    <value>Mã 16 (Hex):</value>
  </data>
  <data name="btnScreenColorPicker.ToolTip" xml:space="preserve">
    <value>Chọn màu từ màn hình</value>
  </data>
  <data name="rbRecentColors.Text" xml:space="preserve">
    <value>Các màu gần đây</value>
  </data>
  <data name="rbStandardColors.Text" xml:space="preserve">
    <value>Các màu cơ bản</value>
  </data>
  <data name="txtY.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtX.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lblY.Text" xml:space="preserve">
    <value>Y:</value>
  </data>
  <data name="lblX.Text" xml:space="preserve">
    <value>X:</value>
  </data>
  <data name="txtDecimal.Text" xml:space="preserve">
    <value>12345678</value>
  </data>
  <data name="lblCyanPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="lblMagentaPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="lblYellowPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="lblKeyPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="lblBrightnessPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="lblSaturationPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="lblHue.Text" xml:space="preserve">
    <value>°</value>
  </data>
  <data name="txtHex.Text" xml:space="preserve">
    <value>FF00FF00</value>
  </data>
  <data name="btnClipboardColorPicker.ToolTip" xml:space="preserve">
    <value>Chọn màu từ khay nhớ tạm</value>
  </data>
  <data name="lblName.Text" xml:space="preserve">
    <value>Tên:</value>
  </data>
</root>
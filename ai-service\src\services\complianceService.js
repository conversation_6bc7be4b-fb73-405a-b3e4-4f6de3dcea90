const OpenAI = require('openai');
const winston = require('winston');
const { v4: uuidv4 } = require('uuid');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'compliance-service' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: '/app/logs/compliance.log' })
  ]
});

class ComplianceService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }

  /**
   * Analisar conformidade LGPD de uma transcrição
   */
  async analyzeLGPDCompliance(transcription, consentData, recordingMetadata = {}) {
    const sessionId = uuidv4();
    const startTime = Date.now();

    try {
      logger.info(`Iniciando análise LGPD [${sessionId}]`, { 
        transcriptionLength: transcription.length,
        consentData: consentData ? 'presente' : 'ausente'
      });

      const prompt = this.buildCompliancePrompt(transcription, consentData, recordingMetadata);

      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: `Você é um especialista em LGPD (Lei Geral de Proteção de Dados) e direito cartorial brasileiro. 
            Analise sempre com rigor técnico e legal, considerando:
            - Artigos 7º, 8º e 9º da LGPD (bases legais)
            - Artigos 14º a 18º (direitos do titular)
            - Artigos 46º a 49º (tratamento de dados sensíveis)
            - Resolução CNJ nº 335/2020 (cartórios)
            - Lei 8.935/94 (Lei dos Cartórios)
            
            Retorne sempre um JSON estruturado com análise detalhada.`
          },
          {
            role: "user", 
            content: prompt
          }
        ],
        response_format: { type: "json_object" },
        temperature: 0.1,
        max_tokens: 2000
      });

      const analysis = JSON.parse(response.choices[0].message.content);
      
      const result = {
        sessionId,
        analysis,
        complianceScore: this.calculateComplianceScore(analysis),
        riskLevel: this.determineRiskLevel(analysis),
        recommendations: this.generateRecommendations(analysis),
        processingTime: Date.now() - startTime,
        metadata: {
          model: "gpt-4",
          transcriptionLength: transcription.length,
          analysisDate: new Date().toISOString()
        }
      };

      logger.info(`Análise LGPD concluída [${sessionId}]`, {
        complianceScore: result.complianceScore,
        riskLevel: result.riskLevel,
        processingTime: result.processingTime
      });

      return result;

    } catch (error) {
      logger.error(`Erro na análise LGPD [${sessionId}]:`, error);
      throw new Error(`Falha na análise de compliance: ${error.message}`);
    }
  }

  /**
   * Construir prompt para análise de compliance
   */
  buildCompliancePrompt(transcription, consentData, metadata) {
    return `
    ANÁLISE DE CONFORMIDADE LGPD - CARTÓRIO

    TRANSCRIÇÃO DA GRAVAÇÃO:
    "${transcription}"

    DADOS DE CONSENTIMENTO:
    ${consentData ? `
    - Nome: ${consentData.name || 'Não informado'}
    - Finalidade: ${consentData.purpose || 'Não especificada'}
    - Data do consentimento: ${consentData.timestamp || 'Não registrada'}
    - IP: ${consentData.ip || 'Não registrado'}
    - Consentimento explícito: ${consentData.explicit_consent ? 'Sim' : 'Não'}
    ` : 'CONSENTIMENTO NÃO REGISTRADO'}

    METADADOS DA GRAVAÇÃO:
    - Data: ${metadata.created_at || 'Não informada'}
    - Usuário: ${metadata.user_name || 'Não informado'}
    - Tipo: ${metadata.recording_type || 'Não especificado'}

    ANALISE OS SEGUINTES ASPECTOS:

    1. DADOS PESSOAIS IDENTIFICADOS:
       - Dados pessoais comuns (nome, CPF, RG, endereço, telefone)
       - Dados pessoais sensíveis (origem racial, convicções religiosas, dados de saúde, etc.)
       - Dados de menores de idade

    2. BASES LEGAIS APLICÁVEIS:
       - Art. 7º LGPD (consentimento, cumprimento de obrigação legal, etc.)
       - Art. 8º LGPD (proteção da vida, tutela da saúde)
       - Art. 9º LGPD (dados sensíveis)

    3. CONFORMIDADE COM FINALIDADE:
       - A gravação está adequada à finalidade declarada?
       - Há tratamento excessivo de dados?
       - Princípio da minimização respeitado?

    4. DIREITOS DO TITULAR:
       - Informação clara sobre o tratamento?
       - Possibilidade de revogação do consentimento?
       - Acesso aos dados garantido?

    5. RISCOS IDENTIFICADOS:
       - Vazamento de dados sensíveis
       - Tratamento inadequado
       - Falta de base legal
       - Não conformidade com finalidade

    6. RECOMENDAÇÕES DE ADEQUAÇÃO:
       - Ações corretivas necessárias
       - Melhorias no processo
       - Documentação adicional

    RETORNE UM JSON COM A SEGUINTE ESTRUTURA:
    {
      "dados_identificados": {
        "pessoais_comuns": ["lista de dados encontrados"],
        "pessoais_sensiveis": ["lista de dados sensíveis"],
        "menores": boolean
      },
      "bases_legais": {
        "aplicaveis": ["lista de bases legais"],
        "principal": "base legal principal",
        "justificativa": "explicação"
      },
      "conformidade_finalidade": {
        "adequada": boolean,
        "excessiva": boolean,
        "justificativa": "explicação"
      },
      "direitos_titular": {
        "informacao_clara": boolean,
        "revogacao_possivel": boolean,
        "acesso_garantido": boolean
      },
      "riscos": [
        {
          "tipo": "tipo do risco",
          "severidade": "baixa|media|alta",
          "descricao": "descrição do risco"
        }
      ],
      "recomendacoes": [
        {
          "prioridade": "baixa|media|alta",
          "acao": "ação recomendada",
          "prazo": "prazo sugerido"
        }
      ],
      "score_geral": number // 0-100
    }`;
  }

  /**
   * Calcular score de compliance
   */
  calculateComplianceScore(analysis) {
    if (!analysis || !analysis.score_geral) {
      return 0;
    }
    return Math.max(0, Math.min(100, analysis.score_geral));
  }

  /**
   * Determinar nível de risco
   */
  determineRiskLevel(analysis) {
    const score = this.calculateComplianceScore(analysis);
    
    if (score >= 80) return 'baixo';
    if (score >= 60) return 'medio';
    if (score >= 40) return 'alto';
    return 'critico';
  }

  /**
   * Gerar recomendações baseadas na análise
   */
  generateRecommendations(analysis) {
    const recommendations = [];

    if (analysis.recomendacoes) {
      recommendations.push(...analysis.recomendacoes);
    }

    // Recomendações adicionais baseadas em padrões
    if (analysis.dados_identificados?.pessoais_sensiveis?.length > 0) {
      recommendations.push({
        prioridade: 'alta',
        acao: 'Revisar tratamento de dados sensíveis e documentar base legal específica',
        prazo: '7 dias'
      });
    }

    if (!analysis.conformidade_finalidade?.adequada) {
      recommendations.push({
        prioridade: 'alta',
        acao: 'Adequar tratamento à finalidade declarada ou atualizar consentimento',
        prazo: '15 dias'
      });
    }

    return recommendations;
  }

  /**
   * Analisar lote de gravações
   */
  async analyzeBatch(recordings) {
    const results = [];
    
    for (const recording of recordings) {
      try {
        const result = await this.analyzeLGPDCompliance(
          recording.transcription,
          recording.consentData,
          recording.metadata
        );
        
        results.push({
          recordingId: recording.id,
          success: true,
          result
        });
      } catch (error) {
        results.push({
          recordingId: recording.id,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Gerar relatório de compliance
   */
  async generateComplianceReport(recordingIds, period = '30d') {
    try {
      const aiDatabase = require('../database/aiDatabase');
      const analyses = await aiDatabase.getComplianceAnalyses(recordingIds, period);

      const report = {
        period,
        totalAnalyses: analyses.length,
        averageScore: analyses.reduce((sum, a) => sum + a.compliance_score, 0) / analyses.length,
        riskDistribution: this.calculateRiskDistribution(analyses),
        topRisks: this.identifyTopRisks(analyses),
        recommendations: this.consolidateRecommendations(analyses),
        trends: this.analyzeTrends(analyses)
      };

      return report;
    } catch (error) {
      throw new Error(`Erro ao gerar relatório: ${error.message}`);
    }
  }

  /**
   * Calcular distribuição de riscos
   */
  calculateRiskDistribution(analyses) {
    const distribution = { baixo: 0, medio: 0, alto: 0, critico: 0 };
    
    analyses.forEach(analysis => {
      distribution[analysis.risk_level]++;
    });

    return distribution;
  }

  /**
   * Identificar principais riscos
   */
  identifyTopRisks(analyses) {
    const riskCounts = {};
    
    analyses.forEach(analysis => {
      if (analysis.analysis?.riscos) {
        analysis.analysis.riscos.forEach(risk => {
          const key = `${risk.tipo}_${risk.severidade}`;
          riskCounts[key] = (riskCounts[key] || 0) + 1;
        });
      }
    });

    return Object.entries(riskCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([risk, count]) => ({ risk, count }));
  }

  /**
   * Consolidar recomendações
   */
  consolidateRecommendations(analyses) {
    const recommendations = new Map();
    
    analyses.forEach(analysis => {
      if (analysis.recommendations) {
        analysis.recommendations.forEach(rec => {
          const key = rec.acao;
          if (recommendations.has(key)) {
            recommendations.get(key).count++;
          } else {
            recommendations.set(key, { ...rec, count: 1 });
          }
        });
      }
    });

    return Array.from(recommendations.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);
  }

  /**
   * Analisar tendências
   */
  analyzeTrends(analyses) {
    // Implementar análise de tendências temporais
    return {
      scoreImprovement: 0,
      riskReduction: 0,
      complianceRate: analyses.filter(a => a.compliance_score >= 80).length / analyses.length
    };
  }
}

module.exports = ComplianceService;

using System;
using System.Diagnostics;
using System.Net.NetworkInformation;
using System.Security.Cryptography;
using System.Text;

namespace CartorioDesktopApp
{
    internal static class Program
    {
        /// <summary>
        /// Ponto de entrada principal para o aplicativo.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // Configurar a aplicação
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.SetHighDpiMode(HighDpiMode.SystemAware);

            // Configurações iniciais
            AppSettings.Initialize();
            
            // Iniciar a janela principal
            Application.Run(new MainForm());
        }
    }

    /// <summary>
    /// Classe estática para gerenciar configurações do aplicativo
    /// </summary>
    internal static class AppSettings
    {
        public static string ApiUrl { get; private set; } = "http://localhost:3000";
        public static string AppVersion { get; } = "1.0.0";
        public static string TempDirectory { get; private set; } = Path.Combine(Path.GetTempPath(), "CartorioApp");
        public static string RecordingsDirectory { get; private set; } = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
            "CartorioApp",
            "Recordings");

        /// <summary>
        /// Inicializa as configurações e diretórios necessários
        /// </summary>
        public static void Initialize()
        {
            // Garantir que os diretórios existam
            Directory.CreateDirectory(TempDirectory);
            Directory.CreateDirectory(RecordingsDirectory);

            // Carregar configurações de arquivo (se existir)
            string configPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "CartorioApp",
                "config.json");

            if (File.Exists(configPath))
            {
                try
                {
                    string json = File.ReadAllText(configPath);
                    var config = Newtonsoft.Json.JsonConvert.DeserializeObject<Dictionary<string, string>>(json);
                    
                    if (config != null && config.ContainsKey("ApiUrl"))
                    {
                        ApiUrl = config["ApiUrl"];
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Erro ao carregar configurações: {ex.Message}");
                    // Usar valores padrão em caso de erro
                }
            }
        }

        /// <summary>
        /// Salva as configurações atuais
        /// </summary>
        public static void SaveSettings()
        {
            string configDir = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "CartorioApp");
            
            Directory.CreateDirectory(configDir);
            
            string configPath = Path.Combine(configDir, "config.json");
            
            var config = new Dictionary<string, string>
            {
                { "ApiUrl", ApiUrl }
            };
            
            string json = Newtonsoft.Json.JsonConvert.SerializeObject(config, Newtonsoft.Json.Formatting.Indented);
            File.WriteAllText(configPath, json);
        }

        /// <summary>
        /// Atualiza a URL da API
        /// </summary>
        public static void UpdateApiUrl(string newUrl)
        {
            ApiUrl = newUrl;
            SaveSettings();
        }
    }

    /// <summary>
    /// Classe de utilidades para segurança e criptografia
    /// </summary>
    internal static class SecurityUtils
    {
        /// <summary>
        /// Calcula o hash SHA-256 de um arquivo
        /// </summary>
        public static string CalculateFileHash(string filePath)
        {
            using (var sha256 = SHA256.Create())
            {
                using (var stream = File.OpenRead(filePath))
                {
                    var hash = sha256.ComputeHash(stream);
                    return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
                }
            }
        }

        /// <summary>
        /// Obtém o endereço MAC do adaptador de rede ativo
        /// </summary>
        public static string GetMacAddress()
        {
            try
            {
                // Obter o adaptador de rede ativo que está conectado à Internet
                NetworkInterface? activeAdapter = NetworkInterface.GetAllNetworkInterfaces()
                    .FirstOrDefault(ni => 
                        ni.OperationalStatus == OperationalStatus.Up && 
                        (ni.NetworkInterfaceType == NetworkInterfaceType.Ethernet || 
                         ni.NetworkInterfaceType == NetworkInterfaceType.Wireless80211));
                
                if (activeAdapter != null)
                {
                    return string.Join("-", (from mac in activeAdapter.GetPhysicalAddress().GetAddressBytes()
                                            select mac.ToString("X2")).ToArray());
                }
                
                return "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// Obtém informações do dispositivo
        /// </summary>
        public static string GetDeviceInfo()
        {
            string os = Environment.OSVersion.ToString();
            string macAddress = GetMacAddress();
            string hostname = Environment.MachineName;
            string processor = Environment.ProcessorCount.ToString() + " núcleos";
            
            return $"OS: {os}, Hostname: {hostname}, CPU: {processor}, MAC: {macAddress}";
        }
    }
}

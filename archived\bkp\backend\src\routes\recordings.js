const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { body, validationResult } = require('express-validator');
const { 
  saveRecording, 
  getRecordingById, 
  getAllRecordings,
  deleteRecording,
  calculateFileHash
} = require('../database');

const router = express.Router();

// Configuração do multer para upload de arquivos
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../storage/temp');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 1024 * 1024 * 500 // 500MB (limite de tamanho)
  },
  fileFilter: (req, file, cb) => {
    // Aceitar apenas arquivos de vídeo
    if (file.mimetype.startsWith('video/')) {
      cb(null, true);
    } else {
      cb(new Error('Apenas arquivos de vídeo são permitidos'));
    }
  }
});

// Validações para upload de gravação
const uploadValidations = [
  body('consent_timestamp').isISO8601().withMessage('Timestamp de consentimento inválido'),
  body('consent_ip').isIP().withMessage('Endereço IP de consentimento inválido'),
  body('consent_user_agent').isString().withMessage('User Agent de consentimento inválido'),
  body('device_info').isString().withMessage('Informações do dispositivo inválidas'),
  body('orientation').isIn(['vertical', 'horizontal']).withMessage('Orientação deve ser vertical ou horizontal')
];

// Rota para listar todas as gravações
router.get('/', async (req, res, next) => {
  try {
    const recordings = await getAllRecordings();
    res.json(recordings);
  } catch (error) {
    next(error);
  }
});

// Rota para buscar uma gravação específica
router.get('/:id', async (req, res, next) => {
  try {
    const recording = await getRecordingById(req.params.id);
    
    if (!recording) {
      return res.status(404).json({ error: 'Gravação não encontrada' });
    }
    
    res.json(recording);
  } catch (error) {
    next(error);
  }
});

// Rota para fazer download de uma gravação
router.get('/:id/download', async (req, res, next) => {
  try {
    const recording = await getRecordingById(req.params.id);
    
    if (!recording) {
      return res.status(404).json({ error: 'Gravação não encontrada' });
    }
    
    // Verificar se o arquivo existe
    if (!fs.existsSync(recording.file_path)) {
      return res.status(404).json({ error: 'Arquivo de gravação não encontrado' });
    }
    
    // Verificar a integridade do arquivo
    const currentHash = await calculateFileHash(recording.file_path);
    if (currentHash !== recording.hash) {
      return res.status(500).json({ 
        error: 'Falha na verificação de integridade',
        message: 'O hash do arquivo não corresponde ao hash original'
      });
    }
    
    // Enviar o arquivo
    res.download(recording.file_path, recording.file_name);
  } catch (error) {
    next(error);
  }
});

// Rota para fazer upload de uma gravação
router.post('/', upload.single('video'), uploadValidations, async (req, res, next) => {
  // Verificar erros de validação
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    // Remover o arquivo temporário em caso de erro
    if (req.file) {
      fs.unlinkSync(req.file.path);
    }
    return res.status(400).json({ errors: errors.array() });
  }
  
  if (!req.file) {
    return res.status(400).json({ error: 'Nenhum arquivo enviado' });
  }
  
  try {
    // Ler o arquivo do disco para o buffer
    const fileBuffer = fs.readFileSync(req.file.path);
    
    // Salvar a gravação no banco de dados
    const recordingData = {
      file_name: req.file.originalname,
      mime_type: req.file.mimetype,
      duration: req.body.duration || null,
      device_info: req.body.device_info,
      orientation: req.body.orientation,
      consent_timestamp: req.body.consent_timestamp,
      consent_ip: req.body.consent_ip,
      consent_user_agent: req.body.consent_user_agent,
      additionalData: req.body.additional_data ? JSON.parse(req.body.additional_data) : {}
    };
    
    const recordingId = await saveRecording(recordingData, fileBuffer);
    
    // Remover o arquivo temporário
    fs.unlinkSync(req.file.path);
    
    res.status(201).json({
      id: recordingId,
      message: 'Gravação enviada com sucesso'
    });
  } catch (error) {
    // Remover o arquivo temporário em caso de erro
    if (req.file) {
      fs.unlinkSync(req.file.path);
    }
    next(error);
  }
});

// Rota para excluir uma gravação (soft delete)
router.delete('/:id', async (req, res, next) => {
  try {
    const deleted = await deleteRecording(req.params.id);
    
    if (!deleted) {
      return res.status(404).json({ error: 'Gravação não encontrada' });
    }
    
    res.json({ message: 'Gravação excluída com sucesso' });
  } catch (error) {
    next(error);
  }
});

module.exports = router;

# 🤖 AI Service - Sistema de Cartório

## Visão Geral
O **AI Service** fornece funcionalidades de inteligência artificial para o sistema de cartório, incluindo transcrição automática, análise de conformidade LGPD, busca semântica e análise de qualidade.

## ⚠️ Status Atual
**ATENÇÃO**: Este serviço está em desenvolvimento e requer configuração adicional para funcionar corretamente.

### Funcionalidades Implementadas:
- ✅ Estrutura base do serviço
- ✅ Integração com OpenAI Whisper
- ✅ Análise de conformidade LGPD
- ✅ Busca semântica
- ✅ Sistema de filas com Redis
- ✅ Análise de qualidade de áudio/vídeo

### Configuração Necessária:

#### 1. **Chave da OpenAI (OBRIGATÓRIA)**
```bash
# Editar arquivo .env
OPENAI_API_KEY=sk-your-actual-openai-api-key-here
```

#### 2. **Redis (OBRIGATÓRIA)**
O serviço depende do Redis para gerenciamento de filas. Certifique-se de que o container Redis está rodando:
```bash
docker-compose up redis -d
```

#### 3. **FFmpeg (AUTOMÁTICO)**
O FFmpeg é instalado automaticamente no container Docker para processamento de áudio/vídeo.

## 🚀 Como Usar

### Desenvolvimento Local:
```bash
cd ai-service
npm install
npm run dev
```

### Produção (Docker):
```bash
docker-compose up ai-service -d
```

### Endpoints Disponíveis:
- `POST /api/transcription/upload` - Upload e transcrição de arquivo
- `POST /api/transcription/url` - Transcrição via URL
- `GET /api/transcription/:id` - Status da transcrição
- `POST /api/compliance/analyze` - Análise LGPD
- `POST /api/semantic-search/query` - Busca semântica
- `POST /api/quality/analyze` - Análise de qualidade
- `GET /health` - Health check

## ⚙️ Configuração Avançada

### Variáveis de Ambiente:
```bash
# Obrigatórias
OPENAI_API_KEY=sk-...
REDIS_HOST=redis
BACKEND_URL=http://backend:3001

# Opcionais
MAX_CONCURRENT_JOBS=3
MAX_FILE_SIZE=104857600
TRANSCRIPTION_TIMEOUT=300000
LOG_LEVEL=info
```

### Limites e Quotas:
- **Tamanho máximo de arquivo**: 100MB
- **Timeout de transcrição**: 5 minutos
- **Jobs simultâneos**: 3 (configurável)
- **Formatos suportados**: MP4, AVI, MOV, MP3, WAV, M4A

## 🔧 Troubleshooting

### Problemas Comuns:

#### 1. **Erro "OpenAI API Key não configurada"**
```bash
# Solução: Configurar chave válida no .env
OPENAI_API_KEY=sk-your-real-key-here
```

#### 2. **Erro de conexão com Redis**
```bash
# Verificar se Redis está rodando
docker-compose ps redis

# Reiniciar Redis se necessário
docker-compose restart redis
```

#### 3. **Erro de FFmpeg**
```bash
# O FFmpeg deve ser instalado automaticamente no Docker
# Se houver erro, verificar logs:
docker-compose logs ai-service
```

#### 4. **Arquivos muito grandes**
```bash
# Aumentar limite no .env
MAX_FILE_SIZE=209715200  # 200MB
```

## 📊 Monitoramento

### Health Check:
```bash
curl http://localhost:3003/health
```

### Logs:
```bash
# Ver logs em tempo real
docker-compose logs -f ai-service

# Logs específicos
docker-compose exec ai-service cat /app/logs/combined.log
```

### Métricas de Fila:
```bash
# Status das filas Redis
docker-compose exec redis redis-cli
> KEYS *queue*
> LLEN transcription
```

## 🔒 Segurança

### Boas Práticas:
1. **Nunca commitar chaves da OpenAI no git**
2. **Usar .env.local para desenvolvimento**
3. **Configurar rate limiting em produção**
4. **Monitorar uso da API OpenAI**
5. **Limpar arquivos temporários regularmente**

### Conformidade LGPD:
O serviço analisa automaticamente:
- Dados pessoais sensíveis
- Adequação à finalidade
- Riscos de privacidade
- Conformidade geral

## 📈 Roadmap

### Próximas Versões:
- [ ] Suporte a modelos locais (Whisper.cpp)
- [ ] OCR com Tesseract
- [ ] Análise de sentimento
- [ ] Clustering automático
- [ ] Dashboard de analytics
- [ ] API rate limiting
- [ ] Backup automático de embeddings

## 💰 Custos Estimados

### OpenAI API (Whisper):
- **Transcrição**: $0.006 por minuto de áudio
- **GPT-4 (análise)**: $0.03 por 1K tokens
- **Embeddings**: $0.0001 por 1K tokens

### Exemplo Mensal (100 gravações de 10min):
- Transcrição: 1000min × $0.006 = $6.00
- Análise: ~$10.00
- Embeddings: ~$1.00
- **Total estimado**: ~$17.00/mês

---

## 🆘 Suporte

Para problemas ou dúvidas:
1. Verificar logs: `docker-compose logs ai-service`
2. Conferir configuração: verificar .env
3. Testar health check: `curl localhost:3003/health`
4. Verificar Redis: `docker-compose ps redis`

**IMPORTANTE**: O serviço não funcionará sem uma chave válida da OpenAI!

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../services/screen_recorder_service.dart';

class RecordingsScreen extends StatefulWidget {
  const RecordingsScreen({super.key});

  @override
  State<RecordingsScreen> createState() => _RecordingsScreenState();
}

class _RecordingsScreenState extends State<RecordingsScreen> {
  List<FileSystemEntity> _recordings = [];
  bool _isLoading = true;
  final ScreenRecorderService _recorderService = ScreenRecorderService();
  
  @override
  void initState() {
    super.initState();
    _loadRecordings();
  }
  
  Future<void> _loadRecordings() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final docsDir = await getApplicationDocumentsDirectory();
      final recordingsDir = Directory('${docsDir.path}/recordings');
      
      if (await recordingsDir.exists()) {
        final files = await recordingsDir.list().toList();
        files.sort((a, b) => b.statSync().modified.compareTo(a.statSync().modified));
        
        setState(() {
          _recordings = files;
          _isLoading = false;
        });
      } else {
        setState(() {
          _recordings = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading recordings: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < (1024 * 1024)) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < (1024 * 1024 * 1024)) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
  
  String _formatDate(DateTime date) {
    final formatter = DateFormat('dd/MM/yyyy HH:mm');
    return formatter.format(date);
  }
  
  Future<void> _uploadRecording(File file) async {
    try {
      final result = await _recorderService.uploadRecording(
        file: file,
        apiUrl: 'http://10.0.2.2:3000', // Você pode obter isso do provider também
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Gravação enviada com sucesso! ID: ${result['id']}'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao enviar gravação: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  Future<void> _deleteRecording(FileSystemEntity file) async {
    final confirmDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Excluir gravação'),
        content: Text('Tem certeza que deseja excluir esta gravação?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancelar'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('Excluir'),
          ),
        ],
      ),
    );
    
    if (confirmDelete == true) {
      try {
        await file.delete();
        _loadRecordings();
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gravação excluída com sucesso!'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao excluir gravação: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Minhas Gravações'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _loadRecordings,
            tooltip: 'Atualizar',
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _recordings.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.videocam_off,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Nenhuma gravação encontrada',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Suas gravações aparecerão aqui',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: _recordings.length,
                  itemBuilder: (context, index) {
                    final file = _recordings[index];
                    final fileStats = file.statSync();
                    final fileName = file.path.split('/').last;
                    final isHorizontal = fileName.contains('horizontal');
                    
                    return Card(
                      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: ListTile(
                        leading: Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            isHorizontal
                                ? Icons.stay_current_landscape
                                : Icons.stay_current_portrait,
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                        title: Text(
                          fileName,
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(_formatDate(fileStats.modified)),
                            Text(_formatFileSize(fileStats.size)),
                          ],
                        ),
                        trailing: PopupMenuButton(
                          itemBuilder: (context) => [
                            PopupMenuItem(
                              value: 'upload',
                              child: Row(
                                children: [
                                  Icon(Icons.cloud_upload),
                                  SizedBox(width: 8),
                                  Text('Enviar'),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete, color: Colors.red),
                                  SizedBox(width: 8),
                                  Text('Excluir', style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          ],
                          onSelected: (value) {
                            if (value == 'upload') {
                              _uploadRecording(File(file.path));
                            } else if (value == 'delete') {
                              _deleteRecording(file);
                            }
                          },
                        ),
                      ),
                    );
                  },
                ),
    );
  }
}

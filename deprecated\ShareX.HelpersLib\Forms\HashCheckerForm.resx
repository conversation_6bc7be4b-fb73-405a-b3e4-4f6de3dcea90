﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="txtFilePath.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 40</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtFilePath.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="txtFilePath.Size" type="System.Drawing.Size, System.Drawing">
    <value>504, 25</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtFilePath.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txtFilePath.Name" xml:space="preserve">
    <value>txtFilePath</value>
  </data>
  <data name="&gt;&gt;txtFilePath.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtFilePath.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtFilePath.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnFilePathBrowse.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnFilePathBrowse.Location" type="System.Drawing.Point, System.Drawing">
    <value>528, 38</value>
  </data>
  <data name="btnFilePathBrowse.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="btnFilePathBrowse.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 28</value>
  </data>
  <data name="btnFilePathBrowse.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnFilePathBrowse.Text" xml:space="preserve">
    <value>Browse...</value>
  </data>
  <data name="&gt;&gt;btnFilePathBrowse.Name" xml:space="preserve">
    <value>btnFilePathBrowse</value>
  </data>
  <data name="&gt;&gt;btnFilePathBrowse.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnFilePathBrowse.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnFilePathBrowse.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="lblHashType.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblHashType.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblHashType.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 160</value>
  </data>
  <data name="lblHashType.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="lblHashType.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 17</value>
  </data>
  <data name="lblHashType.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="lblHashType.Text" xml:space="preserve">
    <value>Hash type:</value>
  </data>
  <data name="&gt;&gt;lblHashType.Name" xml:space="preserve">
    <value>lblHashType</value>
  </data>
  <data name="&gt;&gt;lblHashType.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblHashType.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblHashType.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="lblResult.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblResult.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblResult.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 232</value>
  </data>
  <data name="lblResult.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="lblResult.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 17</value>
  </data>
  <data name="lblResult.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="lblResult.Text" xml:space="preserve">
    <value>Result:</value>
  </data>
  <data name="&gt;&gt;lblResult.Name" xml:space="preserve">
    <value>lblResult</value>
  </data>
  <data name="&gt;&gt;lblResult.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblResult.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblResult.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="lblTarget.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblTarget.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblTarget.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 288</value>
  </data>
  <data name="lblTarget.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="lblTarget.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 17</value>
  </data>
  <data name="lblTarget.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="lblTarget.Text" xml:space="preserve">
    <value>Target:</value>
  </data>
  <data name="&gt;&gt;lblTarget.Name" xml:space="preserve">
    <value>lblTarget</value>
  </data>
  <data name="&gt;&gt;lblTarget.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblTarget.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblTarget.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="btnStartHashCheck.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnStartHashCheck.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 192</value>
  </data>
  <data name="btnStartHashCheck.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="btnStartHashCheck.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 32</value>
  </data>
  <data name="btnStartHashCheck.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="btnStartHashCheck.Text" xml:space="preserve">
    <value>Check</value>
  </data>
  <data name="&gt;&gt;btnStartHashCheck.Name" xml:space="preserve">
    <value>btnStartHashCheck</value>
  </data>
  <data name="&gt;&gt;btnStartHashCheck.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnStartHashCheck.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnStartHashCheck.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="cbHashType.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 156</value>
  </data>
  <data name="cbHashType.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="cbHashType.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 25</value>
  </data>
  <data name="cbHashType.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;cbHashType.Name" xml:space="preserve">
    <value>cbHashType</value>
  </data>
  <data name="&gt;&gt;cbHashType.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbHashType.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbHashType.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="txtResult.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 256</value>
  </data>
  <data name="txtResult.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="txtResult.Size" type="System.Drawing.Size, System.Drawing">
    <value>632, 25</value>
  </data>
  <data name="txtResult.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;txtResult.Name" xml:space="preserve">
    <value>txtResult</value>
  </data>
  <data name="&gt;&gt;txtResult.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtResult.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtResult.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="txtTarget.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 312</value>
  </data>
  <data name="txtTarget.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="txtTarget.Size" type="System.Drawing.Size, System.Drawing">
    <value>632, 25</value>
  </data>
  <data name="txtTarget.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;txtTarget.Name" xml:space="preserve">
    <value>txtTarget</value>
  </data>
  <data name="&gt;&gt;txtTarget.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtTarget.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtTarget.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="lblFilePath.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblFilePath.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblFilePath.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 16</value>
  </data>
  <data name="lblFilePath.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="lblFilePath.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 17</value>
  </data>
  <data name="lblFilePath.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblFilePath.Text" xml:space="preserve">
    <value>File path:</value>
  </data>
  <data name="&gt;&gt;lblFilePath.Name" xml:space="preserve">
    <value>lblFilePath</value>
  </data>
  <data name="&gt;&gt;lblFilePath.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblFilePath.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblFilePath.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="lblFilePath2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblFilePath2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblFilePath2.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 96</value>
  </data>
  <data name="lblFilePath2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 0, 4, 0</value>
  </data>
  <data name="lblFilePath2.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 17</value>
  </data>
  <data name="lblFilePath2.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="lblFilePath2.Text" xml:space="preserve">
    <value>File path:</value>
  </data>
  <data name="&gt;&gt;lblFilePath2.Name" xml:space="preserve">
    <value>lblFilePath2</value>
  </data>
  <data name="&gt;&gt;lblFilePath2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblFilePath2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblFilePath2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtFilePath2.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 120</value>
  </data>
  <data name="txtFilePath2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="txtFilePath2.Size" type="System.Drawing.Size, System.Drawing">
    <value>504, 25</value>
  </data>
  <data name="txtFilePath2.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;txtFilePath2.Name" xml:space="preserve">
    <value>txtFilePath2</value>
  </data>
  <data name="&gt;&gt;txtFilePath2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtFilePath2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtFilePath2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnFilePathBrowse2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnFilePathBrowse2.Location" type="System.Drawing.Point, System.Drawing">
    <value>528, 118</value>
  </data>
  <data name="btnFilePathBrowse2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="btnFilePathBrowse2.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 28</value>
  </data>
  <data name="btnFilePathBrowse2.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="btnFilePathBrowse2.Text" xml:space="preserve">
    <value>Browse...</value>
  </data>
  <data name="&gt;&gt;btnFilePathBrowse2.Name" xml:space="preserve">
    <value>btnFilePathBrowse2</value>
  </data>
  <data name="&gt;&gt;btnFilePathBrowse2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnFilePathBrowse2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnFilePathBrowse2.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="cbCompareTwoFiles.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbCompareTwoFiles.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="cbCompareTwoFiles.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 72</value>
  </data>
  <data name="cbCompareTwoFiles.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="cbCompareTwoFiles.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 21</value>
  </data>
  <data name="cbCompareTwoFiles.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="cbCompareTwoFiles.Text" xml:space="preserve">
    <value>Compare two files</value>
  </data>
  <data name="&gt;&gt;cbCompareTwoFiles.Name" xml:space="preserve">
    <value>cbCompareTwoFiles</value>
  </data>
  <data name="&gt;&gt;cbCompareTwoFiles.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbCompareTwoFiles.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbCompareTwoFiles.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="pbProgress.Font" type="System.Drawing.Font, System.Drawing">
    <value>Segoe UI, 9.75pt, style=Bold</value>
  </data>
  <data name="pbProgress.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 192</value>
  </data>
  <data name="pbProgress.Size" type="System.Drawing.Size, System.Drawing">
    <value>480, 32</value>
  </data>
  <data name="pbProgress.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="pbProgress.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;pbProgress.Name" xml:space="preserve">
    <value>pbProgress</value>
  </data>
  <data name="&gt;&gt;pbProgress.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.BlackStyleProgressBar, ShareX.HelpersLib, Version=17.0.1.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;pbProgress.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pbProgress.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>7, 17</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>665, 353</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>Segoe UI, 9.75pt</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="$this.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 4, 4, 4</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Hash checker</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>HashCheckerForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
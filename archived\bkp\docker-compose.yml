version: '3.8'

services:
  backend:
    build: 
      context: ./backend
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app
      - ./storage:/app/storage
    environment:
      - NODE_ENV=production
      - DB_PATH=/app/storage/database.sqlite
      - ENCRYPTION_KEY=change_this_to_a_secure_key_in_production
    networks:
      - app-network
    restart: unless-stopped

  latex-service:
    build:
      context: ./latex-service
    volumes:
      - ./latex-service:/app
      - ./storage:/app/storage
    environment:
      - BACKEND_URL=http://backend:3000
    networks:
      - app-network
    depends_on:
      - backend
      
  web-app:
    build:
      context: ./web-app
    ports:
      - "80:80"
    networks:
      - app-network
    depends_on:
      - backend
    environment:
      - REACT_APP_API_URL=http://localhost:3000

networks:
  app-network:
    driver: bridge

volumes:
  storage:

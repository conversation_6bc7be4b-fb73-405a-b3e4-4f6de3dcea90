# Configurações do Serviço de IA
NODE_ENV=production
PORT=3003

# URLs dos serviços
BACKEND_URL=http://backend:3001

# Configurações do Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Chave da API OpenAI (OBRIGATÓRIA)
OPENAI_API_KEY=your_openai_api_key_here

# Configurações de logging
LOG_LEVEL=info

# Configurações de processamento
MAX_CONCURRENT_JOBS=5
QUEUE_CLEANUP_INTERVAL=3600000

# Configurações de arquivos
MAX_FILE_SIZE=104857600
TEMP_DIR=/app/storage/temp
AI_DATA_DIR=/app/storage/ai-data

# Configurações de timeout
TRANSCRIPTION_TIMEOUT=300000
ANALYSIS_TIMEOUT=180000
SEARCH_TIMEOUT=30000

const sqlite3 = require('sqlite3').verbose();
const crypto = require('crypto');
const path = require('path');
const fs = require('fs');

// Certificar-se de que o diretório de armazenamento existe
const storageDir = path.join(__dirname, '..', 'storage');
if (!fs.existsSync(storageDir)) {
  fs.mkdirSync(storageDir, { recursive: true });
}

const DB_PATH = process.env.DB_PATH || path.join(storageDir, 'database.sqlite');
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default_encryption_key_change_in_production';

let db;

/**
 * Inicializa o banco de dados SQLite com tabelas necessárias
 */
async function initializeDatabase() {
  return new Promise((resolve, reject) => {
    db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('Erro ao conectar ao banco de dados:', err.message);
        reject(err);
        return;
      }
      
      console.log('Conectado ao banco de dados SQLite');
      
      // Habilitar chaves estrangeiras
      db.run('PRAGMA foreign_keys = ON', (pragmaErr) => {
        if (pragmaErr) {
          console.error('Erro ao habilitar chaves estrangeiras:', pragmaErr.message);
          reject(pragmaErr);
          return;
        }
        
        // Criar tabela de gravações
        db.run(`CREATE TABLE IF NOT EXISTS recordings (
          id TEXT PRIMARY KEY,
          file_name TEXT NOT NULL,
          file_path TEXT NOT NULL,
          mime_type TEXT NOT NULL,
          file_size INTEGER NOT NULL,
          duration INTEGER,
          hash TEXT NOT NULL,
          device_info TEXT,
          orientation TEXT,
          created_at TEXT DEFAULT CURRENT_TIMESTAMP,
          encrypted_data BLOB,
          consent_timestamp TEXT,
          consent_ip TEXT,
          consent_user_agent TEXT,
          deleted INTEGER DEFAULT 0
        )`, (createErr) => {
          if (createErr) {
            console.error('Erro ao criar tabela de gravações:', createErr.message);
            reject(createErr);
            return;
          }
          
          // Criar tabela de relatórios
          db.run(`CREATE TABLE IF NOT EXISTS reports (
            id TEXT PRIMARY KEY,
            recording_id TEXT NOT NULL,
            pdf_path TEXT NOT NULL,
            generated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            notarized INTEGER DEFAULT 0,
            notary_info TEXT,
            FOREIGN KEY (recording_id) REFERENCES recordings(id)
          )`, (createReportsErr) => {
            if (createReportsErr) {
              console.error('Erro ao criar tabela de relatórios:', createReportsErr.message);
              reject(createReportsErr);
              return;
            }
            
            resolve();
          });
        });
      });
    });
  });
}

/**
 * Criptografa dados usando AES-256
 * @param {Buffer|string} data - Dados a serem criptografados
 * @returns {Buffer} - Dados criptografados
 */
function encryptData(data) {
  // Derivar chave usando PBKDF2
  const salt = crypto.randomBytes(16);
  const key = crypto.pbkdf2Sync(ENCRYPTION_KEY, salt, 100000, 32, 'sha256');
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
  let encrypted = cipher.update(data);
  encrypted = Buffer.concat([encrypted, cipher.final()]);
  
  // Retornar salt + iv + dados criptografados
  return Buffer.concat([salt, iv, encrypted]);
}

/**
 * Descriptografa dados usando AES-256
 * @param {Buffer} encryptedData - Dados criptografados
 * @returns {Buffer} - Dados descriptografados
 */
function decryptData(encryptedData) {
  const salt = encryptedData.slice(0, 16);
  const iv = encryptedData.slice(16, 32);
  const encrypted = encryptedData.slice(32);
  
  const key = crypto.pbkdf2Sync(ENCRYPTION_KEY, salt, 100000, 32, 'sha256');
  const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
  
  let decrypted = decipher.update(encrypted);
  decrypted = Buffer.concat([decrypted, decipher.final()]);
  
  return decrypted;
}

/**
 * Calcula o hash SHA-256 de um arquivo
 * @param {string} filePath - Caminho do arquivo
 * @returns {Promise<string>} - Hash SHA-256 em formato hexadecimal
 */
function calculateFileHash(filePath) {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('sha256');
    const stream = fs.createReadStream(filePath);
    
    stream.on('error', err => reject(err));
    stream.on('data', chunk => hash.update(chunk));
    stream.on('end', () => resolve(hash.digest('hex')));
  });
}

/**
 * Salva uma nova gravação no banco de dados
 * @param {Object} recordingData - Dados da gravação
 * @param {Buffer} fileBuffer - Buffer do arquivo de vídeo
 * @returns {Promise<string>} - ID da gravação inserida
 */
async function saveRecording(recordingData, fileBuffer) {
  const id = crypto.randomUUID();
  const filePath = path.join(storageDir, `${id}.mp4`);
  
  // Salvar o arquivo
  await fs.promises.writeFile(filePath, fileBuffer);
  
  // Calcular hash do arquivo
  const hash = await calculateFileHash(filePath);
  
  // Criptografar os metadados sensíveis
  const encryptedData = encryptData(JSON.stringify({
    consentInfo: {
      timestamp: recordingData.consent_timestamp,
      ip: recordingData.consent_ip,
      userAgent: recordingData.consent_user_agent
    },
    deviceInfo: recordingData.device_info,
    additionalData: recordingData.additionalData || {}
  }));
  
  return new Promise((resolve, reject) => {
    db.run(`
      INSERT INTO recordings (
        id, file_name, file_path, mime_type, file_size, 
        duration, hash, device_info, orientation, 
        consent_timestamp, consent_ip, consent_user_agent, encrypted_data
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      id,
      recordingData.file_name,
      filePath,
      recordingData.mime_type,
      fileBuffer.length,
      recordingData.duration,
      hash,
      recordingData.device_info,
      recordingData.orientation,
      recordingData.consent_timestamp,
      recordingData.consent_ip,
      recordingData.consent_user_agent,
      encryptedData
    ], function(err) {
      if (err) {
        console.error('Erro ao salvar gravação:', err.message);
        reject(err);
        return;
      }
      resolve(id);
    });
  });
}

/**
 * Busca uma gravação por ID
 * @param {string} id - ID da gravação
 * @returns {Promise<Object>} - Dados da gravação
 */
async function getRecordingById(id) {
  return new Promise((resolve, reject) => {
    db.get('SELECT * FROM recordings WHERE id = ? AND deleted = 0', [id], (err, row) => {
      if (err) {
        console.error('Erro ao buscar gravação:', err.message);
        reject(err);
        return;
      }
      
      if (!row) {
        resolve(null);
        return;
      }
      
      // Descriptografar dados sensíveis
      if (row.encrypted_data) {
        try {
          const decryptedData = JSON.parse(decryptData(row.encrypted_data).toString());
          row.decrypted_data = decryptedData;
          delete row.encrypted_data; // Não retornar os dados criptografados
        } catch (decryptErr) {
          console.error('Erro ao descriptografar dados:', decryptErr);
        }
      }
      
      resolve(row);
    });
  });
}

/**
 * Lista todas as gravações não excluídas
 * @returns {Promise<Array>} - Lista de gravações
 */
async function getAllRecordings() {
  return new Promise((resolve, reject) => {
    db.all('SELECT id, file_name, mime_type, file_size, duration, hash, device_info, orientation, created_at FROM recordings WHERE deleted = 0', (err, rows) => {
      if (err) {
        console.error('Erro ao listar gravações:', err.message);
        reject(err);
        return;
      }
      resolve(rows);
    });
  });
}

/**
 * Marca uma gravação como excluída (soft delete)
 * @param {string} id - ID da gravação
 * @returns {Promise<boolean>} - Sucesso da operação
 */
async function deleteRecording(id) {
  return new Promise((resolve, reject) => {
    db.run('UPDATE recordings SET deleted = 1 WHERE id = ?', [id], function(err) {
      if (err) {
        console.error('Erro ao excluir gravação:', err.message);
        reject(err);
        return;
      }
      resolve(this.changes > 0);
    });
  });
}

/**
 * Salva um relatório gerado no banco de dados
 * @param {string} recordingId - ID da gravação
 * @param {string} pdfPath - Caminho do arquivo PDF
 * @returns {Promise<string>} - ID do relatório
 */
async function saveReport(recordingId, pdfPath) {
  const id = crypto.randomUUID();
  
  return new Promise((resolve, reject) => {
    db.run(`
      INSERT INTO reports (id, recording_id, pdf_path)
      VALUES (?, ?, ?)
    `, [id, recordingId, pdfPath], function(err) {
      if (err) {
        console.error('Erro ao salvar relatório:', err.message);
        reject(err);
        return;
      }
      resolve(id);
    });
  });
}

/**
 * Atualiza informações de autenticação em cartório
 * @param {string} reportId - ID do relatório
 * @param {Object} notaryInfo - Informações do cartório
 * @returns {Promise<boolean>} - Sucesso da operação
 */
async function updateNotaryInfo(reportId, notaryInfo) {
  return new Promise((resolve, reject) => {
    db.run(`
      UPDATE reports
      SET notarized = 1, notary_info = ?
      WHERE id = ?
    `, [JSON.stringify(notaryInfo), reportId], function(err) {
      if (err) {
        console.error('Erro ao atualizar informações de cartório:', err.message);
        reject(err);
        return;
      }
      resolve(this.changes > 0);
    });
  });
}

module.exports = {
  initializeDatabase,
  saveRecording,
  getRecordingById,
  getAllRecordings,
  deleteRecording,
  saveReport,
  updateNotaryInfo,
  calculateFileHash,
  encryptData,
  decryptData
};

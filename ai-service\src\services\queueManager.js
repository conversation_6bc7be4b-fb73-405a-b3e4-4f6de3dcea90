const Queue = require('bull');
const redis = require('redis');
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'queue-manager' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: '/app/logs/queue.log' })
  ]
});

class QueueManager {
  constructor() {
    this.redisConfig = {
      host: process.env.REDIS_HOST || 'redis',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      db: process.env.REDIS_DB || 0
    };
    
    this.queues = {};
    this.redisClient = null;
  }

  /**
   * Inicializar gerenciador de filas
   */
  async initialize() {
    try {
      logger.info('Inicializando conexão com Redis...', this.redisConfig);
      
      // Conectar ao Redis com retry
      this.redisClient = redis.createClient(this.redisConfig);
      
      this.redisClient.on('error', (err) => {
        logger.error('Erro na conexão Redis:', err);
      });

      this.redisClient.on('connect', () => {
        logger.info('Conexão Redis estabelecida');
      });

      await this.redisClient.connect();

      // Testar conexão
      await this.redisClient.ping();
      logger.info('Redis ping bem-sucedido');

      // Criar filas
      this.queues = {
        transcription: new Queue('transcription', { redis: this.redisConfig }),
        compliance: new Queue('compliance', { redis: this.redisConfig }),
        quality: new Queue('quality', { redis: this.redisConfig }),
        semantic: new Queue('semantic', { redis: this.redisConfig }),
        batch: new Queue('batch', { redis: this.redisConfig })
      };

      // Configurar processadores
      this.setupProcessors();

      logger.info('Gerenciador de filas inicializado com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar gerenciador de filas:', error);
      logger.warn('Continuando sem Redis. Algumas funcionalidades de IA podem não funcionar.');
      // Não falhar se Redis não estiver disponível
    }
  }

  /**
   * Configurar processadores das filas
   */
  setupProcessors() {
    // Processador de transcrição
    this.queues.transcription.process(async (job) => {
      const { recordingId, options } = job.data;
      logger.info(`Processando transcrição: ${recordingId}`);

      try {
        const TranscriptionService = require('./transcriptionService');
        const transcriptionService = new TranscriptionService();
        
        // Buscar dados da gravação
        const axios = require('axios');
        const backendUrl = process.env.BACKEND_URL || 'http://backend:3001';
        const response = await axios.get(`${backendUrl}/api/recordings/${recordingId}`);
        const recording = response.data;

        if (!recording) {
          throw new Error('Gravação não encontrada');
        }

        // Transcrever
        const result = await transcriptionService.transcribeRecording(recording.file_path, options);
        
        // Salvar no banco
        const aiDatabase = require('../database/aiDatabase');
        await aiDatabase.saveTranscription(recordingId, result);

        logger.info(`Transcrição concluída: ${recordingId}`);
        return result;

      } catch (error) {
        logger.error(`Erro na transcrição ${recordingId}:`, error);
        throw error;
      }
    });

    // Processador de compliance
    this.queues.compliance.process(async (job) => {
      const { recordingId } = job.data;
      logger.info(`Processando compliance: ${recordingId}`);

      try {
        const ComplianceService = require('./complianceService');
        const complianceService = new ComplianceService();
        
        // Buscar transcrição
        const aiDatabase = require('../database/aiDatabase');
        const transcription = await aiDatabase.getTranscription(recordingId);

        if (!transcription) {
          throw new Error('Transcrição não encontrada');
        }

        // Buscar dados da gravação
        const axios = require('axios');
        const backendUrl = process.env.BACKEND_URL || 'http://backend:3001';
        const response = await axios.get(`${backendUrl}/api/recordings/${recordingId}`);
        const recording = response.data;

        // Analisar compliance
        const result = await complianceService.analyzeLGPDCompliance(
          transcription.text,
          {
            name: recording.user_name,
            purpose: recording.purpose,
            timestamp: recording.consent_timestamp,
            ip: recording.consent_ip,
            explicit_consent: recording.consent_given
          },
          {
            created_at: recording.created_at,
            user_name: recording.user_name,
            recording_type: recording.additional_data?.type
          }
        );

        // Salvar no banco
        await aiDatabase.saveComplianceAnalysis(recordingId, result);

        logger.info(`Análise de compliance concluída: ${recordingId}`);
        return result;

      } catch (error) {
        logger.error(`Erro na análise de compliance ${recordingId}:`, error);
        throw error;
      }
    });

    // Processador de qualidade
    this.queues.quality.process(async (job) => {
      const { recordingId } = job.data;
      logger.info(`Processando qualidade: ${recordingId}`);

      try {
        const QualityAnalysisService = require('./qualityAnalysisService');
        const qualityService = new QualityAnalysisService();
        
        // Buscar dados
        const aiDatabase = require('../database/aiDatabase');
        const axios = require('axios');
        const backendUrl = process.env.BACKEND_URL || 'http://backend:3001';
        
        const [transcription, recordingResponse] = await Promise.all([
          aiDatabase.getTranscription(recordingId),
          axios.get(`${backendUrl}/api/recordings/${recordingId}`)
        ]);

        const recording = recordingResponse.data;

        // Analisar qualidade
        const result = await qualityService.analyzeRecordingQuality(
          recording.file_path,
          transcription?.text,
          {
            filename: recording.filename,
            created_at: recording.created_at,
            user_name: recording.user_name,
            file_size: recording.file_size,
            duration: recording.duration
          }
        );

        // Salvar no banco
        await aiDatabase.saveQualityAnalysis(recordingId, result);

        logger.info(`Análise de qualidade concluída: ${recordingId}`);
        return result;

      } catch (error) {
        logger.error(`Erro na análise de qualidade ${recordingId}:`, error);
        throw error;
      }
    });

    // Processador semântico
    this.queues.semantic.process(async (job) => {
      const { recordingId } = job.data;
      logger.info(`Processando semântica: ${recordingId}`);

      try {
        const SemanticSearchService = require('./semanticSearchService');
        const semanticService = new SemanticSearchService();
        
        // Buscar transcrição
        const aiDatabase = require('../database/aiDatabase');
        const transcription = await aiDatabase.getTranscription(recordingId);

        if (!transcription) {
          throw new Error('Transcrição não encontrada');
        }

        // Processar para busca semântica
        const result = await semanticService.processRecordingForSearch(recordingId, transcription.text);

        logger.info(`Processamento semântico concluído: ${recordingId}`);
        return result;

      } catch (error) {
        logger.error(`Erro no processamento semântico ${recordingId}:`, error);
        throw error;
      }
    });

    // Configurar eventos
    Object.values(this.queues).forEach(queue => {
      queue.on('completed', (job, result) => {
        logger.info(`Job ${job.id} concluído`, { queue: queue.name, result });
      });

      queue.on('failed', (job, err) => {
        logger.error(`Job ${job.id} falhou`, { queue: queue.name, error: err.message });
      });

      queue.on('stalled', (job) => {
        logger.warn(`Job ${job.id} travado`, { queue: queue.name });
      });
    });
  }

  /**
   * Adicionar job de transcrição
   */
  async addTranscriptionJob(recordingId, options = {}) {
    const job = await this.queues.transcription.add('transcribe', {
      recordingId,
      options
    }, {
      attempts: 3,
      backoff: 'exponential',
      delay: 1000
    });

    logger.info(`Job de transcrição adicionado: ${job.id}`, { recordingId });
    return job;
  }

  /**
   * Adicionar job de compliance
   */
  async addComplianceJob(recordingId) {
    const job = await this.queues.compliance.add('analyze', {
      recordingId
    }, {
      attempts: 2,
      backoff: 'exponential',
      delay: 2000
    });

    logger.info(`Job de compliance adicionado: ${job.id}`, { recordingId });
    return job;
  }

  /**
   * Adicionar job de qualidade
   */
  async addQualityAnalysisJob(recordingId) {
    const job = await this.queues.quality.add('analyze', {
      recordingId
    }, {
      attempts: 2,
      backoff: 'exponential',
      delay: 1500
    });

    logger.info(`Job de qualidade adicionado: ${job.id}`, { recordingId });
    return job;
  }

  /**
   * Adicionar job de processamento semântico
   */
  async addSemanticProcessingJob(recordingId) {
    const job = await this.queues.semantic.add('process', {
      recordingId
    }, {
      attempts: 2,
      backoff: 'exponential',
      delay: 1000
    });

    logger.info(`Job semântico adicionado: ${job.id}`, { recordingId });
    return job;
  }

  /**
   * Processar gravação completa (todos os serviços)
   */
  async processRecordingComplete(recordingId, options = {}) {
    try {
      // Transcrição primeiro
      const transcriptionJob = await this.addTranscriptionJob(recordingId, options);
      
      // Aguardar transcrição
      await transcriptionJob.finished();

      // Processar em paralelo
      const [complianceJob, qualityJob, semanticJob] = await Promise.all([
        this.addComplianceJob(recordingId),
        this.addQualityAnalysisJob(recordingId),
        this.addSemanticProcessingJob(recordingId)
      ]);

      return {
        transcriptionJob: transcriptionJob.id,
        complianceJob: complianceJob.id,
        qualityJob: qualityJob.id,
        semanticJob: semanticJob.id
      };

    } catch (error) {
      logger.error(`Erro no processamento completo ${recordingId}:`, error);
      throw error;
    }
  }

  /**
   * Obter status de job
   */
  async getJobStatus(jobId) {
    for (const [queueName, queue] of Object.entries(this.queues)) {
      try {
        const job = await queue.getJob(jobId);
        if (job) {
          return {
            id: job.id,
            queue: queueName,
            status: await job.getState(),
            progress: job.progress(),
            data: job.data,
            result: job.returnvalue,
            error: job.failedReason,
            createdAt: new Date(job.timestamp),
            processedAt: job.processedOn ? new Date(job.processedOn) : null,
            finishedAt: job.finishedOn ? new Date(job.finishedOn) : null
          };
        }
      } catch (error) {
        // Continuar procurando em outras filas
      }
    }
    
    return null;
  }

  /**
   * Obter estatísticas das filas
   */
  async getQueueStats() {
    const stats = {};
    
    for (const [name, queue] of Object.entries(this.queues)) {
      const [waiting, active, completed, failed, delayed] = await Promise.all([
        queue.getWaiting(),
        queue.getActive(),
        queue.getCompleted(),
        queue.getFailed(),
        queue.getDelayed()
      ]);

      stats[name] = {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length
      };
    }

    return stats;
  }

  /**
   * Limpar filas
   */
  async cleanQueues() {
    for (const queue of Object.values(this.queues)) {
      await queue.clean(24 * 60 * 60 * 1000, 'completed'); // Limpar jobs concluídos há mais de 24h
      await queue.clean(7 * 24 * 60 * 60 * 1000, 'failed'); // Limpar jobs falhados há mais de 7 dias
    }
    
    logger.info('Filas limpas');
  }

  /**
   * Fechar conexões
   */
  async close() {
    for (const queue of Object.values(this.queues)) {
      await queue.close();
    }
    
    if (this.redisClient) {
      await this.redisClient.quit();
    }
    
    logger.info('Gerenciador de filas fechado');
  }
}

// Singleton instance
let queueManagerInstance = null;

module.exports = {
  initialize: async () => {
    if (!queueManagerInstance) {
      queueManagerInstance = new QueueManager();
      await queueManagerInstance.initialize();
    }
    return queueManagerInstance;
  },
  getInstance: () => queueManagerInstance,
  
  // Métodos de conveniência
  addTranscriptionJob: (recordingId, options) => queueManagerInstance?.addTranscriptionJob(recordingId, options),
  addComplianceJob: (recordingId) => queueManagerInstance?.addComplianceJob(recordingId),
  addQualityAnalysisJob: (recordingId) => queueManagerInstance?.addQualityAnalysisJob(recordingId),
  addSemanticProcessingJob: (recordingId) => queueManagerInstance?.addSemanticProcessingJob(recordingId),
  processRecordingComplete: (recordingId, options) => queueManagerInstance?.processRecordingComplete(recordingId, options),
  getJobStatus: (jobId) => queueManagerInstance?.getJobStatus(jobId),
  getQueueStats: () => queueManagerInstance?.getQueueStats(),
  cleanQueues: () => queueManagerInstance?.cleanQueues()
};

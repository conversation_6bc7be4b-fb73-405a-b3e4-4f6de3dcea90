import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/user.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class AdminPanel extends StatefulWidget {
  const AdminPanel({super.key});

  @override
  State<AdminPanel> createState() => _AdminPanelState();
}

class _AdminPanelState extends State<AdminPanel> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<User> _users = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _fetchUsers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _fetchUsers() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final appProvider = context.read<AppProvider>();
      final response = await http.get(
        Uri.parse('${appProvider.apiUrl}/api/admin/users'),
        headers: {'Authorization': 'Bearer ${appProvider.authToken}'},
      );

      if (response.statusCode == 200) {
        final List<dynamic> usersData = json.decode(response.body);
        setState(() {
          _users = usersData.map((data) => User.fromJson(data)).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = 'Falha ao carregar usuários: ${response.statusCode}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Erro ao carregar usuários: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _toggleUserStatus(String userId, bool isActive) async {
    try {
      final appProvider = context.read<AppProvider>();
      final response = await http.put(
        Uri.parse('${appProvider.apiUrl}/api/admin/users/$userId/status'),
        headers: {
          'Authorization': 'Bearer ${appProvider.authToken}',
          'Content-Type': 'application/json',
        },
        body: json.encode({'active': isActive}),
      );

      if (response.statusCode == 200) {
        await _fetchUsers();
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isActive 
                ? 'Usuário ativado com sucesso' 
                : 'Usuário desativado com sucesso'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Falha ao atualizar usuário: ${response.statusCode}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao atualizar usuário: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _extendSubscription(String userId, int days) async {
    try {
      final appProvider = context.read<AppProvider>();
      final response = await http.post(
        Uri.parse('${appProvider.apiUrl}/api/admin/users/$userId/subscription'),
        headers: {
          'Authorization': 'Bearer ${appProvider.authToken}',
          'Content-Type': 'application/json',
        },
        body: json.encode({'days': days}),
      );

      if (response.statusCode == 200) {
        await _fetchUsers();
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Assinatura estendida por $days dias'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Falha ao estender assinatura: ${response.statusCode}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao estender assinatura: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showExtendSubscriptionDialog(String userId, String userName) {
    int days = 30;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text('Estender Assinatura'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Estender a assinatura de $userName por quantos dias?'),
              SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Slider(
                      value: days.toDouble(),
                      min: 1,
                      max: 365,
                      divisions: 365,
                      label: days.toString(),
                      onChanged: (value) {
                        setState(() {
                          days = value.round();
                        });
                      },
                    ),
                  ),
                  Text('$days dias'),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Cancelar'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _extendSubscription(userId, days);
              },
              child: Text('Confirmar'),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Painel de Administração'),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: 'Usuários'),
            Tab(text: 'Assinaturas'),
            Tab(text: 'Estatísticas'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Tab de Usuários
          _buildUsersTab(),
          
          // Tab de Assinaturas
          _buildSubscriptionsTab(),
          
          // Tab de Estatísticas
          _buildStatisticsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _fetchUsers,
        tooltip: 'Atualizar',
        child: Icon(Icons.refresh),
      ),
    );
  }

  Widget _buildUsersTab() {
    if (_isLoading) {
      return Center(child: CircularProgressIndicator());
    }
    
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(_error!, style: TextStyle(color: Colors.red)),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchUsers,
              child: Text('Tentar novamente'),
            ),
          ],
        ),
      );
    }
    
    if (_users.isEmpty) {
      return Center(
        child: Text('Nenhum usuário encontrado.'),
      );
    }
    
    return ListView.builder(
      itemCount: _users.length,
      itemBuilder: (context, index) {
        final user = _users[index];
        return Card(
          margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            title: Text(user.name),
            subtitle: Text(user.email),
            trailing: Switch(
              value: user.isActive,
              onChanged: (value) => _toggleUserStatus(user.id, value),
            ),
            onTap: () => _showUserDetailsBottomSheet(user),
          ),
        );
      },
    );
  }

  Widget _buildSubscriptionsTab() {
    if (_isLoading) {
      return Center(child: CircularProgressIndicator());
    }
    
    if (_error != null) {
      return Center(child: Text(_error!));
    }
    
    // Filtrar usuários com assinatura
    final usersWithSubscription = _users.where((user) => 
        user.subscriptionEndDate != null).toList();
        
    if (usersWithSubscription.isEmpty) {
      return Center(
        child: Text('Nenhuma assinatura ativa encontrada.'),
      );
    }
    
    // Ordenar por data de término (mais próximas do vencimento primeiro)
    usersWithSubscription.sort((a, b) => 
        a.subscriptionEndDate!.compareTo(b.subscriptionEndDate!));
        
    return ListView.builder(
      itemCount: usersWithSubscription.length,
      itemBuilder: (context, index) {
        final user = usersWithSubscription[index];
        final now = DateTime.now();
        final daysRemaining = user.subscriptionEndDate!.difference(now).inDays;
        
        return Card(
          margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          color: daysRemaining < 7 ? Colors.red[50] : null,
          child: ListTile(
            title: Text(user.name),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(user.email),
                Text(
                  'Expira em: ${user.formattedSubscriptionEndDate}',
                  style: TextStyle(
                    color: daysRemaining < 7 ? Colors.red : null,
                    fontWeight: daysRemaining < 7 ? FontWeight.bold : null,
                  ),
                ),
                Text('Dias restantes: $daysRemaining'),
              ],
            ),
            isThreeLine: true,
            trailing: IconButton(
              icon: Icon(Icons.date_range),
              tooltip: 'Estender assinatura',
              onPressed: () => _showExtendSubscriptionDialog(user.id, user.name),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatisticsTab() {
    // Em um app real, isso seria obtido do backend
    final totalUsers = _users.length;
    final activeUsers = _users.where((u) => u.isActive).length;
    final usersWithSubscription = _users.where((u) => 
        u.subscriptionEndDate != null && 
        u.subscriptionEndDate!.isAfter(DateTime.now())).length;
        
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Resumo',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  Divider(),
                  _statisticsItem('Total de usuários', totalUsers.toString()),
                  _statisticsItem('Usuários ativos', activeUsers.toString()),
                  _statisticsItem('Assinaturas ativas', usersWithSubscription.toString()),
                  _statisticsItem(
                    'Taxa de conversão', 
                    totalUsers > 0 
                        ? '${(usersWithSubscription * 100 / totalUsers).toStringAsFixed(1)}%' 
                        : '0%'
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),
          Text(
            'Próximos vencimentos',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          SizedBox(height: 8),
          _buildUpcomingExpirations(),
        ],
      ),
    );
  }
  
  Widget _buildUpcomingExpirations() {
    // Filtrar usuários com assinatura e ordenar por data de vencimento
    final upcomingExpirations = _users
        .where((u) => u.subscriptionEndDate != null)
        .toList()
      ..sort((a, b) => a.subscriptionEndDate!.compareTo(b.subscriptionEndDate!));
      
    if (upcomingExpirations.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text('Nenhuma assinatura para expirar'),
        ),
      );
    }
    
    // Mostrar apenas os próximos 5 vencimentos
    final expirations = upcomingExpirations.take(5).toList();
    
    return Card(
      child: ListView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: expirations.length,
        itemBuilder: (context, index) {
          final user = expirations[index];
          final now = DateTime.now();
          final daysRemaining = user.subscriptionEndDate!.difference(now).inDays;
          
          return ListTile(
            title: Text(user.name),
            subtitle: Text('${user.formattedSubscriptionEndDate} ($daysRemaining dias)'),
            trailing: daysRemaining < 7 
                ? Icon(Icons.warning, color: Colors.red)
                : null,
          );
        },
      ),
    );
  }

  Widget _statisticsItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  void _showUserDetailsBottomSheet(User user) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 60,
                height: 5,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Detalhes do Usuário',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: 16),
            _detailItem('ID', user.id),
            _detailItem('Nome', user.name),
            _detailItem('Email', user.email),
            _detailItem('Status', user.isActive ? 'Ativo' : 'Inativo'),
            _detailItem(
              'Assinatura', 
              user.subscriptionEndDate != null 
                  ? 'Válida até ${user.formattedSubscriptionEndDate}'
                  : 'Sem assinatura'
            ),
            _detailItem('Criado em', user.formattedCreatedAt),
            SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _toggleUserStatus(user.id, !user.isActive);
                  },
                  icon: Icon(user.isActive ? Icons.block : Icons.check_circle),
                  label: Text(user.isActive ? 'Desativar' : 'Ativar'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: user.isActive ? Colors.red : Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showExtendSubscriptionDialog(user.id, user.name);
                  },
                  icon: Icon(Icons.date_range),
                  label: Text('Estender Assinatura'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _detailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';

class OrientationSelector extends StatelessWidget {
  final String currentOrientation;
  final Function(String) onOrientationChanged;
  final bool isRecording;
  
  const OrientationSelector({
    super.key,
    required this.currentOrientation,
    required this.onOrientationChanged,
    required this.isRecording,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      margin: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Orientação da gravação:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildOrientationOption(
                context, 
                'Vertical', 
                'vertical',
                Icons.stay_current_portrait,
              ),
              _buildOrientationOption(
                context, 
                'Horizontal', 
                'horizontal',
                Icons.stay_current_landscape,
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  Widget _buildOrientationOption(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final isSelected = currentOrientation == value;
    
    return InkWell(
      onTap: isRecording ? null : () => onOrientationChanged(value),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 24),
        decoration: BoxDecoration(
          color: isSelected 
              ? Theme.of(context).colorScheme.primary 
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: isSelected
                  ? Theme.of(context).colorScheme.onPrimary
                  : Theme.of(context).colorScheme.onSurface,
            ),
            SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

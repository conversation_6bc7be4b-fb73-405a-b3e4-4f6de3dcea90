import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class AppProvider extends ChangeNotifier {
  static late SharedPreferences _prefs;
  static final _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(accessibility: KeychainAccessibility.first_unlock),
  );
  
  // Chave para criptografia simples (para dados não tão sensíveis)
  static const String _encryptionKey = 'cartorios_seguro_2025';
  
  ThemeMode _themeMode = ThemeMode.system;
  String _apiUrl = 'http://********:3000'; // URL padrão (localhost para emulador)
  
  // Estado de autenticação
  bool _isAuthenticated = false;
  String? _authToken;
  String? _userId;
  String? _userName;
  String? _userEmail;
  String? _userRole; // 'user', 'admin'
  DateTime? _subscriptionEndDate;
  
  // Getters
  ThemeMode get themeMode => _themeMode;
  String get apiUrl => _apiUrl;
  bool get isAuthenticated => _isAuthenticated;
  String? get authToken => _authToken;
  String? get userId => _userId;
  String? get userName => _userName;
  String? get userEmail => _userEmail;
  String? get userRole => _userRole;
  DateTime? get subscriptionEndDate => _subscriptionEndDate;
  bool get isAdmin => _userRole == 'admin';
  bool get hasActiveSubscription => _subscriptionEndDate != null && 
      _subscriptionEndDate!.isAfter(DateTime.now());
  
  // Inicialização
  static Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }
  
  // Construtor
  AppProvider() {
    _loadSettings();
    _loadAuthData();
  }
  
  // Métodos de criptografia
  String _encrypt(String data) {
    final key = utf8.encode(_encryptionKey);
    final bytes = utf8.encode(data);
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(bytes);
    return base64.encode(bytes) + "." + digest.toString();
  }
  
  String? _decrypt(String? encryptedData) {
    if (encryptedData == null) return null;
    try {
      final parts = encryptedData.split(".");
      if (parts.length != 2) return null;
      
      final data = base64.decode(parts[0]);
      final key = utf8.encode(_encryptionKey);
      final hmac = Hmac(sha256, key);
      final digest = hmac.convert(data);
      
      if (digest.toString() != parts[1]) return null; // Verificação de integridade falhou
      
      return utf8.decode(data);
    } catch (e) {
      debugPrint('Erro ao descriptografar: $e');
      return null;
    }
  }
  
  // Carregar configurações
  void _loadSettings() {
    final themeModeIndex = _prefs.getInt('themeMode') ?? 0;
    _themeMode = ThemeMode.values[themeModeIndex];
    
    _apiUrl = _prefs.getString('apiUrl') ?? 'http://********:3000';
    
    notifyListeners();
  }
  
  // Carregar dados de autenticação salvos
  Future<void> _loadAuthData() async {
    try {
      // Carregar dados sensíveis do armazenamento seguro
      _authToken = await _secureStorage.read(key: 'authToken');
      _userId = await _secureStorage.read(key: 'userId');
      
      // Carregar e descriptografar dados menos sensíveis
      _userName = _decrypt(_prefs.getString('userName'));
      _userEmail = _decrypt(_prefs.getString('userEmail'));
      _userRole = _prefs.getString('userRole');
      
      final subscriptionEndTimestamp = _prefs.getInt('subscriptionEndDate');
      if (subscriptionEndTimestamp != null) {
        _subscriptionEndDate = DateTime.fromMillisecondsSinceEpoch(subscriptionEndTimestamp);
      }
      
      _isAuthenticated = _authToken != null;
      
      // Verificar se o token ainda é válido
      if (_isAuthenticated && _authToken != null) {
        await _validateToken();
      }
    } catch (e) {
      debugPrint('Erro ao carregar dados de autenticação: $e');
      _isAuthenticated = false;
    }
    
    notifyListeners();
  }
  
  // Validar token com o backend
  Future<void> _validateToken() async {
    if (_authToken == null) return;
    
    try {
      final response = await http.get(
        Uri.parse('$_apiUrl/api/auth/validate'),
        headers: {'Authorization': 'Bearer $_authToken'},
      );
      
      if (response.statusCode != 200) {
        // Token inválido, fazer logout
        await logout();
      }
    } catch (e) {
      // Erro de conexão, manter o usuário logado mas será validado na próxima operação
      debugPrint('Erro ao validar token: $e');
    }
  }
  
  // Alternar tema
  void toggleTheme() {
    _themeMode = _themeMode == ThemeMode.light
        ? ThemeMode.dark
        : ThemeMode.light;
        
    _prefs.setInt('themeMode', _themeMode.index);
    notifyListeners();
  }
  
  // Atualizar URL da API
  Future<void> updateApiUrl(String url) async {
    _apiUrl = url;
    await _prefs.setString('apiUrl', url);
    notifyListeners();
  }
  
  // Autenticar usuário (login)
  Future<bool> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$_apiUrl/api/auth/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'email': email,
          'password': password,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _authToken = data['token'];
        _userId = data['userId'];
        _userName = data['name'];
        _userEmail = email;
        _userRole = data['role'];
        
        if (data['subscriptionEnd'] != null) {
          _subscriptionEndDate = DateTime.parse(data['subscriptionEnd']);
        }
        
        _isAuthenticated = true;
        
        // Salvar no SharedPreferences
        await _prefs.setString('authToken', _authToken!);
        await _prefs.setString('userId', _userId!);
        await _prefs.setString('userName', _userName!);
        await _prefs.setString('userEmail', _userEmail!);
        await _prefs.setString('userRole', _userRole!);
        
        if (_subscriptionEndDate != null) {
          await _prefs.setInt(
            'subscriptionEndDate', 
            _subscriptionEndDate!.millisecondsSinceEpoch
          );
        }
        
        notifyListeners();
        return true;
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Falha na autenticação');
      }
    } catch (e) {
      debugPrint('Erro no login: $e');
      return false;
    }
  }
  
  // Registrar novo usuário
  Future<bool> register(String name, String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('$_apiUrl/api/auth/register'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'name': name,
          'email': email,
          'password': password,
        }),
      );
      
      if (response.statusCode == 201) {
        // Após o registro, fazer login automaticamente
        return await login(email, password);
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['message'] ?? 'Falha no registro');
      }
    } catch (e) {
      debugPrint('Erro no registro: $e');
      return false;
    }
  }
  
  // Logout
  Future<void> logout() async {
    _isAuthenticated = false;
    _authToken = null;
    _userId = null;
    _userName = null;
    _userEmail = null;
    _userRole = null;
    _subscriptionEndDate = null;
    
    // Remover dados de autenticação do SharedPreferences
    await _prefs.remove('authToken');
    await _prefs.remove('userId');
    await _prefs.remove('userName');
    await _prefs.remove('userEmail');
    await _prefs.remove('userRole');
    await _prefs.remove('subscriptionEndDate');
    
    notifyListeners();
  }
  
  // Atualizar assinatura
  Future<bool> updateSubscription(String subscriptionId, {bool isRenewal = false}) async {
    if (!_isAuthenticated) return false;
    
    try {
      final response = await http.post(
        Uri.parse('$_apiUrl/api/subscriptions/activate'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_authToken',
        },
        body: json.encode({
          'subscriptionId': subscriptionId,
          'isRenewal': isRenewal,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _subscriptionEndDate = DateTime.parse(data['subscriptionEnd']);
        
        await _prefs.setInt(
          'subscriptionEndDate', 
          _subscriptionEndDate!.millisecondsSinceEpoch
        );
        
        notifyListeners();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      debugPrint('Erro ao atualizar assinatura: $e');
      return false;
    }
  }
  
  // Verificar se o usuário tem permissão para gravar
  bool canRecord() {
    return _isAuthenticated && (isAdmin || hasActiveSubscription);
  }
}

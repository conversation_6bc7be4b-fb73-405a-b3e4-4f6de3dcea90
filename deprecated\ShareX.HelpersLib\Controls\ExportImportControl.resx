﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="cmsExport.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="tsmiExportClipboard.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="tsmiExportClipboard.Text" xml:space="preserve">
    <value>Copy to clipboard</value>
  </data>
  <data name="tsmiExportFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="tsmiExportFile.Text" xml:space="preserve">
    <value>Save to file...</value>
  </data>
  <data name="tsmiExportUpload.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="tsmiExportUpload.Text" xml:space="preserve">
    <value>Upload as text</value>
  </data>
  <data name="cmsExport.Size" type="System.Drawing.Size, System.Drawing">
    <value>145, 70</value>
  </data>
  <data name="&gt;&gt;cmsExport.Name" xml:space="preserve">
    <value>cmsExport</value>
  </data>
  <data name="&gt;&gt;cmsExport.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <metadata name="cmsImport.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>125, 17</value>
  </metadata>
  <data name="tsmiImportClipboard.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 22</value>
  </data>
  <data name="tsmiImportClipboard.Text" xml:space="preserve">
    <value>From clipboard</value>
  </data>
  <data name="tsmiImportFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 22</value>
  </data>
  <data name="tsmiImportFile.Text" xml:space="preserve">
    <value>From file...</value>
  </data>
  <data name="tsmiImportURL.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 22</value>
  </data>
  <data name="tsmiImportURL.Text" xml:space="preserve">
    <value>From URL...</value>
  </data>
  <data name="cmsImport.Size" type="System.Drawing.Size, System.Drawing">
    <value>131, 70</value>
  </data>
  <data name="&gt;&gt;cmsImport.Name" xml:space="preserve">
    <value>cmsImport</value>
  </data>
  <data name="&gt;&gt;cmsImport.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnImport.Location" type="System.Drawing.Point, System.Drawing">
    <value>96, 0</value>
  </data>
  <data name="btnImport.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 24</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnImport.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnImport.Text" xml:space="preserve">
    <value> Import</value>
  </data>
  <data name="btnImport.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;btnImport.Name" xml:space="preserve">
    <value>btnImport</value>
  </data>
  <data name="&gt;&gt;btnImport.Type" xml:space="preserve">
    <value>HelpersLib.MenuButton, HelpersLib, Version=*******, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;btnImport.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnImport.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnExport.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="btnExport.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 24</value>
  </data>
  <data name="btnExport.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnExport.Text" xml:space="preserve">
    <value> Export</value>
  </data>
  <data name="btnExport.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;btnExport.Name" xml:space="preserve">
    <value>btnExport</value>
  </data>
  <data name="&gt;&gt;btnExport.Type" xml:space="preserve">
    <value>HelpersLib.MenuButton, HelpersLib, Version=*******, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;btnExport.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnExport.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>185, 24</value>
  </data>
  <data name="&gt;&gt;tsmiExportClipboard.Name" xml:space="preserve">
    <value>tsmiExportClipboard</value>
  </data>
  <data name="&gt;&gt;tsmiExportClipboard.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiExportFile.Name" xml:space="preserve">
    <value>tsmiExportFile</value>
  </data>
  <data name="&gt;&gt;tsmiExportFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiExportUpload.Name" xml:space="preserve">
    <value>tsmiExportUpload</value>
  </data>
  <data name="&gt;&gt;tsmiExportUpload.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiImportClipboard.Name" xml:space="preserve">
    <value>tsmiImportClipboard</value>
  </data>
  <data name="&gt;&gt;tsmiImportClipboard.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiImportFile.Name" xml:space="preserve">
    <value>tsmiImportFile</value>
  </data>
  <data name="&gt;&gt;tsmiImportFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiImportURL.Name" xml:space="preserve">
    <value>tsmiImportURL</value>
  </data>
  <data name="&gt;&gt;tsmiImportURL.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>ExportImportControl</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
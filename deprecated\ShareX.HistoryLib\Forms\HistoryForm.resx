﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="&gt;&gt;tscHistory.BottomToolStripPanel.Name" xml:space="preserve">
    <value>tscHistory.BottomToolStripPanel</value>
  </data>
  <data name="&gt;&gt;tscHistory.BottomToolStripPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscHistory.BottomToolStripPanel.Parent" xml:space="preserve">
    <value>tscHistory</value>
  </data>
  <data name="&gt;&gt;tscHistory.BottomToolStripPanel.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="chIcon.Text" xml:space="preserve">
    <value />
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="chIcon.Width" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="chDateTime.Text" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="chDateTime.Width" type="System.Int32, mscorlib">
    <value>115</value>
  </data>
  <data name="chFilename.Text" xml:space="preserve">
    <value>Filename</value>
  </data>
  <data name="chFilename.Width" type="System.Int32, mscorlib">
    <value>170</value>
  </data>
  <data name="chURL.Text" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="chURL.Width" type="System.Int32, mscorlib">
    <value>230</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lvHistory.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lvHistory.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lvHistory.Size" type="System.Drawing.Size, System.Drawing">
    <value>550, 636</value>
  </data>
  <data name="lvHistory.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lvHistory.Name" xml:space="preserve">
    <value>lvHistory</value>
  </data>
  <data name="&gt;&gt;lvHistory.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.MyListView, ShareX.HelpersLib, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;lvHistory.Parent" xml:space="preserve">
    <value>tscHistory.ContentPanel</value>
  </data>
  <data name="&gt;&gt;lvHistory.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tscHistory.ContentPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>550, 636</value>
  </data>
  <data name="&gt;&gt;tscHistory.ContentPanel.Name" xml:space="preserve">
    <value>tscHistory.ContentPanel</value>
  </data>
  <data name="&gt;&gt;tscHistory.ContentPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripContentPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscHistory.ContentPanel.Parent" xml:space="preserve">
    <value>tscHistory</value>
  </data>
  <data name="&gt;&gt;tscHistory.ContentPanel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tscHistory.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="&gt;&gt;tscHistory.LeftToolStripPanel.Name" xml:space="preserve">
    <value>tscHistory.LeftToolStripPanel</value>
  </data>
  <data name="&gt;&gt;tscHistory.LeftToolStripPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscHistory.LeftToolStripPanel.Parent" xml:space="preserve">
    <value>tscHistory</value>
  </data>
  <data name="&gt;&gt;tscHistory.LeftToolStripPanel.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tscHistory.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="&gt;&gt;tscHistory.RightToolStripPanel.Name" xml:space="preserve">
    <value>tscHistory.RightToolStripPanel</value>
  </data>
  <data name="&gt;&gt;tscHistory.RightToolStripPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscHistory.RightToolStripPanel.Parent" xml:space="preserve">
    <value>tscHistory</value>
  </data>
  <data name="&gt;&gt;tscHistory.RightToolStripPanel.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="tscHistory.Size" type="System.Drawing.Size, System.Drawing">
    <value>550, 661</value>
  </data>
  <data name="tscHistory.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <metadata name="tsHistory.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="tsHistory.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="tslSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 22</value>
  </data>
  <data name="tslSearch.Text" xml:space="preserve">
    <value>Search:</value>
  </data>
  <data name="tstbSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>300, 25</value>
  </data>
  <data name="tsbSearch.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="tsbSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 22</value>
  </data>
  <data name="tsbSearch.Text" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="tsbAdvancedSearch.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="tsbAdvancedSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 22</value>
  </data>
  <data name="tsbAdvancedSearch.Text" xml:space="preserve">
    <value>Advanced search...</value>
  </data>
  <data name="tss1.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 25</value>
  </data>
  <data name="tsbToggleMoreInfo.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="tsbToggleMoreInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 22</value>
  </data>
  <data name="tsbToggleMoreInfo.Text" xml:space="preserve">
    <value>Toggle more info</value>
  </data>
  <data name="tsbShowStats.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="tsbShowStats.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 22</value>
  </data>
  <data name="tsbShowStats.Text" xml:space="preserve">
    <value>Show stats...</value>
  </data>
  <data name="tss2.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 25</value>
  </data>
  <data name="tsbSettings.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="tsbSettings.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 22</value>
  </data>
  <data name="tsbSettings.Text" xml:space="preserve">
    <value>Settings...</value>
  </data>
  <data name="tsHistory.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 0</value>
  </data>
  <data name="tsHistory.Size" type="System.Drawing.Size, System.Drawing">
    <value>477, 25</value>
  </data>
  <data name="tsHistory.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tsHistory.Name" xml:space="preserve">
    <value>tsHistory</value>
  </data>
  <data name="&gt;&gt;tsHistory.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStrip, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsHistory.Parent" xml:space="preserve">
    <value>tscHistory.TopToolStripPanel</value>
  </data>
  <data name="&gt;&gt;tsHistory.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tscHistory.TopToolStripPanel.Name" xml:space="preserve">
    <value>tscHistory.TopToolStripPanel</value>
  </data>
  <data name="&gt;&gt;tscHistory.TopToolStripPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscHistory.TopToolStripPanel.Parent" xml:space="preserve">
    <value>tscHistory</value>
  </data>
  <data name="&gt;&gt;tscHistory.TopToolStripPanel.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;tscHistory.Name" xml:space="preserve">
    <value>tscHistory</value>
  </data>
  <data name="&gt;&gt;tscHistory.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripContainer, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscHistory.Parent" xml:space="preserve">
    <value>scMain.Panel1</value>
  </data>
  <data name="&gt;&gt;tscHistory.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnAdvancedSearchClose.Location" type="System.Drawing.Point, System.Drawing">
    <value>272, 176</value>
  </data>
  <data name="btnAdvancedSearchClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="btnAdvancedSearchClose.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="btnAdvancedSearchClose.Text" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="&gt;&gt;btnAdvancedSearchClose.Name" xml:space="preserve">
    <value>btnAdvancedSearchClose</value>
  </data>
  <data name="&gt;&gt;btnAdvancedSearchClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAdvancedSearchClose.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;btnAdvancedSearchClose.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnAdvancedSearchReset.Location" type="System.Drawing.Point, System.Drawing">
    <value>144, 176</value>
  </data>
  <data name="btnAdvancedSearchReset.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="btnAdvancedSearchReset.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="btnAdvancedSearchReset.Text" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="&gt;&gt;btnAdvancedSearchReset.Name" xml:space="preserve">
    <value>btnAdvancedSearchReset</value>
  </data>
  <data name="&gt;&gt;btnAdvancedSearchReset.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAdvancedSearchReset.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;btnAdvancedSearchReset.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lblURLFilter.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblURLFilter.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblURLFilter.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 48</value>
  </data>
  <data name="lblURLFilter.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 15</value>
  </data>
  <data name="lblURLFilter.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lblURLFilter.Text" xml:space="preserve">
    <value>URL:</value>
  </data>
  <data name="&gt;&gt;lblURLFilter.Name" xml:space="preserve">
    <value>lblURLFilter</value>
  </data>
  <data name="&gt;&gt;lblURLFilter.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblURLFilter.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;lblURLFilter.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtURLFilter.Location" type="System.Drawing.Point, System.Drawing">
    <value>96, 44</value>
  </data>
  <data name="txtURLFilter.Size" type="System.Drawing.Size, System.Drawing">
    <value>296, 23</value>
  </data>
  <data name="txtURLFilter.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;txtURLFilter.Name" xml:space="preserve">
    <value>txtURLFilter</value>
  </data>
  <data name="&gt;&gt;txtURLFilter.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtURLFilter.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;txtURLFilter.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="lblFilenameFilter.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblFilenameFilter.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblFilenameFilter.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 24</value>
  </data>
  <data name="lblFilenameFilter.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 15</value>
  </data>
  <data name="lblFilenameFilter.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblFilenameFilter.Text" xml:space="preserve">
    <value>Filename:</value>
  </data>
  <data name="&gt;&gt;lblFilenameFilter.Name" xml:space="preserve">
    <value>lblFilenameFilter</value>
  </data>
  <data name="&gt;&gt;lblFilenameFilter.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblFilenameFilter.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;lblFilenameFilter.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="cbHostFilterSelection.Location" type="System.Drawing.Point, System.Drawing">
    <value>152, 142</value>
  </data>
  <data name="cbHostFilterSelection.Size" type="System.Drawing.Size, System.Drawing">
    <value>240, 23</value>
  </data>
  <data name="cbHostFilterSelection.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;cbHostFilterSelection.Name" xml:space="preserve">
    <value>cbHostFilterSelection</value>
  </data>
  <data name="&gt;&gt;cbHostFilterSelection.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbHostFilterSelection.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;cbHostFilterSelection.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cbTypeFilterSelection.Location" type="System.Drawing.Point, System.Drawing">
    <value>152, 118</value>
  </data>
  <data name="cbTypeFilterSelection.Size" type="System.Drawing.Size, System.Drawing">
    <value>240, 23</value>
  </data>
  <data name="cbTypeFilterSelection.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;cbTypeFilterSelection.Name" xml:space="preserve">
    <value>cbTypeFilterSelection</value>
  </data>
  <data name="&gt;&gt;cbTypeFilterSelection.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbTypeFilterSelection.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;cbTypeFilterSelection.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="cbHostFilter.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbHostFilter.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="cbHostFilter.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 144</value>
  </data>
  <data name="cbHostFilter.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 19</value>
  </data>
  <data name="cbHostFilter.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="cbHostFilter.Text" xml:space="preserve">
    <value>Host:</value>
  </data>
  <data name="&gt;&gt;cbHostFilter.Name" xml:space="preserve">
    <value>cbHostFilter</value>
  </data>
  <data name="&gt;&gt;cbHostFilter.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbHostFilter.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;cbHostFilter.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cbTypeFilter.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbTypeFilter.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="cbTypeFilter.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 120</value>
  </data>
  <data name="cbTypeFilter.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 19</value>
  </data>
  <data name="cbTypeFilter.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="cbTypeFilter.Text" xml:space="preserve">
    <value>Type:</value>
  </data>
  <data name="&gt;&gt;cbTypeFilter.Name" xml:space="preserve">
    <value>cbTypeFilter</value>
  </data>
  <data name="&gt;&gt;cbTypeFilter.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbTypeFilter.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;cbTypeFilter.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="dtpFilterFrom.Location" type="System.Drawing.Point, System.Drawing">
    <value>152, 70</value>
  </data>
  <data name="dtpFilterFrom.Size" type="System.Drawing.Size, System.Drawing">
    <value>240, 23</value>
  </data>
  <data name="dtpFilterFrom.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;dtpFilterFrom.Name" xml:space="preserve">
    <value>dtpFilterFrom</value>
  </data>
  <data name="&gt;&gt;dtpFilterFrom.Type" xml:space="preserve">
    <value>System.Windows.Forms.DateTimePicker, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;dtpFilterFrom.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;dtpFilterFrom.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="lblFilterFrom.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblFilterFrom.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblFilterFrom.Location" type="System.Drawing.Point, System.Drawing">
    <value>93, 74</value>
  </data>
  <data name="lblFilterFrom.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 15</value>
  </data>
  <data name="lblFilterFrom.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lblFilterFrom.Text" xml:space="preserve">
    <value>From:</value>
  </data>
  <data name="&gt;&gt;lblFilterFrom.Name" xml:space="preserve">
    <value>lblFilterFrom</value>
  </data>
  <data name="&gt;&gt;lblFilterFrom.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblFilterFrom.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;lblFilterFrom.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="lblFilterTo.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblFilterTo.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblFilterTo.Location" type="System.Drawing.Point, System.Drawing">
    <value>93, 98</value>
  </data>
  <data name="lblFilterTo.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 15</value>
  </data>
  <data name="lblFilterTo.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="lblFilterTo.Text" xml:space="preserve">
    <value>To:</value>
  </data>
  <data name="&gt;&gt;lblFilterTo.Name" xml:space="preserve">
    <value>lblFilterTo</value>
  </data>
  <data name="&gt;&gt;lblFilterTo.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblFilterTo.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;lblFilterTo.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="cbDateFilter.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbDateFilter.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="cbDateFilter.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 72</value>
  </data>
  <data name="cbDateFilter.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 19</value>
  </data>
  <data name="cbDateFilter.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="cbDateFilter.Text" xml:space="preserve">
    <value>Date:</value>
  </data>
  <data name="&gt;&gt;cbDateFilter.Name" xml:space="preserve">
    <value>cbDateFilter</value>
  </data>
  <data name="&gt;&gt;cbDateFilter.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbDateFilter.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;cbDateFilter.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="dtpFilterTo.Location" type="System.Drawing.Point, System.Drawing">
    <value>152, 94</value>
  </data>
  <data name="dtpFilterTo.Size" type="System.Drawing.Size, System.Drawing">
    <value>240, 23</value>
  </data>
  <data name="dtpFilterTo.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;dtpFilterTo.Name" xml:space="preserve">
    <value>dtpFilterTo</value>
  </data>
  <data name="&gt;&gt;dtpFilterTo.Type" xml:space="preserve">
    <value>System.Windows.Forms.DateTimePicker, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;dtpFilterTo.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;dtpFilterTo.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="txtFilenameFilter.Location" type="System.Drawing.Point, System.Drawing">
    <value>96, 20</value>
  </data>
  <data name="txtFilenameFilter.Size" type="System.Drawing.Size, System.Drawing">
    <value>296, 23</value>
  </data>
  <data name="txtFilenameFilter.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txtFilenameFilter.Name" xml:space="preserve">
    <value>txtFilenameFilter</value>
  </data>
  <data name="&gt;&gt;txtFilenameFilter.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtFilenameFilter.Parent" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;txtFilenameFilter.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="gbAdvancedSearch.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="gbAdvancedSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>409, 213</value>
  </data>
  <data name="gbAdvancedSearch.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gbAdvancedSearch.Text" xml:space="preserve">
    <value>Advanced search</value>
  </data>
  <data name="gbAdvancedSearch.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gbAdvancedSearch.Name" xml:space="preserve">
    <value>gbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;gbAdvancedSearch.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gbAdvancedSearch.Parent" xml:space="preserve">
    <value>scHistoryItemInfo.Panel1</value>
  </data>
  <data name="&gt;&gt;gbAdvancedSearch.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="scMain.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="scMain.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="&gt;&gt;scMain.Panel1.Name" xml:space="preserve">
    <value>scMain.Panel1</value>
  </data>
  <data name="&gt;&gt;scMain.Panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.SplitterPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;scMain.Panel1.Parent" xml:space="preserve">
    <value>scMain</value>
  </data>
  <data name="&gt;&gt;scMain.Panel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="scMain.Panel1MinSize" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="scHistoryItemInfo.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="scHistoryItemInfo.IsSplitterFixed" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="scHistoryItemInfo.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="scHistoryItemInfo.Orientation" type="System.Windows.Forms.Orientation, System.Windows.Forms">
    <value>Horizontal</value>
  </data>
  <data name="pbThumbnail.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="pbThumbnail.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="pbThumbnail.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 3, 4, 3</value>
  </data>
  <data name="pbThumbnail.Size" type="System.Drawing.Size, System.Drawing">
    <value>627, 412</value>
  </data>
  <data name="pbThumbnail.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;pbThumbnail.Name" xml:space="preserve">
    <value>pbThumbnail</value>
  </data>
  <data name="&gt;&gt;pbThumbnail.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.MyPictureBox, ShareX.HelpersLib, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;pbThumbnail.Parent" xml:space="preserve">
    <value>scHistoryItemInfo.Panel1</value>
  </data>
  <data name="&gt;&gt;pbThumbnail.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.Panel1.Name" xml:space="preserve">
    <value>scHistoryItemInfo.Panel1</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.Panel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.SplitterPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.Panel1.Parent" xml:space="preserve">
    <value>scHistoryItemInfo</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.Panel1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="pgHistoryItemInfo.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="pgHistoryItemInfo.HelpVisible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="pgHistoryItemInfo.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="pgHistoryItemInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>627, 242</value>
  </data>
  <data name="pgHistoryItemInfo.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;pgHistoryItemInfo.Name" xml:space="preserve">
    <value>pgHistoryItemInfo</value>
  </data>
  <data name="&gt;&gt;pgHistoryItemInfo.Type" xml:space="preserve">
    <value>System.Windows.Forms.PropertyGrid, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pgHistoryItemInfo.Parent" xml:space="preserve">
    <value>scHistoryItemInfo.Panel2</value>
  </data>
  <data name="&gt;&gt;pgHistoryItemInfo.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.Panel2.Name" xml:space="preserve">
    <value>scHistoryItemInfo.Panel2</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.Panel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.SplitterPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.Panel2.Parent" xml:space="preserve">
    <value>scHistoryItemInfo</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.Panel2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="scHistoryItemInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>627, 661</value>
  </data>
  <data name="scHistoryItemInfo.SplitterDistance" type="System.Int32, mscorlib">
    <value>412</value>
  </data>
  <data name="scHistoryItemInfo.SplitterWidth" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="scHistoryItemInfo.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.Name" xml:space="preserve">
    <value>scHistoryItemInfo</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.SplitContainerCustomSplitter, ShareX.HelpersLib, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.Parent" xml:space="preserve">
    <value>scMain.Panel2</value>
  </data>
  <data name="&gt;&gt;scHistoryItemInfo.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;scMain.Panel2.Name" xml:space="preserve">
    <value>scMain.Panel2</value>
  </data>
  <data name="&gt;&gt;scMain.Panel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.SplitterPanel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;scMain.Panel2.Parent" xml:space="preserve">
    <value>scMain</value>
  </data>
  <data name="&gt;&gt;scMain.Panel2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="scMain.Panel2MinSize" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="scMain.Size" type="System.Drawing.Size, System.Drawing">
    <value>1184, 661</value>
  </data>
  <data name="scMain.SplitterDistance" type="System.Int32, mscorlib">
    <value>550</value>
  </data>
  <data name="scMain.SplitterWidth" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="scMain.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;scMain.Name" xml:space="preserve">
    <value>scMain</value>
  </data>
  <data name="&gt;&gt;scMain.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.SplitContainerCustomSplitter, ShareX.HelpersLib, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;scMain.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;scMain.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>96, 96</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1184, 661</value>
  </data>
  <data name="$this.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>500, 500</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - History</value>
  </data>
  <data name="&gt;&gt;chIcon.Name" xml:space="preserve">
    <value>chIcon</value>
  </data>
  <data name="&gt;&gt;chIcon.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chDateTime.Name" xml:space="preserve">
    <value>chDateTime</value>
  </data>
  <data name="&gt;&gt;chDateTime.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chFilename.Name" xml:space="preserve">
    <value>chFilename</value>
  </data>
  <data name="&gt;&gt;chFilename.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chURL.Name" xml:space="preserve">
    <value>chURL</value>
  </data>
  <data name="&gt;&gt;chURL.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tslSearch.Name" xml:space="preserve">
    <value>tslSearch</value>
  </data>
  <data name="&gt;&gt;tslSearch.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripLabel, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tstbSearch.Name" xml:space="preserve">
    <value>tstbSearch</value>
  </data>
  <data name="&gt;&gt;tstbSearch.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripTextBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsbSearch.Name" xml:space="preserve">
    <value>tsbSearch</value>
  </data>
  <data name="&gt;&gt;tsbSearch.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsbAdvancedSearch.Name" xml:space="preserve">
    <value>tsbAdvancedSearch</value>
  </data>
  <data name="&gt;&gt;tsbAdvancedSearch.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tss1.Name" xml:space="preserve">
    <value>tss1</value>
  </data>
  <data name="&gt;&gt;tss1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsbToggleMoreInfo.Name" xml:space="preserve">
    <value>tsbToggleMoreInfo</value>
  </data>
  <data name="&gt;&gt;tsbToggleMoreInfo.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsbShowStats.Name" xml:space="preserve">
    <value>tsbShowStats</value>
  </data>
  <data name="&gt;&gt;tsbShowStats.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tss2.Name" xml:space="preserve">
    <value>tss2</value>
  </data>
  <data name="&gt;&gt;tss2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsbSettings.Name" xml:space="preserve">
    <value>tsbSettings</value>
  </data>
  <data name="&gt;&gt;tsbSettings.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>HistoryForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
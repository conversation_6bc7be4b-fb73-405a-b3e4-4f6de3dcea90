# Aplicativo Móvel Arquivado

Este diretório contém o código fonte do aplicativo móvel Flutter que foi substituído por uma implementação web (PWA). O código foi preservado para referência futura e possível reutilização.

## Motivo da migração

A decisão de migrar de um aplicativo móvel nativo (Flutter) para uma aplicação web (PWA) foi tomada em 4 de julho de 2025, considerando as seguintes vantagens:

1. **Desenvolvimento único**: Uma única aplicação web que funciona em qualquer dispositivo
2. **Atualizações instantâneas**: Sem necessidade de publicação nas lojas
3. **Acesso facilitado**: Os usuários não precisam instalar nada
4. **Menos restrições**: Menos limitações impostas pelas políticas das lojas de aplicativos
5. **SEO potencial**: Possibilidade de indexação por motores de busca

## Estrutura do código arquivado

- `lib/providers/` - Gerenciamento de estado, autenticação e configurações
- `lib/screens/` - Telas do aplicativo (login, registro, gravações, etc.)
- `lib/services/` - Serviços (gravação de tela, API, etc.)
- `lib/widgets/` - Componentes reutilizáveis
- `lib/utils/` - Utilitários e funções auxiliares
- `lib/main.dart` - Ponto de entrada do aplicativo

## Funcionalidades implementadas

- Autenticação (login/registro)
- Gravação de tela com consentimento LGPD
- Gerenciamento de assinaturas
- Painel de administração
- Criptografia local de dados sensíveis
- Integração com backend
- Geração de hash SHA-256

## Nota importante

Este código é mantido apenas para referência e não deve ser mantido atualizado. A versão oficial do sistema agora é a implementação web localizada em `../web-app`.

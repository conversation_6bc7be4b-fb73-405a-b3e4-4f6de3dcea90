﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="HistoryManager_GetHistoryItems_Error" xml:space="preserve">
    <value>Erro</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Thumbnail_URL" xml:space="preserve">
    <value>Hiperligação de Miniatura</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Image_preview" xml:space="preserve">
    <value>Pré-visualização de Imagem</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_File" xml:space="preserve">
    <value>Ficheiro</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Image" xml:space="preserve">
    <value>Imagem</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Markdown__link" xml:space="preserve">
    <value>Hiperligação MarkDown</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Folder" xml:space="preserve">
    <value>Pasta</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_URL" xml:space="preserve">
    <value>Hiperligação</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_File_name_with_extension" xml:space="preserve">
    <value>Nome do ficheiro com extensão</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Copy" xml:space="preserve">
    <value>Copiar</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_HTML_image" xml:space="preserve">
    <value>Imagem HTML</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Markdown__linked_image" xml:space="preserve">
    <value>Hiperligação de imagem MarkDown</value>
  </data>
  <data name="HistoryForm_UpdateItemCount_Total___0_" xml:space="preserve">
    <value>Total: {0}</value>
  </data>
  <data name="HistoryForm_UpdateItemCount___Filtered___0_" xml:space="preserve">
    <value>Filtrados: {0}</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Open" xml:space="preserve">
    <value>Abrir</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_More_info" xml:space="preserve">
    <value>Mais informações...</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_HTML_link" xml:space="preserve">
    <value>Hiperligação HTML</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Markdown__image" xml:space="preserve">
    <value>Imagem MarkDown</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_HTML_linked_image" xml:space="preserve">
    <value>Hiperligação Imagem HTML</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Forum__BBCode__linked_image" xml:space="preserve">
    <value>Hiperligação Imagem direta para fórum (BBCode)</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Deletion_URL" xml:space="preserve">
    <value>Hiperligação de remoção</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Forum__BBCode__image" xml:space="preserve">
    <value>Hiperligação Imagem para fórum (BBCode)</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Text" xml:space="preserve">
    <value>Texto</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Shortened_URL" xml:space="preserve">
    <value>Hiperligação encurtada</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_File_name" xml:space="preserve">
    <value>Nome do ficheiro</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_File_path" xml:space="preserve">
    <value>Caminho do ficheiro</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_Forum__BBCode__link" xml:space="preserve">
    <value>Hiperligação para fórum (BBCode)</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_UploadFile" xml:space="preserve">
    <value>Enviar ficheiro</value>
  </data>
  <data name="HistoryItemManager_InitializeComponent_EditImage" xml:space="preserve">
    <value>Editar imagem...</value>
  </data>
  <data name="HistoryItemCounts" xml:space="preserve">
    <value>Contador de items no histórico:</value>
  </data>
  <data name="HistoryStats_Total" xml:space="preserve">
    <value>Total:</value>
  </data>
  <data name="HistoryStats_YearlyUsages" xml:space="preserve">
    <value>Usagem anual:</value>
  </data>
  <data name="HistoryStats_FileExtensions" xml:space="preserve">
    <value>Extensões de ficheiro:</value>
  </data>
  <data name="HistoryStats_Hosts" xml:space="preserve">
    <value>Hospedagem:</value>
  </data>
  <data name="ErrorOccuredWhileReadingHistoryFile" xml:space="preserve">
    <value>Erro ao ler o ficheiro de histórico:</value>
  </data>
</root>
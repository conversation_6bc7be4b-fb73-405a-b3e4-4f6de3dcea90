const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const dotenv = require('dotenv');
const path = require('path');
const winston = require('winston');

// Carregar variáveis de ambiente
dotenv.config();

// Configurar logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'ai-service' },
  transports: [
    new winston.transports.File({ filename: '/app/logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: '/app/logs/combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});

const app = express();
const PORT = process.env.PORT || 3003;

// Middlewares
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Middleware de logging
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  next();
});

// Importar rotas
const transcriptionRoutes = require('./routes/transcription');
const semanticSearchRoutes = require('./routes/semanticSearch');
const complianceRoutes = require('./routes/compliance');
const qualityAnalysisRoutes = require('./routes/qualityAnalysis');
const aiAnalyticsRoutes = require('./routes/aiAnalytics');

// Usar rotas
app.use('/api/ai/transcription', transcriptionRoutes);
app.use('/api/ai/semantic-search', semanticSearchRoutes);
app.use('/api/ai/compliance', complianceRoutes);
app.use('/api/ai/quality', qualityAnalysisRoutes);
app.use('/api/ai/analytics', aiAnalyticsRoutes);

// Rota de health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'ai-service',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Middleware de tratamento de erros
app.use((error, req, res, next) => {
  logger.error('Erro não tratado:', error);
  res.status(500).json({
    success: false,
    message: 'Erro interno do servidor de IA',
    error: process.env.NODE_ENV === 'development' ? error.message : 'Erro interno'
  });
});

// Middleware para rotas não encontradas
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint não encontrado no serviço de IA'
  });
});

// Inicializar serviços
const initializeServices = async () => {
  try {
    // Inicializar Redis para filas
    const queueManager = require('./services/queueManager');
    await queueManager.initialize();
    
    // Inicializar banco de dados para IA
    const aiDatabase = require('./database/aiDatabase');
    await aiDatabase.initialize();
    
    logger.info('Serviços de IA inicializados com sucesso');
  } catch (error) {
    logger.error('Erro ao inicializar serviços de IA:', error);
    process.exit(1);
  }
};

// Iniciar servidor
const startServer = async () => {
  await initializeServices();
  
  app.listen(PORT, () => {
    logger.info(`🤖 Serviço de IA rodando na porta ${PORT}`);
    logger.info(`🔗 Health check: http://localhost:${PORT}/health`);
  });
};

// Tratamento de sinais de sistema
process.on('SIGTERM', () => {
  logger.info('SIGTERM recebido, encerrando servidor...');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT recebido, encerrando servidor...');
  process.exit(0);
});

startServer().catch(error => {
  logger.error('Erro ao iniciar servidor:', error);
  process.exit(1);
});

module.exports = app;

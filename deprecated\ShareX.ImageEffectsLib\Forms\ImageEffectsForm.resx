<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnSaveImage.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnSaveImage.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnSaveImage.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnSaveImage.Location" type="System.Drawing.Point, System.Drawing">
    <value>392, 552</value>
  </data>
  <data name="btnSaveImage.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="btnSaveImage.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="btnSaveImage.Text" xml:space="preserve">
    <value>Save image...</value>
  </data>
  <data name="btnSaveImage.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btnSaveImage.Name" xml:space="preserve">
    <value>btnSaveImage</value>
  </data>
  <data name="&gt;&gt;btnSaveImage.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnSaveImage.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnSaveImage.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <metadata name="cmsEffects.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="cmsEffects.Size" type="System.Drawing.Size, System.Drawing">
    <value>36, 4</value>
  </data>
  <data name="&gt;&gt;cmsEffects.Name" xml:space="preserve">
    <value>cmsEffects</value>
  </data>
  <data name="&gt;&gt;cmsEffects.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <metadata name="cmsLoadImage.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>126, 17</value>
  </metadata>
  <data name="tsmiLoadImageFromFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 22</value>
  </data>
  <data name="tsmiLoadImageFromFile.Text" xml:space="preserve">
    <value>From file...</value>
  </data>
  <data name="tsmiLoadImageFromClipboard.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 22</value>
  </data>
  <data name="tsmiLoadImageFromClipboard.Text" xml:space="preserve">
    <value>From clipboard</value>
  </data>
  <data name="cmsLoadImage.Size" type="System.Drawing.Size, System.Drawing">
    <value>131, 48</value>
  </data>
  <data name="&gt;&gt;cmsLoadImage.Name" xml:space="preserve">
    <value>cmsLoadImage</value>
  </data>
  <data name="&gt;&gt;cmsLoadImage.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="lblPresetName.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblPresetName.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblPresetName.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 8</value>
  </data>
  <data name="lblPresetName.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="lblPresetName.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="lblPresetName.Text" xml:space="preserve">
    <value>Preset name:</value>
  </data>
  <data name="&gt;&gt;lblPresetName.Name" xml:space="preserve">
    <value>lblPresetName</value>
  </data>
  <data name="&gt;&gt;lblPresetName.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblPresetName.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblPresetName.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="txtPresetName.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 24</value>
  </data>
  <data name="txtPresetName.Size" type="System.Drawing.Size, System.Drawing">
    <value>192, 20</value>
  </data>
  <data name="txtPresetName.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;txtPresetName.Name" xml:space="preserve">
    <value>txtPresetName</value>
  </data>
  <data name="&gt;&gt;txtPresetName.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtPresetName.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtPresetName.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="btnClose.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btnClose.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnClose.Location" type="System.Drawing.Point, System.Drawing">
    <value>1096, 552</value>
  </data>
  <data name="btnClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="btnClose.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnClose.Text" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="&gt;&gt;btnClose.Name" xml:space="preserve">
    <value>btnClose</value>
  </data>
  <data name="&gt;&gt;btnClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnClose.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnClose.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="btnOK.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="btnOK.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>968, 552</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="btnOK.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="btnOK.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btnOK.Name" xml:space="preserve">
    <value>btnOK</value>
  </data>
  <data name="&gt;&gt;btnOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOK.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnOK.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="btnUploadImage.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="btnUploadImage.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnUploadImage.Location" type="System.Drawing.Point, System.Drawing">
    <value>520, 552</value>
  </data>
  <data name="btnUploadImage.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="btnUploadImage.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="btnUploadImage.Text" xml:space="preserve">
    <value>Upload image</value>
  </data>
  <data name="btnUploadImage.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btnUploadImage.Name" xml:space="preserve">
    <value>btnUploadImage</value>
  </data>
  <data name="&gt;&gt;btnUploadImage.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnUploadImage.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnUploadImage.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="lblPresets.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblPresets.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblPresets.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 8</value>
  </data>
  <data name="lblPresets.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="lblPresets.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lblPresets.Text" xml:space="preserve">
    <value>Presets:</value>
  </data>
  <data name="&gt;&gt;lblPresets.Name" xml:space="preserve">
    <value>lblPresets</value>
  </data>
  <data name="&gt;&gt;lblPresets.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblPresets.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblPresets.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="btnPackager.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="btnPackager.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnPackager.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 552</value>
  </data>
  <data name="btnPackager.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="btnPackager.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="btnPackager.Text" xml:space="preserve">
    <value>Packager...</value>
  </data>
  <data name="&gt;&gt;btnPackager.Name" xml:space="preserve">
    <value>btnPackager</value>
  </data>
  <data name="&gt;&gt;btnPackager.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnPackager.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnPackager.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="btnPresetNew.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnPresetNew.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 24</value>
  </data>
  <data name="btnPresetNew.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 24</value>
  </data>
  <data name="btnPresetNew.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <metadata name="ttMain.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>260, 17</value>
  </metadata>
  <data name="btnPresetNew.ToolTip" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="&gt;&gt;btnPresetNew.Name" xml:space="preserve">
    <value>btnPresetNew</value>
  </data>
  <data name="&gt;&gt;btnPresetNew.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnPresetNew.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnPresetNew.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="btnPresetRemove.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnPresetRemove.Location" type="System.Drawing.Point, System.Drawing">
    <value>35, 24</value>
  </data>
  <data name="btnPresetRemove.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 24</value>
  </data>
  <data name="btnPresetRemove.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="btnPresetRemove.ToolTip" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="&gt;&gt;btnPresetRemove.Name" xml:space="preserve">
    <value>btnPresetRemove</value>
  </data>
  <data name="&gt;&gt;btnPresetRemove.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnPresetRemove.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnPresetRemove.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="btnPresetDuplicate.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnPresetDuplicate.Location" type="System.Drawing.Point, System.Drawing">
    <value>62, 24</value>
  </data>
  <data name="btnPresetDuplicate.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 24</value>
  </data>
  <data name="btnPresetDuplicate.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="btnPresetDuplicate.ToolTip" xml:space="preserve">
    <value>Duplicate</value>
  </data>
  <data name="&gt;&gt;btnPresetDuplicate.Name" xml:space="preserve">
    <value>btnPresetDuplicate</value>
  </data>
  <data name="&gt;&gt;btnPresetDuplicate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnPresetDuplicate.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnPresetDuplicate.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="lblEffects.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblEffects.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblEffects.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 56</value>
  </data>
  <data name="lblEffects.Size" type="System.Drawing.Size, System.Drawing">
    <value>43, 13</value>
  </data>
  <data name="lblEffects.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="lblEffects.Text" xml:space="preserve">
    <value>Effects:</value>
  </data>
  <data name="&gt;&gt;lblEffects.Name" xml:space="preserve">
    <value>lblEffects</value>
  </data>
  <data name="&gt;&gt;lblEffects.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblEffects.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblEffects.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="btnEffectAdd.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnEffectAdd.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 72</value>
  </data>
  <data name="btnEffectAdd.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 24</value>
  </data>
  <data name="btnEffectAdd.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="btnEffectAdd.ToolTip" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="&gt;&gt;btnEffectAdd.Name" xml:space="preserve">
    <value>btnEffectAdd</value>
  </data>
  <data name="&gt;&gt;btnEffectAdd.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnEffectAdd.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnEffectAdd.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="btnEffectRemove.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnEffectRemove.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 72</value>
  </data>
  <data name="btnEffectRemove.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 24</value>
  </data>
  <data name="btnEffectRemove.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="btnEffectRemove.ToolTip" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="&gt;&gt;btnEffectRemove.Name" xml:space="preserve">
    <value>btnEffectRemove</value>
  </data>
  <data name="&gt;&gt;btnEffectRemove.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnEffectRemove.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnEffectRemove.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="btnEffectDuplicate.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnEffectDuplicate.Location" type="System.Drawing.Point, System.Drawing">
    <value>238, 72</value>
  </data>
  <data name="btnEffectDuplicate.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 24</value>
  </data>
  <data name="btnEffectDuplicate.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="btnEffectDuplicate.ToolTip" xml:space="preserve">
    <value>Duplicate</value>
  </data>
  <data name="&gt;&gt;btnEffectDuplicate.Name" xml:space="preserve">
    <value>btnEffectDuplicate</value>
  </data>
  <data name="&gt;&gt;btnEffectDuplicate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnEffectDuplicate.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnEffectDuplicate.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="btnEffectClear.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnEffectClear.Location" type="System.Drawing.Point, System.Drawing">
    <value>265, 72</value>
  </data>
  <data name="btnEffectClear.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 24</value>
  </data>
  <data name="btnEffectClear.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="btnEffectClear.ToolTip" xml:space="preserve">
    <value>Clear...</value>
  </data>
  <data name="&gt;&gt;btnEffectClear.Name" xml:space="preserve">
    <value>btnEffectClear</value>
  </data>
  <data name="&gt;&gt;btnEffectClear.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnEffectClear.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnEffectClear.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="btnEffectRefresh.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnEffectRefresh.Location" type="System.Drawing.Point, System.Drawing">
    <value>292, 72</value>
  </data>
  <data name="btnEffectRefresh.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 24</value>
  </data>
  <data name="btnEffectRefresh.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="btnEffectRefresh.ToolTip" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="&gt;&gt;btnEffectRefresh.Name" xml:space="preserve">
    <value>btnEffectRefresh</value>
  </data>
  <data name="&gt;&gt;btnEffectRefresh.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnEffectRefresh.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnEffectRefresh.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="pgSettings.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left</value>
  </data>
  <data name="pgSettings.HelpVisible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="pgSettings.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 56</value>
  </data>
  <data name="pgSettings.Size" type="System.Drawing.Size, System.Drawing">
    <value>288, 488</value>
  </data>
  <data name="pgSettings.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="&gt;&gt;pgSettings.Name" xml:space="preserve">
    <value>pgSettings</value>
  </data>
  <data name="&gt;&gt;pgSettings.Type" xml:space="preserve">
    <value>System.Windows.Forms.PropertyGrid, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pgSettings.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pgSettings.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="btnImageEffects.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="btnImageEffects.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnImageEffects.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 552</value>
  </data>
  <data name="btnImageEffects.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="btnImageEffects.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="btnImageEffects.Text" xml:space="preserve">
    <value>Image effects...</value>
  </data>
  <data name="&gt;&gt;btnImageEffects.Name" xml:space="preserve">
    <value>btnImageEffects</value>
  </data>
  <data name="&gt;&gt;btnImageEffects.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnImageEffects.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnImageEffects.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lblEffectName.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblEffectName.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblEffectName.Location" type="System.Drawing.Point, System.Drawing">
    <value>381, 8</value>
  </data>
  <data name="lblEffectName.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 13</value>
  </data>
  <data name="lblEffectName.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="lblEffectName.Text" xml:space="preserve">
    <value>Effect name:</value>
  </data>
  <data name="&gt;&gt;lblEffectName.Name" xml:space="preserve">
    <value>lblEffectName</value>
  </data>
  <data name="&gt;&gt;lblEffectName.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblEffectName.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblEffectName.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txtEffectName.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 24</value>
  </data>
  <data name="txtEffectName.Size" type="System.Drawing.Size, System.Drawing">
    <value>288, 20</value>
  </data>
  <data name="txtEffectName.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;txtEffectName.Name" xml:space="preserve">
    <value>txtEffectName</value>
  </data>
  <data name="&gt;&gt;txtEffectName.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtEffectName.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtEffectName.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="pbResult.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="pbResult.Location" type="System.Drawing.Point, System.Drawing">
    <value>680, 8</value>
  </data>
  <data name="pbResult.Size" type="System.Drawing.Size, System.Drawing">
    <value>536, 536</value>
  </data>
  <data name="pbResult.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="&gt;&gt;pbResult.Name" xml:space="preserve">
    <value>pbResult</value>
  </data>
  <data name="&gt;&gt;pbResult.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.MyPictureBox, ShareX.HelpersLib, Version=15.0.1.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;pbResult.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pbResult.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="lvPresets.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left</value>
  </data>
  <data name="chPreset.Width" type="System.Int32, mscorlib">
    <value>164</value>
  </data>
  <data name="lvPresets.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 53</value>
  </data>
  <data name="lvPresets.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 491</value>
  </data>
  <data name="lvPresets.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;lvPresets.Name" xml:space="preserve">
    <value>lvPresets</value>
  </data>
  <data name="&gt;&gt;lvPresets.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.MyListView, ShareX.HelpersLib, Version=15.0.1.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;lvPresets.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lvPresets.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="mbLoadImage.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="mbLoadImage.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="mbLoadImage.Location" type="System.Drawing.Point, System.Drawing">
    <value>264, 552</value>
  </data>
  <data name="mbLoadImage.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 24</value>
  </data>
  <data name="mbLoadImage.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="mbLoadImage.Text" xml:space="preserve">
    <value>Load image</value>
  </data>
  <data name="mbLoadImage.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;mbLoadImage.Name" xml:space="preserve">
    <value>mbLoadImage</value>
  </data>
  <data name="&gt;&gt;mbLoadImage.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.MenuButton, ShareX.HelpersLib, Version=15.0.1.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;mbLoadImage.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;mbLoadImage.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="lvEffects.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left</value>
  </data>
  <data name="chEffect.Width" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="lvEffects.Location" type="System.Drawing.Point, System.Drawing">
    <value>184, 101</value>
  </data>
  <data name="lvEffects.Size" type="System.Drawing.Size, System.Drawing">
    <value>192, 443</value>
  </data>
  <data name="lvEffects.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;lvEffects.Name" xml:space="preserve">
    <value>lvEffects</value>
  </data>
  <data name="&gt;&gt;lvEffects.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.MyListView, ShareX.HelpersLib, Version=15.0.1.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;lvEffects.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lvEffects.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>64</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>96, 96</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1224, 585</value>
  </data>
  <data name="$this.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>600, 400</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Image effects</value>
  </data>
  <data name="&gt;&gt;tsmiLoadImageFromFile.Name" xml:space="preserve">
    <value>tsmiLoadImageFromFile</value>
  </data>
  <data name="&gt;&gt;tsmiLoadImageFromFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiLoadImageFromClipboard.Name" xml:space="preserve">
    <value>tsmiLoadImageFromClipboard</value>
  </data>
  <data name="&gt;&gt;tsmiLoadImageFromClipboard.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ttMain.Name" xml:space="preserve">
    <value>ttMain</value>
  </data>
  <data name="&gt;&gt;ttMain.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chPreset.Name" xml:space="preserve">
    <value>chPreset</value>
  </data>
  <data name="&gt;&gt;chPreset.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chEffect.Name" xml:space="preserve">
    <value>chEffect</value>
  </data>
  <data name="&gt;&gt;chEffect.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>ImageEffectsForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
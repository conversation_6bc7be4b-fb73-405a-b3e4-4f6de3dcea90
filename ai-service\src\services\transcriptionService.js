const OpenAI = require('openai');
const ffmpeg = require('fluent-ffmpeg');
const fs = require('fs').promises;
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'transcription-service' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: '/app/logs/transcription.log' })
  ]
});

class TranscriptionService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.tempDir = '/app/storage/temp';
    this.maxFileSize = 25 * 1024 * 1024; // 25MB limite do Whisper
  }

  /**
   * Transcrever gravação de vídeo/áudio
   */
  async transcribeRecording(videoPath, options = {}) {
    const startTime = Date.now();
    const sessionId = uuidv4();
    
    try {
      logger.info(`Iniciando transcrição [${sessionId}]`, { videoPath, options });

      // Verificar se arquivo existe
      await fs.access(videoPath);
      
      // Extrair áudio do vídeo
      const audioPath = await this.extractAudio(videoPath, sessionId);
      
      // Verificar tamanho do arquivo
      const stats = await fs.stat(audioPath);
      if (stats.size > this.maxFileSize) {
        throw new Error(`Arquivo muito grande: ${stats.size} bytes. Máximo: ${this.maxFileSize} bytes`);
      }

      // Transcrição com OpenAI Whisper
      const transcription = await this.openai.audio.transcriptions.create({
        file: await fs.readFile(audioPath),
        model: "whisper-1",
        language: options.language || "pt",
        prompt: options.prompt || "Esta é uma gravação de cartório com termos jurídicos brasileiros.",
        response_format: "verbose_json",
        temperature: 0.1
      });

      // Limpar arquivo temporário
      await fs.unlink(audioPath).catch(() => {});

      const result = {
        sessionId,
        text: transcription.text,
        language: transcription.language,
        duration: transcription.duration,
        segments: transcription.segments || [],
        confidence: this.calculateConfidence(transcription),
        wordCount: transcription.text.split(' ').length,
        processingTime: Date.now() - startTime,
        metadata: {
          model: "whisper-1",
          prompt: options.prompt,
          temperature: 0.1
        }
      };

      logger.info(`Transcrição concluída [${sessionId}]`, {
        duration: result.duration,
        wordCount: result.wordCount,
        confidence: result.confidence,
        processingTime: result.processingTime
      });

      return result;

    } catch (error) {
      logger.error(`Erro na transcrição [${sessionId}]:`, error);
      throw new Error(`Falha na transcrição: ${error.message}`);
    }
  }

  /**
   * Extrair áudio de arquivo de vídeo
   */
  async extractAudio(videoPath, sessionId) {
    return new Promise((resolve, reject) => {
      const audioPath = path.join(this.tempDir, `audio_${sessionId}.wav`);
      
      ffmpeg(videoPath)
        .toFormat('wav')
        .audioChannels(1)
        .audioFrequency(16000)
        .audioBitrate('16k')
        .on('end', () => {
          logger.info(`Áudio extraído: ${audioPath}`);
          resolve(audioPath);
        })
        .on('error', (error) => {
          logger.error('Erro ao extrair áudio:', error);
          reject(new Error(`Falha na extração de áudio: ${error.message}`));
        })
        .save(audioPath);
    });
  }

  /**
   * Calcular confiança da transcrição
   */
  calculateConfidence(transcription) {
    if (!transcription.segments || transcription.segments.length === 0) {
      return 0.8; // Valor padrão
    }

    const avgConfidence = transcription.segments.reduce((sum, segment) => {
      return sum + (segment.avg_logprob || 0);
    }, 0) / transcription.segments.length;

    // Converter log probability para porcentagem
    return Math.max(0, Math.min(1, Math.exp(avgConfidence)));
  }

  /**
   * Obter duração de arquivo de áudio
   */
  async getAudioDuration(filePath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err);
        } else {
          resolve(metadata.format.duration);
        }
      });
    });
  }

  /**
   * Transcrever em lote
   */
  async transcribeBatch(filePaths, options = {}) {
    const results = [];
    
    for (const filePath of filePaths) {
      try {
        const result = await this.transcribeRecording(filePath, options);
        results.push({
          filePath,
          success: true,
          result
        });
      } catch (error) {
        results.push({
          filePath,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Validar arquivo de entrada
   */
  async validateInputFile(filePath) {
    try {
      await fs.access(filePath);
      const stats = await fs.stat(filePath);
      
      if (stats.size === 0) {
        throw new Error('Arquivo vazio');
      }

      if (stats.size > this.maxFileSize) {
        throw new Error(`Arquivo muito grande: ${stats.size} bytes`);
      }

      return true;
    } catch (error) {
      throw new Error(`Arquivo inválido: ${error.message}`);
    }
  }
}

module.exports = TranscriptionService;

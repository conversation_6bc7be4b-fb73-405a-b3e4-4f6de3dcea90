import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import 'home_screen.dart';
import 'package:intl/intl.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  bool _isLoading = false;

  Future<void> _selectPlan(String planId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final appProvider = context.read<AppProvider>();
      final success = await appProvider.updateSubscription(planId);

      if (success) {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Assinatura ativada com sucesso!'),
            backgroundColor: Colors.green,
          ),
        );

        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => HomeScreen()),
        );
      } else {
        if (!mounted) return;

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Falha ao ativar assinatura. Tente novamente.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    final formatter = DateFormat('dd/MM/yyyy');
    final hasSubscription = appProvider.subscriptionEndDate != null;
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Assinatura'),
        automaticallyImplyLeading: appProvider.hasActiveSubscription,
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : SafeArea(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Status da assinatura
                      if (hasSubscription) ...[
                        Card(
                          color: appProvider.hasActiveSubscription
                              ? Colors.green[100]
                              : Colors.red[100],
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              children: [
                                Text(
                                  appProvider.hasActiveSubscription
                                      ? 'Assinatura Ativa'
                                      : 'Assinatura Expirada',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: appProvider.hasActiveSubscription
                                        ? Colors.green[800]
                                        : Colors.red[800],
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Válida até: ${formatter.format(appProvider.subscriptionEndDate!)}',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: appProvider.hasActiveSubscription
                                        ? Colors.green[800]
                                        : Colors.red[800],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: 24),
                        Text(
                          appProvider.hasActiveSubscription
                              ? 'Renovar Assinatura'
                              : 'Reativar Assinatura',
                          style: Theme.of(context).textTheme.titleLarge,
                          textAlign: TextAlign.center,
                        ),
                      ] else ...[
                        Center(
                          child: Column(
                            children: [
                              Icon(
                                Icons.card_membership,
                                size: 80,
                                color: Theme.of(context).primaryColor,
                              ),
                              SizedBox(height: 16),
                              Text(
                                'Escolha seu plano',
                                style: Theme.of(context).textTheme.headlineSmall,
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Para usar o aplicativo, é necessário assinar um plano',
                                textAlign: TextAlign.center,
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                            ],
                          ),
                        ),
                      ],
                      SizedBox(height: 32),
                      // Lista de planos disponíveis
                      _buildSubscriptionPlan(
                        title: 'Plano Mensal',
                        price: 'R\$ 29,90',
                        features: [
                          'Gravações ilimitadas',
                          'Duração máxima de 30 minutos por gravação',
                          'Relatórios PDF para cartório',
                        ],
                        planId: 'monthly',
                      ),
                      SizedBox(height: 16),
                      _buildSubscriptionPlan(
                        title: 'Plano Trimestral',
                        price: 'R\$ 79,90',
                        features: [
                          'Gravações ilimitadas',
                          'Duração máxima de 2 horas por gravação',
                          'Relatórios PDF para cartório',
                          'Economia de 10%',
                        ],
                        isRecommended: true,
                        planId: 'quarterly',
                      ),
                      SizedBox(height: 16),
                      _buildSubscriptionPlan(
                        title: 'Plano Anual',
                        price: 'R\$ 249,90',
                        features: [
                          'Gravações ilimitadas',
                          'Sem limite de duração de gravação',
                          'Relatórios PDF para cartório',
                          'Economia de 30%',
                          'Suporte prioritário',
                        ],
                        planId: 'yearly',
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildSubscriptionPlan({
    required String title,
    required String price,
    required List<String> features,
    required String planId,
    bool isRecommended = false,
  }) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isRecommended
            ? BorderSide(color: Theme.of(context).primaryColor, width: 2)
            : BorderSide.none,
      ),
      elevation: isRecommended ? 4 : 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (isRecommended) ...[
              Container(
                padding: EdgeInsets.symmetric(vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Mais Popular',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              SizedBox(height: 12),
            ],
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              price,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            SizedBox(height: 16),
            ...features.map((feature) => Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Expanded(child: Text(feature)),
                    ],
                  ),
                )),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _selectPlan(planId),
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 12),
                backgroundColor: isRecommended
                    ? Theme.of(context).primaryColor
                    : null,
                foregroundColor: isRecommended
                    ? Colors.white
                    : null,
              ),
              child: Text('Assinar'),
            ),
          ],
        ),
      ),
    );
  }
}

name: Build ShareX (PR)

on:
  pull_request:
    branches:
      - "**"

permissions:
  contents: read

jobs:
  build:
    name: Build
    runs-on: windows-latest

    strategy:
      fail-fast: false
      matrix:
        configuration:
          - Release
          - Debug
          - Steam
          - MicrosoftStore
          - MicrosoftStoreDebug
        platform:
          - Any CPU

    env:
      SOLUTION_FILE_PATH: ShareX.sln

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: 9.0.x

      - name: Restore NuGet packages
        run: dotnet restore --runtime win-x64 "${{ env.SOLUTION_FILE_PATH }}"

      - name: Build
        run: dotnet build --no-restore --configuration "${{ matrix.configuration }}" --self-contained true /m:1 "${{ env.SOLUTION_FILE_PATH }}"
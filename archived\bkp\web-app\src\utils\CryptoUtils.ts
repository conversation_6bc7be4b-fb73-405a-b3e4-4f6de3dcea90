import CryptoJS from 'crypto-js';

/**
 * Classe para funções de criptografia no cliente
 */
class CryptoUtils {
  /**
   * Gera um hash SHA-256 para um arquivo/blob
   */
  static async generateFileHash(file: File | Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        const reader = new FileReader();
        reader.readAsArrayBuffer(file);
        reader.onloadend = function () {
          const arrayBuffer = reader.result as ArrayBuffer;
          const hash = CryptoJS.SHA256(CryptoJS.lib.WordArray.create(arrayBuffer));
          resolve(hash.toString());
        };
        reader.onerror = function () {
          reject(new Error('Erro ao ler o arquivo para geração de hash'));
        };
      } catch (error) {
        reject(error);
      }
    });
  }
  
  /**
   * Gera um hash SHA-256 para uma string
   */
  static generateStringHash(data: string): string {
    return CryptoJS.SHA256(data).toString();
  }
  
  /**
   * Criptografa dados usando AES
   */
  static encrypt(data: string, key: string): string {
    return CryptoJS.AES.encrypt(data, key).toString();
  }
  
  /**
   * Descriptografa dados usando AES
   */
  static decrypt(encryptedData: string, key: string): string | null {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, key);
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (e) {
      console.error('Erro ao descriptografar:', e);
      return null;
    }
  }
  
  /**
   * Criptografa um objeto usando AES
   */
  static encryptObject(obj: any, key: string): string {
    const jsonString = JSON.stringify(obj);
    return this.encrypt(jsonString, key);
  }
  
  /**
   * Descriptografa um objeto usando AES
   */
  static decryptObject<T>(encryptedData: string, key: string): T | null {
    try {
      const jsonString = this.decrypt(encryptedData, key);
      if (!jsonString) return null;
      return JSON.parse(jsonString) as T;
    } catch (e) {
      console.error('Erro ao descriptografar objeto:', e);
      return null;
    }
  }
  
  /**
   * Gera uma chave aleatória para criptografia
   */
  static generateRandomKey(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    const randomValues = new Uint8Array(length);
    window.crypto.getRandomValues(randomValues);
    randomValues.forEach(value => {
      result += chars.charAt(value % chars.length);
    });
    return result;
  }
}

export default CryptoUtils;

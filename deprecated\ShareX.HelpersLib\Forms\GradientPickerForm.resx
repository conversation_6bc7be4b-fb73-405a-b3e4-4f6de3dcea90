﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ilColors.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="ilColors.ImageSize" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnAdd.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnAdd.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 8</value>
  </data>
  <data name="btnAdd.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 23</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnAdd.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnAdd.Text" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="&gt;&gt;btnAdd.Name" xml:space="preserve">
    <value>btnAdd</value>
  </data>
  <data name="&gt;&gt;btnAdd.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnAdd.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnAdd.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="btnRemove.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnRemove.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 32</value>
  </data>
  <data name="btnRemove.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 23</value>
  </data>
  <data name="btnRemove.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnRemove.Text" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="&gt;&gt;btnRemove.Name" xml:space="preserve">
    <value>btnRemove</value>
  </data>
  <data name="&gt;&gt;btnRemove.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnRemove.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnRemove.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="nudLocation.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 144</value>
  </data>
  <data name="nudLocation.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 20</value>
  </data>
  <data name="nudLocation.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="nudLocation.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudLocation.Name" xml:space="preserve">
    <value>nudLocation</value>
  </data>
  <data name="&gt;&gt;nudLocation.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudLocation.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudLocation.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="lblLocation.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblLocation.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblLocation.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 128</value>
  </data>
  <data name="lblLocation.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="lblLocation.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="lblLocation.Text" xml:space="preserve">
    <value>Location:</value>
  </data>
  <data name="&gt;&gt;lblLocation.Name" xml:space="preserve">
    <value>lblLocation</value>
  </data>
  <data name="&gt;&gt;lblLocation.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblLocation.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblLocation.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="cbGradientType.Location" type="System.Drawing.Point, System.Drawing">
    <value>88, 176</value>
  </data>
  <data name="cbGradientType.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 21</value>
  </data>
  <data name="cbGradientType.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;cbGradientType.Name" xml:space="preserve">
    <value>cbGradientType</value>
  </data>
  <data name="&gt;&gt;cbGradientType.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbGradientType.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbGradientType.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="lblGradientType.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblGradientType.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblGradientType.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 180</value>
  </data>
  <data name="lblGradientType.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 13</value>
  </data>
  <data name="lblGradientType.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="lblGradientType.Text" xml:space="preserve">
    <value>Gradient type:</value>
  </data>
  <data name="&gt;&gt;lblGradientType.Name" xml:space="preserve">
    <value>lblGradientType</value>
  </data>
  <data name="&gt;&gt;lblGradientType.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblGradientType.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblGradientType.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="btnOK.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 280</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 23</value>
  </data>
  <data name="btnOK.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="&gt;&gt;btnOK.Name" xml:space="preserve">
    <value>btnOK</value>
  </data>
  <data name="&gt;&gt;btnOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOK.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnOK.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="btnCancel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>112, 280</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 23</value>
  </data>
  <data name="btnCancel.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Name" xml:space="preserve">
    <value>btnCancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnCancel.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="pbPreview.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="pbPreview.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 224</value>
  </data>
  <data name="pbPreview.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 50</value>
  </data>
  <data name="pbPreview.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;pbPreview.Name" xml:space="preserve">
    <value>pbPreview</value>
  </data>
  <data name="&gt;&gt;pbPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.PictureBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pbPreview.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pbPreview.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lblPreview.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblPreview.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblPreview.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 206</value>
  </data>
  <data name="lblPreview.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="lblPreview.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="lblPreview.Text" xml:space="preserve">
    <value>Preview:</value>
  </data>
  <data name="&gt;&gt;lblPreview.Name" xml:space="preserve">
    <value>lblPreview</value>
  </data>
  <data name="&gt;&gt;lblPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblPreview.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblPreview.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="btnReverse.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnReverse.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 80</value>
  </data>
  <data name="btnReverse.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 23</value>
  </data>
  <data name="btnReverse.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="btnReverse.Text" xml:space="preserve">
    <value>Reverse</value>
  </data>
  <data name="&gt;&gt;btnReverse.Name" xml:space="preserve">
    <value>btnReverse</value>
  </data>
  <data name="&gt;&gt;btnReverse.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnReverse.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnReverse.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="lblPresets.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblPresets.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblPresets.Location" type="System.Drawing.Point, System.Drawing">
    <value>213, 8</value>
  </data>
  <data name="lblPresets.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="lblPresets.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="lblPresets.Text" xml:space="preserve">
    <value>Presets:</value>
  </data>
  <data name="&gt;&gt;lblPresets.Name" xml:space="preserve">
    <value>lblPresets</value>
  </data>
  <data name="&gt;&gt;lblPresets.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblPresets.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblPresets.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="ilPresets.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>109, 17</value>
  </metadata>
  <data name="ilPresets.ImageSize" type="System.Drawing.Size, System.Drawing">
    <value>64, 64</value>
  </data>
  <data name="chGradient.Text" xml:space="preserve">
    <value>Gradient</value>
  </data>
  <data name="chGradient.Width" type="System.Int32, mscorlib">
    <value>140</value>
  </data>
  <data name="lvPresets.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 24</value>
  </data>
  <data name="lvPresets.Size" type="System.Drawing.Size, System.Drawing">
    <value>376, 280</value>
  </data>
  <data name="lvPresets.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;lvPresets.Name" xml:space="preserve">
    <value>lvPresets</value>
  </data>
  <data name="&gt;&gt;lvPresets.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lvPresets.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lvPresets.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnClear.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnClear.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 56</value>
  </data>
  <data name="btnClear.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 23</value>
  </data>
  <data name="btnClear.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="btnClear.Text" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="&gt;&gt;btnClear.Name" xml:space="preserve">
    <value>btnClear</value>
  </data>
  <data name="&gt;&gt;btnClear.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnClear.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnClear.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cbtnCurrentColor.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="cbtnCurrentColor.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 104</value>
  </data>
  <data name="cbtnCurrentColor.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 23</value>
  </data>
  <data name="cbtnCurrentColor.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="cbtnCurrentColor.Text" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="&gt;&gt;cbtnCurrentColor.Name" xml:space="preserve">
    <value>cbtnCurrentColor</value>
  </data>
  <data name="&gt;&gt;cbtnCurrentColor.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.ColorButton, ShareX.HelpersLib, Version=15.0.1.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;cbtnCurrentColor.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbtnCurrentColor.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="chLocation.Text" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="chLocation.Width" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="lvGradientPoints.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 8</value>
  </data>
  <data name="lvGradientPoints.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 160</value>
  </data>
  <data name="lvGradientPoints.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;lvGradientPoints.Name" xml:space="preserve">
    <value>lvGradientPoints</value>
  </data>
  <data name="&gt;&gt;lvGradientPoints.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.MyListView, ShareX.HelpersLib, Version=15.0.1.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;lvGradientPoints.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lvGradientPoints.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>96, 96</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>600, 312</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Gradient picker</value>
  </data>
  <data name="&gt;&gt;ilColors.Name" xml:space="preserve">
    <value>ilColors</value>
  </data>
  <data name="&gt;&gt;ilColors.Type" xml:space="preserve">
    <value>System.Windows.Forms.ImageList, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ilPresets.Name" xml:space="preserve">
    <value>ilPresets</value>
  </data>
  <data name="&gt;&gt;ilPresets.Type" xml:space="preserve">
    <value>System.Windows.Forms.ImageList, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chGradient.Name" xml:space="preserve">
    <value>chGradient</value>
  </data>
  <data name="&gt;&gt;chGradient.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chLocation.Name" xml:space="preserve">
    <value>chLocation</value>
  </data>
  <data name="&gt;&gt;chLocation.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>GradientPickerForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
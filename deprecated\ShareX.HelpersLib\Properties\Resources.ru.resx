﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ShapeType_RegionFreehand" xml:space="preserve">
    <value>Область от руки</value>
  </data>
  <data name="ReplCodeMenuEntry_w_Current_week_name__Local_language_" xml:space="preserve">
    <value>Текущий день недели (Местный язык)</value>
  </data>
  <data name="ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_" xml:space="preserve">
    <value>Настройки скопированы в буфер обмена.</value>
  </data>
  <data name="ImgurThumbnailType_Big_Square" xml:space="preserve">
    <value>Большой квадрат</value>
  </data>
  <data name="ReplCodeMenuEntry_s_Current_second" xml:space="preserve">
    <value>Текущая секунда</value>
  </data>
  <data name="TextDestination_CustomTextUploader" xml:space="preserve">
    <value>Пользовательский сервис загрузки текста</value>
  </data>
  <data name="ProxyMethod_None" xml:space="preserve">
    <value>Обойтись без прокси</value>
  </data>
  <data name="ReplCodeMenuEntry_mo_Current_month" xml:space="preserve">
    <value>Текущий месяц</value>
  </data>
  <data name="CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___" xml:space="preserve">
    <value>Обзор каскадных таблиц стилей...</value>
  </data>
  <data name="Extensions_AddContextMenu_Redo" xml:space="preserve">
    <value>Повтор</value>
  </data>
  <data name="HotkeyType_VideoThumbnailer" xml:space="preserve">
    <value>Миниатюры из видео</value>
  </data>
  <data name="ShapeType_EffectBlur" xml:space="preserve">
    <value>Размытие (B)</value>
  </data>
  <data name="AfterCaptureTasks_ShowQuickTaskMenu" xml:space="preserve">
    <value>Открыть меню быстрых задач</value>
  </data>
  <data name="CustomUploaderDestinationType_URLShortener" xml:space="preserve">
    <value>Сервис коротких ссылок</value>
  </data>
  <data name="ReplCodeMenuEntry_uln_User_login_name" xml:space="preserve">
    <value>Логин пользователя</value>
  </data>
  <data name="HotkeyType_ImageEffects" xml:space="preserve">
    <value>Эффекты изображений</value>
  </data>
  <data name="ShapeType_DrawingImageScreen" xml:space="preserve">
    <value>Картинка с экрана</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available" xml:space="preserve">
    <value>Доступна новая версия {0}</value>
  </data>
  <data name="AfterUploadTasks_ShowQRCode" xml:space="preserve">
    <value>Показать окно с QR-кодом</value>
  </data>
  <data name="ShapeType_DrawingSpeechBalloon" xml:space="preserve">
    <value>Сноска (S)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date" xml:space="preserve">
    <value>{0} в актуальном состоянии</value>
  </data>
  <data name="HotkeyType_Category_ScreenRecord" xml:space="preserve">
    <value>Запись экрана</value>
  </data>
  <data name="PastebinExpiration_H1" xml:space="preserve">
    <value>1 Час</value>
  </data>
  <data name="HotkeyType_ScrollingCapture" xml:space="preserve">
    <value>Захват с прокруткой</value>
  </data>
  <data name="ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all" xml:space="preserve">
    <value>Алфавитно-цифровой счетчик с учетом регистра. Нули слева, {n} задает длину</value>
  </data>
  <data name="ReplCodeMenuEntry_t_Title_of_active_window" xml:space="preserve">
    <value>Заголовок активного окна</value>
  </data>
  <data name="AfterCaptureTasks_SendImageToPrinter" xml:space="preserve">
    <value>Распечатать изображение</value>
  </data>
  <data name="ShapeType_RegionRectangle" xml:space="preserve">
    <value>Прямоугольная область</value>
  </data>
  <data name="HotkeyType_ToggleActionsToolbar" xml:space="preserve">
    <value>Переключить панель действий</value>
  </data>
  <data name="AfterCaptureTasks_PerformActions" xml:space="preserve">
    <value>Выполнить действия</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfCanvas" xml:space="preserve">
    <value>Процент полотна</value>
  </data>
  <data name="ReplCodeMenuCategory_Date_and_Time" xml:space="preserve">
    <value>Дата и время</value>
  </data>
  <data name="HotkeyType_ImageCombiner" xml:space="preserve">
    <value>Объединитель изображений</value>
  </data>
  <data name="HotkeyType_RectangleTransparent" xml:space="preserve">
    <value>Захват области (Прозрачный фон)</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_" xml:space="preserve">
    <value>Загрузка завершена.</value>
  </data>
  <data name="YouTubeVideoPrivacy_Private" xml:space="preserve">
    <value>Ограниченный доступ</value>
  </data>
  <data name="AfterUploadTasks_ShareURL" xml:space="preserve">
    <value>Отправить ссылку</value>
  </data>
  <data name="CustomUploaderDestinationType_FileUploader" xml:space="preserve">
    <value>Файловый сервис</value>
  </data>
  <data name="ReplCodeMenuEntry_h_Current_hour" xml:space="preserve">
    <value>Текущий час</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_Update_check_failed" xml:space="preserve">
    <value>Проверка обновлений не удалась</value>
  </data>
  <data name="ReplCodeMenuEntry_ms_Current_millisecond" xml:space="preserve">
    <value>Текущая миллисекунда</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Install" xml:space="preserve">
    <value>Установить</value>
  </data>
  <data name="AfterCaptureTasks_UploadImageToHost" xml:space="preserve">
    <value>Загрузить изображение на сервер</value>
  </data>
  <data name="ReplCodeMenuEntry_ix_Auto_increment_hexadecimal" xml:space="preserve">
    <value>Шестнадцатеричный счетчик. Нули слева, {n} задает длину</value>
  </data>
  <data name="CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__" xml:space="preserve">
    <value>Голубой: {0:0.0}%, Пурпурный: {1:0.0}%, Жёлтый: {2:0.0}%, Ключ: {3:0.0}%</value>
  </data>
  <data name="HotkeyType_FolderUpload" xml:space="preserve">
    <value>Загрузка папки</value>
  </data>
  <data name="ReplCodeMenuEntry_mi_Current_minute" xml:space="preserve">
    <value>Текущая минута</value>
  </data>
  <data name="ShapeType_EffectPixelate" xml:space="preserve">
    <value>Пикселизация (P)</value>
  </data>
  <data name="ReplCodeMenuEntry_d_Current_day" xml:space="preserve">
    <value>Сегодняшний день</value>
  </data>
  <data name="PastebinExpiration_D1" xml:space="preserve">
    <value>1 День</value>
  </data>
  <data name="ShapeType_DrawingArrow" xml:space="preserve">
    <value>Стрелка (A)</value>
  </data>
  <data name="ShapeType_DrawingSmartEraser" xml:space="preserve">
    <value>Умный ластик</value>
  </data>
  <data name="PastebinPrivacy_Unlisted" xml:space="preserve">
    <value>Не публиковать</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_update_is_available" xml:space="preserve">
    <value>Доступно обновление</value>
  </data>
  <data name="HotkeyType_Category_Upload" xml:space="preserve">
    <value>Загрузка</value>
  </data>
  <data name="Extensions_AddContextMenu_Cut" xml:space="preserve">
    <value>Вырезать</value>
  </data>
  <data name="FileExistAction_Cancel" xml:space="preserve">
    <value>Не сохранять</value>
  </data>
  <data name="AfterCaptureTasks_CopyImageToClipboard" xml:space="preserve">
    <value>Скопировать изображение в буфер обмена</value>
  </data>
  <data name="PNGBitDepth_Bit32" xml:space="preserve">
    <value>32 бит</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFActiveWindow" xml:space="preserve">
    <value>Начать/Остановить запись области экрана с активным окном (GIF)</value>
  </data>
  <data name="HotkeyType_PrintScreen" xml:space="preserve">
    <value>Захват всего экрана</value>
  </data>
  <data name="ImageEditorStartMode_Normal" xml:space="preserve">
    <value>Нормальный</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFCustomRegion" xml:space="preserve">
    <value>Начать/Остановить запись подготовленной области экрана (GIF)</value>
  </data>
  <data name="HotkeyType_CustomRegion" xml:space="preserve">
    <value>Захват подготовленной области экрана</value>
  </data>
  <data name="ReplCodeMenuCategory_Image" xml:space="preserve">
    <value>Изображение</value>
  </data>
  <data name="PastebinExpiration_M10" xml:space="preserve">
    <value>10 Минут</value>
  </data>
  <data name="RegionCaptureAction_SwapToolType" xml:space="preserve">
    <value>Сменить тип инструмента</value>
  </data>
  <data name="HotkeyType_RectangleRegion" xml:space="preserve">
    <value>Захват области</value>
  </data>
  <data name="AfterCaptureTasks_DoOCR" xml:space="preserve">
    <value>Распознать текст (OCR)</value>
  </data>
  <data name="HotkeyType_ExitShareX" xml:space="preserve">
    <value>Выйти из ShareX</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_Portable" xml:space="preserve">
    <value>Доступна более новая версия {0}.
Хотите загрузить ее?</value>
  </data>
  <data name="Helpers_DownloadString_Download_failed_" xml:space="preserve">
    <value>Загрузка не удалась:</value>
  </data>
  <data name="ShapeType_DrawingTextOutline" xml:space="preserve">
    <value>Текст с обводкой (O)</value>
  </data>
  <data name="RegionCaptureAction_CaptureActiveMonitor" xml:space="preserve">
    <value>Захват активного монитора</value>
  </data>
  <data name="ImgurThumbnailType_Small_Thumbnail" xml:space="preserve">
    <value>Маленькая миниатюра</value>
  </data>
  <data name="PrintForm_LoadSettings_Print" xml:space="preserve">
    <value>Печать</value>
  </data>
  <data name="GIFQuality_Bit4" xml:space="preserve">
    <value>Octree quantizer 16 цветов</value>
  </data>
  <data name="AfterUploadTasks_ShowAfterUploadWindow" xml:space="preserve">
    <value>Показать окно «После загрузки»</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAdjective" xml:space="preserve">
    <value>Случайное прилагательное</value>
  </data>
  <data name="Extensions_AddContextMenu_SelectAll" xml:space="preserve">
    <value>Выбрать все</value>
  </data>
  <data name="FileDestination_CustomFileUploader" xml:space="preserve">
    <value>Пользовательский сервис загрузки файлов</value>
  </data>
  <data name="LinearGradientMode_Vertical" xml:space="preserve">
    <value>Вертикальный</value>
  </data>
  <data name="ReplCodeMenuCategory_Random" xml:space="preserve">
    <value>Случайные числа</value>
  </data>
  <data name="CustomUploaderDestinationType_ImageUploader" xml:space="preserve">
    <value>Сервис картинок</value>
  </data>
  <data name="HotkeyType_HashCheck" xml:space="preserve">
    <value>Проверка хеша</value>
  </data>
  <data name="HotkeyType_ScreenRecorderActiveWindow" xml:space="preserve">
    <value>Начать/Остановить запись области экрана с активным окном</value>
  </data>
  <data name="ReplCodeMenuEntry_rn_Random_number_0_to_9" xml:space="preserve">
    <value>Случайное число от 0 до 9. Повторение задается через {n}</value>
  </data>
  <data name="HotkeyType_ClipboardUploadWithContentViewer" xml:space="preserve">
    <value>Загрузка из буфера обмена с предпросмотром</value>
  </data>
  <data name="YouTubeVideoPrivacy_Public" xml:space="preserve">
    <value>Открытый доступ</value>
  </data>
  <data name="HSB_ToString_" xml:space="preserve">
    <value>Тон: {0:0.0}°, Насыщенность: {1:0.0}%, Яркость: {2:0.0}%</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_OK" xml:space="preserve">
    <value>ОК</value>
  </data>
  <data name="HotkeyType_DragDropUpload" xml:space="preserve">
    <value>Загрузка перетаскиванием</value>
  </data>
  <data name="PastebinExpiration_N" xml:space="preserve">
    <value>Никогда</value>
  </data>
  <data name="HotkeyType_StartScreenRecorder" xml:space="preserve">
    <value>Начать/Остановить запись последней области экрана</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Yes" xml:space="preserve">
    <value>Да</value>
  </data>
  <data name="HotkeyType_ImageThumbnailer" xml:space="preserve">
    <value>Миниатюры изображений</value>
  </data>
  <data name="ReplCodeMenuEntry_mon_Current_month_name__Local_language_" xml:space="preserve">
    <value>Имя текущего месяца (Местный язык)</value>
  </data>
  <data name="GIFQuality_Bit8" xml:space="preserve">
    <value>Octree quantizer 256 цветов (Медленно с лучшим качеством)</value>
  </data>
  <data name="ShapeType_DrawingImage" xml:space="preserve">
    <value>Картинка из файла</value>
  </data>
  <data name="ScreenRecordGIFEncoding_NET" xml:space="preserve">
    <value>.NET (Плохое качество)</value>
  </data>
  <data name="ReplCodeMenuEntry_ia_Auto_increment_alphanumeric" xml:space="preserve">
    <value>Алфавитно-цифровой счетчик. Нули слева, {n} задает длину</value>
  </data>
  <data name="AfterCaptureTasks_AddImageEffects" xml:space="preserve">
    <value>Добавить эффекты изображений</value>
  </data>
  <data name="AfterCaptureTasks_DeleteFile" xml:space="preserve">
    <value>Удалить локальный файл</value>
  </data>
  <data name="ExportImportControl_Serialize_Export_failed_" xml:space="preserve">
    <value>Экспорт не удался.</value>
  </data>
  <data name="ReplCodeMenuCategory_Computer" xml:space="preserve">
    <value>Компьютер</value>
  </data>
  <data name="FileExistAction_UniqueName" xml:space="preserve">
    <value>Добавить номер к имени файла</value>
  </data>
  <data name="ImgurThumbnailType_Large_Thumbnail" xml:space="preserve">
    <value>Большая миниатюра</value>
  </data>
  <data name="ReplCodeMenuEntry_yy_Current_year__2_digits_" xml:space="preserve">
    <value>Текущий год (2 цифры)</value>
  </data>
  <data name="PNGBitDepth_Automatic" xml:space="preserve">
    <value>Определять автоматически</value>
  </data>
  <data name="ImageEditorStartMode_PreviousState" xml:space="preserve">
    <value>Помнить прошлый</value>
  </data>
  <data name="ShapeType_RegionEllipse" xml:space="preserve">
    <value>Эллиптическая область</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIF" xml:space="preserve">
    <value>Начать/Остановить запись области экрана (GIF)</value>
  </data>
  <data name="YouTubeVideoPrivacy_Unlisted" xml:space="preserve">
    <value>Доступ по ссылке</value>
  </data>
  <data name="ObjectListView_ObjectListView_Name" xml:space="preserve">
    <value>Имя</value>
  </data>
  <data name="ReplCodeMenuCategory_Window" xml:space="preserve">
    <value>Окно</value>
  </data>
  <data name="HotkeyType_Ruler" xml:space="preserve">
    <value>Линейка</value>
  </data>
  <data name="ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from" xml:space="preserve">
    <value>Ссылка для загрузки настроек</value>
  </data>
  <data name="ShapeType_DrawingFreehand" xml:space="preserve">
    <value>От руки (F)</value>
  </data>
  <data name="ReplCodeMenuEntry_pm_Gets_AM_PM" xml:space="preserve">
    <value>Получает AM/PM</value>
  </data>
  <data name="DirectoryNameEditor_EditValue_Browse_for_a_folder___" xml:space="preserve">
    <value>Обзор папок...</value>
  </data>
  <data name="LinearGradientMode_BackwardDiagonal" xml:space="preserve">
    <value>Обратно диагональный</value>
  </data>
  <data name="ShapeType_DrawingCursor" xml:space="preserve">
    <value>Курсор</value>
  </data>
  <data name="ImgurThumbnailType_Huge_Thumbnail" xml:space="preserve">
    <value>Огромная миниатюра</value>
  </data>
  <data name="LinearGradientMode_Horizontal" xml:space="preserve">
    <value>Горизонтальный</value>
  </data>
  <data name="HotkeyType_AbortScreenRecording" xml:space="preserve">
    <value>Отменить запись экрана</value>
  </data>
  <data name="ReplCodeMenuEntry_y_Current_year" xml:space="preserve">
    <value>Текущий год</value>
  </data>
  <data name="PastebinExpiration_W2" xml:space="preserve">
    <value>2 Недели</value>
  </data>
  <data name="ImageEditorStartMode_Fullscreen" xml:space="preserve">
    <value>Полноэкранный</value>
  </data>
  <data name="AfterCaptureTasks_CopyFilePathToClipboard" xml:space="preserve">
    <value>Скопировать путь к файлу в буфер обмена</value>
  </data>
  <data name="HotkeyType_ScreenRecorder" xml:space="preserve">
    <value>Начать/Остановить запись области экрана</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFile" xml:space="preserve">
    <value>Сохранить изображение в файл</value>
  </data>
  <data name="ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_" xml:space="preserve">
    <value>Путь к файлу без расширения + "Расширение итогового файла"</value>
  </data>
  <data name="URLSharingServices_GoogleImageSearch" xml:space="preserve">
    <value>Поиск по картинке в Google</value>
  </data>
  <data name="HotkeyType_IndexFolder" xml:space="preserve">
    <value>Индексация папки</value>
  </data>
  <data name="ReplCodeMenuEntry_unix_Unix_timestamp" xml:space="preserve">
    <value>Временная метка UNIX</value>
  </data>
  <data name="ScreenRecordGIFEncoding_FFmpeg" xml:space="preserve">
    <value>FFmpeg (Хорошее качество)</value>
  </data>
  <data name="HotkeyType_TweetMessage" xml:space="preserve">
    <value>Сообщение в Twitter</value>
  </data>
  <data name="DrawImageSizeMode_DontResize" xml:space="preserve">
    <value>Не масштабировать</value>
  </data>
  <data name="HotkeyType_StopUploads" xml:space="preserve">
    <value>Остановка всех активных загрузок</value>
  </data>
  <data name="AfterUploadTasks_OpenURL" xml:space="preserve">
    <value>Перейти по ссылке</value>
  </data>
  <data name="AfterCaptureTasks_AnnotateImage" xml:space="preserve">
    <value>Открыть в редакторе изображений</value>
  </data>
  <data name="MyPictureBox_LoadImageAsync_Loading_image___" xml:space="preserve">
    <value>Загрузка изображения...</value>
  </data>
  <data name="HotkeyType_LastRegion" xml:space="preserve">
    <value>Захват последней области</value>
  </data>
  <data name="Helpers_OpenFolder_Folder_not_exist_" xml:space="preserve">
    <value>Папка не существует:</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_CurrentVersion" xml:space="preserve">
    <value>Текущая версия</value>
  </data>
  <data name="FileDestination_Email" xml:space="preserve">
    <value>Электронная почта</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_" xml:space="preserve">
    <value>Доступна более новая версия {0}.
Хотите скачать и установить?</value>
  </data>
  <data name="WavFileNameEditor_EditValue_Browse_for_a_sound_file___" xml:space="preserve">
    <value>Искать звуковой файл...</value>
  </data>
  <data name="Helpers_OpenFile_File_not_exist_" xml:space="preserve">
    <value>Файл не существует:</value>
  </data>
  <data name="Helpers_BrowseFolder_Choose_folder" xml:space="preserve">
    <value>Выберите папку</value>
  </data>
  <data name="ExportImportControl_Deserialize_Import_failed_" xml:space="preserve">
    <value>Импорт не удался.</value>
  </data>
  <data name="Extensions_AddContextMenu_Delete" xml:space="preserve">
    <value>Удалить</value>
  </data>
  <data name="Extensions_AddContextMenu_Paste" xml:space="preserve">
    <value>Вставить</value>
  </data>
  <data name="HotkeyType_QRCodeDecodeFromScreen" xml:space="preserve">
    <value>QR-код (Считать с экрана)</value>
  </data>
  <data name="LinearGradientMode_ForwardDiagonal" xml:space="preserve">
    <value>Прямо диагональный</value>
  </data>
  <data name="PNGBitDepth_Bit24" xml:space="preserve">
    <value>24 бит</value>
  </data>
  <data name="ReplCodeMenuEntry_wy_Week_of_year" xml:space="preserve">
    <value>Неделя года</value>
  </data>
  <data name="DrawImageSizeMode_AbsoluteSize" xml:space="preserve">
    <value>Абсолютный размер</value>
  </data>
  <data name="HotkeyType_OpenImageHistory" xml:space="preserve">
    <value>Открыть окно истории изображений</value>
  </data>
  <data name="ReplCodeMenuCategory_Incremental" xml:space="preserve">
    <value>Счетчик</value>
  </data>
  <data name="RandomEmojiRepeatUsingN" xml:space="preserve">
    <value>Случайный символ эмодзи. Повторение задается через {n}</value>
  </data>
  <data name="AfterCaptureTasks_SaveThumbnailImageToFile" xml:space="preserve">
    <value>Сохранить миниатюру в файл</value>
  </data>
  <data name="DownloaderForm_StartDownload_Downloading_" xml:space="preserve">
    <value>Загрузка...</value>
  </data>
  <data name="RegionCaptureAction_RemoveShapeCancelCapture" xml:space="preserve">
    <value>Убрать фигуру или отменить захват</value>
  </data>
  <data name="ReplCodeMenuEntry_un_User_name" xml:space="preserve">
    <value>Имя пользователя</value>
  </data>
  <data name="ShapeType_DrawingMagnify" xml:space="preserve">
    <value>Увеличение</value>
  </data>
  <data name="CodeMenu_Create_Close" xml:space="preserve">
    <value>Закрыть</value>
  </data>
  <data name="ShapeType_DrawingSticker" xml:space="preserve">
    <value>Стикер</value>
  </data>
  <data name="HotkeyType_QRCode" xml:space="preserve">
    <value>QR-код</value>
  </data>
  <data name="PastebinExpiration_W1" xml:space="preserve">
    <value>1 Неделю</value>
  </data>
  <data name="CustomUploaderDestinationType_URLSharingService" xml:space="preserve">
    <value>Сервис отправки ссылок</value>
  </data>
  <data name="ShapeType_EffectHighlight" xml:space="preserve">
    <value>Подсветить область (H)</value>
  </data>
  <data name="GIFQuality_Grayscale" xml:space="preserve">
    <value>Palette quantizer 256 градаций серого</value>
  </data>
  <data name="GIFQuality_Default" xml:space="preserve">
    <value>Стандартное кодирование .NET (Быстро со средним качеством)</value>
  </data>
  <data name="ReplCodeMenuEntry_rx_Random_hexadecimal" xml:space="preserve">
    <value>Случайный шестнадцатеричный символ. Повторение задается через {n}</value>
  </data>
  <data name="PastebinPrivacy_Private" xml:space="preserve">
    <value>Личная (только участники)</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Ошибка</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAnimal" xml:space="preserve">
    <value>Случайное животное</value>
  </data>
  <data name="URLSharingServices_CustomURLSharingService" xml:space="preserve">
    <value>Пользовательский сервис отправки ссылок</value>
  </data>
  <data name="RegionCaptureAction_CaptureFullscreen" xml:space="preserve">
    <value>Захват всего экрана</value>
  </data>
  <data name="ReplCodeMenuEntry_pn_Process_name_of_active_window" xml:space="preserve">
    <value>Имя процесса активного окна</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Waiting_" xml:space="preserve">
    <value>Ожидание...</value>
  </data>
  <data name="HotkeyType_ImageEditor" xml:space="preserve">
    <value>Редактор изображений</value>
  </data>
  <data name="URLSharingServices_Email" xml:space="preserve">
    <value>Электронная почта</value>
  </data>
  <data name="HotkeyType_OpenHistory" xml:space="preserve">
    <value>Открыть окно истории</value>
  </data>
  <data name="ShapeType_ToolSelect" xml:space="preserve">
    <value>Выбирать и двигать (M)</value>
  </data>
  <data name="ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric" xml:space="preserve">
    <value>Счетчик, прибавляющий базовое алфавитно-цифровое {n} (1 &lt; n &lt; 63)</value>
  </data>
  <data name="HotkeyType_CaptureWebpage" xml:space="preserve">
    <value>Захват веб-страницы</value>
  </data>
  <data name="RegionCaptureAction_CancelCapture" xml:space="preserve">
    <value>Отменить захват</value>
  </data>
  <data name="AfterCaptureTasks_ScanQRCode" xml:space="preserve">
    <value>Сканировать QR-код</value>
  </data>
  <data name="HotkeyType_RectangleLight" xml:space="preserve">
    <value>Захват области (Облегченный)</value>
  </data>
  <data name="ProxyMethod_Automatic" xml:space="preserve">
    <value>Автоматическая</value>
  </data>
  <data name="HotkeyType_FileUpload" xml:space="preserve">
    <value>Загрузка файла</value>
  </data>
  <data name="ReplCodeMenuEntry_guid_Random_guid" xml:space="preserve">
    <value>Случайный GUID</value>
  </data>
  <data name="ShapeType_DrawingLine" xml:space="preserve">
    <value>Линия (L)</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_value" xml:space="preserve">
    <value>Скопировать значение</value>
  </data>
  <data name="AfterCaptureTasks_ShowBeforeUploadWindow" xml:space="preserve">
    <value>Показать окно «Перед загрузкой»</value>
  </data>
  <data name="AfterCaptureTasks_ShowInExplorer" xml:space="preserve">
    <value>Показать файл в проводнике</value>
  </data>
  <data name="ImageDestination_CustomImageUploader" xml:space="preserve">
    <value>Пользовательский сервис загрузки картинок</value>
  </data>
  <data name="HotkeyType_Category_ScreenCapture" xml:space="preserve">
    <value>Захват экрана</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_No" xml:space="preserve">
    <value>Нет</value>
  </data>
  <data name="HotkeyType_ActiveWindow" xml:space="preserve">
    <value>Захват активного окна</value>
  </data>
  <data name="ShapeType_DrawingStep" xml:space="preserve">
    <value>Шаг (I)</value>
  </data>
  <data name="ReplCodeMenuEntry_i_Auto_increment_number" xml:space="preserve">
    <value>Числовой счетчик. Нули слева, {n} задает длину</value>
  </data>
  <data name="HotkeyType_ClipboardUpload" xml:space="preserve">
    <value>Загрузка из буфера обмена</value>
  </data>
  <data name="ReplCodeMenuEntry_n_New_line" xml:space="preserve">
    <value>Новая строка</value>
  </data>
  <data name="ReplCodeMenuEntry_mon2_Current_month_name__English_" xml:space="preserve">
    <value>Имя текущего месяца (Английский)</value>
  </data>
  <data name="HotkeyType_OpenScreenshotsFolder" xml:space="preserve">
    <value>Открыть папку скриншотов</value>
  </data>
  <data name="ReplCodeMenuEntry_width_Gets_image_width" xml:space="preserve">
    <value>Ширина картинки</value>
  </data>
  <data name="ReplCodeMenuEntry_w2_Current_week_name__English_" xml:space="preserve">
    <value>Текущий день недели (Английский)</value>
  </data>
  <data name="ExeFileNameEditor_EditValue_Browse_for_executable___" xml:space="preserve">
    <value>Обзор выполняемых файлов...</value>
  </data>
  <data name="ImageDestination_FileUploader" xml:space="preserve">
    <value>Файловый сервис</value>
  </data>
  <data name="ImageEditorStartMode_AutoSize" xml:space="preserve">
    <value>Авторазмер</value>
  </data>
  <data name="HotkeyType_None" xml:space="preserve">
    <value>Не выбрано</value>
  </data>
  <data name="PNGBitDepth_Default" xml:space="preserve">
    <value>По умолчанию</value>
  </data>
  <data name="Helpers_CreateDirectoryIfNotExist_Create_failed_" xml:space="preserve">
    <value>Не удалось создать папку.</value>
  </data>
  <data name="ProxyMethod_Manual" xml:space="preserve">
    <value>Ручная</value>
  </data>
  <data name="DownloaderForm_ChangeStatus_Status___0_" xml:space="preserve">
    <value>Состояние: {0}</value>
  </data>
  <data name="HotkeyType_StartScreenRecorderGIF" xml:space="preserve">
    <value>Начать/Остановить запись последней области экрана (GIF)</value>
  </data>
  <data name="ImgurThumbnailType_Small_Square" xml:space="preserve">
    <value>Маленький квадрат</value>
  </data>
  <data name="HotkeyType_MonitorTest" xml:space="preserve">
    <value>Проверка монитора</value>
  </data>
  <data name="Extensions_AddContextMenu_Copy" xml:space="preserve">
    <value>Копировать</value>
  </data>
  <data name="AfterUploadTasks_UseURLShortener" xml:space="preserve">
    <value>Сократить ссылку</value>
  </data>
  <data name="ReplCodeMenuEntry_rf_Random_line_from_file" xml:space="preserve">
    <value>Случайная строка из файла. Используйте {filepath}, чтобы указать файл</value>
  </data>
  <data name="DownloaderForm_StartDownload_Cancel" xml:space="preserve">
    <value>Отменить</value>
  </data>
  <data name="HotkeyType_Category_Tools" xml:space="preserve">
    <value>Инструменты</value>
  </data>
  <data name="FileDestination_SharedFolder" xml:space="preserve">
    <value>Общая папка</value>
  </data>
  <data name="HotkeyType_ActiveMonitor" xml:space="preserve">
    <value>Захват активного монитора</value>
  </data>
  <data name="DownloaderForm_StartDownload_Getting_file_size_" xml:space="preserve">
    <value>Получение размера файла...</value>
  </data>
  <data name="HotkeyType_Category_Other" xml:space="preserve">
    <value>Другие</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Filename___0_" xml:space="preserve">
    <value>Имя файла: {0}</value>
  </data>
  <data name="ShapeType_DrawingEllipse" xml:space="preserve">
    <value>Эллипс (E)</value>
  </data>
  <data name="HotkeyType_ColorPicker" xml:space="preserve">
    <value>Палитра</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>Стоп</value>
  </data>
  <data name="TextDestination_FileUploader" xml:space="preserve">
    <value>Файловый сервис</value>
  </data>
  <data name="MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__" xml:space="preserve">
    <value>Загрузка изображения: {0}%</value>
  </data>
  <data name="ReplCodeMenuEntry_ra_Random_alphanumeric_char" xml:space="preserve">
    <value>Случайный алфавитно-цифровой символ. Повторение задается через {n}</value>
  </data>
  <data name="ObjectListView_ObjectListView_Value" xml:space="preserve">
    <value>Значение</value>
  </data>
  <data name="HotkeyType_DisableHotkeys" xml:space="preserve">
    <value>Отключить/Включить горячие клавиши</value>
  </data>
  <data name="RegionCaptureAction_None" xml:space="preserve">
    <value>Ничего не делать</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFileWithDialog" xml:space="preserve">
    <value>Сохранить изображение в файл как...</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_name" xml:space="preserve">
    <value>Скопировать имя</value>
  </data>
  <data name="RegionCaptureAction_RemoveShape" xml:space="preserve">
    <value>Убрать фигуру</value>
  </data>
  <data name="ActionsCodeMenuEntry_FilePath_File_path" xml:space="preserve">
    <value>Путь к файлу</value>
  </data>
  <data name="SupportedLanguage_Automatic" xml:space="preserve">
    <value>Автоматически</value>
  </data>
  <data name="HotkeyType_VideoConverter" xml:space="preserve">
    <value>Конвертер видео</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Cancel" xml:space="preserve">
    <value>Отменить</value>
  </data>
  <data name="FolderSelectDialog_Title_Select_a_folder" xml:space="preserve">
    <value>Выберите папку</value>
  </data>
  <data name="HotkeyType_OpenMainWindow" xml:space="preserve">
    <value>Открыть главное окно</value>
  </data>
  <data name="HotkeyType_ScreenColorPicker" xml:space="preserve">
    <value>Пипетка</value>
  </data>
  <data name="PrintTextForm_LoadSettings_Name___0___Size___1_" xml:space="preserve">
    <value>Имя: {0}, Размер: {1}</value>
  </data>
  <data name="HotkeyType_AutoCapture" xml:space="preserve">
    <value>Автозахват</value>
  </data>
  <data name="ShapeType_DrawingRectangle" xml:space="preserve">
    <value>Прямоугольник (R)</value>
  </data>
  <data name="ImageEditorStartMode_Maximized" xml:space="preserve">
    <value>Развернутый</value>
  </data>
  <data name="HotkeyType_ScreenRecorderCustomRegion" xml:space="preserve">
    <value>Начать/Остановить запись подготовленной области экрана</value>
  </data>
  <data name="ScreenRecordGIFEncoding_OctreeQuantizer" xml:space="preserve">
    <value>Octree quantizer (Среднее качество)</value>
  </data>
  <data name="Helpers_BrowseFile_Choose_file" xml:space="preserve">
    <value>Выберите файл</value>
  </data>
  <data name="ReplCodeMenuEntry_height_Gets_image_height" xml:space="preserve">
    <value>Высота картинки</value>
  </data>
  <data name="PastebinExpiration_M1" xml:space="preserve">
    <value>1 Месяц</value>
  </data>
  <data name="ShapeType_DrawingTextBackground" xml:space="preserve">
    <value>Текст с фоном (T)</value>
  </data>
  <data name="RandomNonAmbiguousAlphanumericCharRepeatUsingN" xml:space="preserve">
    <value>Случайный недвусмысленный алфавитно-цифровой символ. Повторение задается через {n}</value>
  </data>
  <data name="UrlShortenerType_CustomURLShortener" xml:space="preserve">
    <value>Пользовательский сервис коротких ссылок</value>
  </data>
  <data name="PastebinPrivacy_Public" xml:space="preserve">
    <value>Публичная</value>
  </data>
  <data name="FileExistAction_Overwrite" xml:space="preserve">
    <value>Перезаписать файл</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfWatermark" xml:space="preserve">
    <value>Процент изображения</value>
  </data>
  <data name="HotkeyType_ShortenURL" xml:space="preserve">
    <value>Сокращение ссылки</value>
  </data>
  <data name="CustomUploaderDestinationType_TextUploader" xml:space="preserve">
    <value>Сервис текстов</value>
  </data>
  <data name="FileExistAction_Ask" xml:space="preserve">
    <value>Спросить о действии</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_LatestVersion" xml:space="preserve">
    <value>Последняя версия</value>
  </data>
  <data name="AfterCaptureTasks_ShowAfterCaptureWindow" xml:space="preserve">
    <value>Показать окно «После захвата»</value>
  </data>
  <data name="HotkeyType_UploadText" xml:space="preserve">
    <value>Загрузка текста</value>
  </data>
  <data name="ShapeType_ToolCrop" xml:space="preserve">
    <value>Кадрировать (C)</value>
  </data>
  <data name="HotkeyType_UploadURL" xml:space="preserve">
    <value>Загрузка со ссылки</value>
  </data>
  <data name="HotkeyType_ImageSplitter" xml:space="preserve">
    <value>Разделитель изображений</value>
  </data>
  <data name="AfterUploadTasks_CopyURLToClipboard" xml:space="preserve">
    <value>Скопировать ссылку в буфер обмена</value>
  </data>
  <data name="ReplCodeMenuEntry_cn_Computer_name" xml:space="preserve">
    <value>Имя компьютера</value>
  </data>
  <data name="HotkeyType_StartAutoCapture" xml:space="preserve">
    <value>Начать автозахват последней области</value>
  </data>
  <data name="ImgurThumbnailType_Medium_Thumbnail" xml:space="preserve">
    <value>Средняя миниатюра</value>
  </data>
  <data name="Extensions_AddContextMenu_Undo" xml:space="preserve">
    <value>Отмена</value>
  </data>
  <data name="AfterCaptureTasks_CopyFileToClipboard" xml:space="preserve">
    <value>Скопировать файл в буфер обмена</value>
  </data>
  <data name="ResultOfFirstFile" xml:space="preserve">
    <value>Результат первого файла:</value>
  </data>
  <data name="ResultOfSecondFile" xml:space="preserve">
    <value>Результат второго файла:</value>
  </data>
  <data name="Result" xml:space="preserve">
    <value>Результат:</value>
  </data>
  <data name="Target" xml:space="preserve">
    <value>Цель:</value>
  </data>
  <data name="ArrowHeadDirection_End" xml:space="preserve">
    <value>Конец</value>
  </data>
  <data name="ArrowHeadDirection_Start" xml:space="preserve">
    <value>Начало</value>
  </data>
  <data name="ArrowHeadDirection_Both" xml:space="preserve">
    <value>Оба</value>
  </data>
  <data name="StepType_LettersLowercase" xml:space="preserve">
    <value>Буквы (нижний регистр)</value>
  </data>
  <data name="StepType_LettersUppercase" xml:space="preserve">
    <value>Буквы (верхний регистр)</value>
  </data>
  <data name="StepType_Numbers" xml:space="preserve">
    <value>Цифры</value>
  </data>
  <data name="StepType_RomanNumeralsLowercase" xml:space="preserve">
    <value>Римские цифры (нижний регистр)</value>
  </data>
  <data name="StepType_RomanNumeralsUppercase" xml:space="preserve">
    <value>Римские цифры (верхний регистр)</value>
  </data>
  <data name="HotkeyType_ClipboardViewer" xml:space="preserve">
    <value>Буфер обмена</value>
  </data>
  <data name="HotkeyType_InspectWindow" xml:space="preserve">
    <value>Исследовать окно</value>
  </data>
  <data name="BorderStyle_Solid" xml:space="preserve">
    <value>Сплошная</value>
  </data>
  <data name="BorderStyle_Dash" xml:space="preserve">
    <value>Пунктир</value>
  </data>
  <data name="BorderStyle_Dot" xml:space="preserve">
    <value>Точки</value>
  </data>
  <data name="BorderStyle_DashDot" xml:space="preserve">
    <value>Штрихпунктир</value>
  </data>
  <data name="BorderStyle_DashDotDot" xml:space="preserve">
    <value>Штрихпунктир с двумя точками</value>
  </data>
  <data name="ToastClickAction_CloseNotification" xml:space="preserve">
    <value>Закрыть уведомление</value>
  </data>
  <data name="ToastClickAction_AnnotateImage" xml:space="preserve">
    <value>Пометить изображение</value>
  </data>
  <data name="ToastClickAction_CopyImageToClipboard" xml:space="preserve">
    <value>Скопировать изображение</value>
  </data>
  <data name="ToastClickAction_CopyFile" xml:space="preserve">
    <value>Скопировать файл</value>
  </data>
  <data name="ToastClickAction_CopyFilePath" xml:space="preserve">
    <value>Скопировать путь к файлу</value>
  </data>
  <data name="ToastClickAction_CopyUrl" xml:space="preserve">
    <value>Скопировать ссылку</value>
  </data>
  <data name="ToastClickAction_OpenFile" xml:space="preserve">
    <value>Скопировать файл</value>
  </data>
  <data name="ToastClickAction_OpenFolder" xml:space="preserve">
    <value>Скопировать папку</value>
  </data>
  <data name="ToastClickAction_OpenUrl" xml:space="preserve">
    <value>Скопировать ссылку</value>
  </data>
  <data name="ToastClickAction_Upload" xml:space="preserve">
    <value>Загрузить файл</value>
  </data>
  <data name="ContentAlignment_TopLeft" xml:space="preserve">
    <value>Вверху слева</value>
  </data>
  <data name="ContentAlignment_TopCenter" xml:space="preserve">
    <value>Вверху посередине</value>
  </data>
  <data name="ContentAlignment_TopRight" xml:space="preserve">
    <value>Вверху справа</value>
  </data>
  <data name="ContentAlignment_MiddleLeft" xml:space="preserve">
    <value>Посередине слева</value>
  </data>
  <data name="ContentAlignment_MiddleCenter" xml:space="preserve">
    <value>Посередине</value>
  </data>
  <data name="ContentAlignment_MiddleRight" xml:space="preserve">
    <value>Посередине справа</value>
  </data>
  <data name="ContentAlignment_BottomLeft" xml:space="preserve">
    <value>Внизу слева</value>
  </data>
  <data name="ContentAlignment_BottomCenter" xml:space="preserve">
    <value>Внизу посередине</value>
  </data>
  <data name="ContentAlignment_BottomRight" xml:space="preserve">
    <value>Внизу справа</value>
  </data>
  <data name="URLSharingServices_BingVisualSearch" xml:space="preserve">
    <value>Визуальный поиск Bing</value>
  </data>
  <data name="EDataType_Default" xml:space="preserve">
    <value>По умолчанию</value>
  </data>
  <data name="EDataType_File" xml:space="preserve">
    <value>Файл</value>
  </data>
  <data name="EDataType_Image" xml:space="preserve">
    <value>Изображение</value>
  </data>
  <data name="EDataType_Text" xml:space="preserve">
    <value>Текст</value>
  </data>
  <data name="EDataType_URL" xml:space="preserve">
    <value>Ссылка</value>
  </data>
  <data name="RegionCaptureAction_CaptureLastRegion" xml:space="preserve">
    <value>Захват последней области</value>
  </data>
  <data name="HotkeyType_StopScreenRecording" xml:space="preserve">
    <value>Прекратить запись экрана</value>
  </data>
  <data name="HotkeyType_ToggleTrayMenu" xml:space="preserve">
    <value>Переключить меню трея</value>
  </data>
  <data name="ThumbnailViewClickAction_Default" xml:space="preserve">
    <value>По умолчанию</value>
  </data>
  <data name="ThumbnailViewClickAction_EditImage" xml:space="preserve">
    <value>Редактировать изображение</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFile" xml:space="preserve">
    <value>Открыть файл</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFolder" xml:space="preserve">
    <value>Открыть папку</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenImageViewer" xml:space="preserve">
    <value>Открыть просмотр изображений</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenURL" xml:space="preserve">
    <value>Открыть ссылку</value>
  </data>
  <data name="ThumbnailViewClickAction_Select" xml:space="preserve">
    <value>Выбрать</value>
  </data>
  <data name="ImagePreviewLocation_Bottom" xml:space="preserve">
    <value>Внизу</value>
  </data>
  <data name="ImagePreviewLocation_Side" xml:space="preserve">
    <value>Сбоку</value>
  </data>
  <data name="ImagePreviewVisibility_Automatic" xml:space="preserve">
    <value>Автоматически</value>
  </data>
  <data name="ImagePreviewVisibility_Hide" xml:space="preserve">
    <value>Скрыть</value>
  </data>
  <data name="ImagePreviewVisibility_Show" xml:space="preserve">
    <value>Показывать</value>
  </data>
  <data name="TaskViewMode_ListView" xml:space="preserve">
    <value>Режим списка</value>
  </data>
  <data name="TaskViewMode_ThumbnailView" xml:space="preserve">
    <value>Режим миниатюр</value>
  </data>
  <data name="ThumbnailTitleLocation_Bottom" xml:space="preserve">
    <value>Внизу</value>
  </data>
  <data name="ThumbnailTitleLocation_Top" xml:space="preserve">
    <value>Вверху</value>
  </data>
  <data name="HotkeyType_ImageViewer" xml:space="preserve">
    <value>Просмотр изображений</value>
  </data>
  <data name="HotkeyType_OCR" xml:space="preserve">
    <value>Распознавание текста (OCR)</value>
  </data>
  <data name="HotkeyType_BorderlessWindow" xml:space="preserve">
    <value>Безрамочное окно</value>
  </data>
  <data name="AfterCaptureTasks_PinToScreen" xml:space="preserve">
    <value>Закрепить на экране</value>
  </data>
  <data name="ToastClickAction_PinToScreen" xml:space="preserve">
    <value>Закрепить на экране</value>
  </data>
  <data name="ShareXImageViewer" xml:space="preserve">
    <value>ShareX - Просмотр изображений</value>
  </data>
  <data name="HotkeyType_PinToScreen" xml:space="preserve">
    <value>Закрепить на экране</value>
  </data>
  <data name="CutOutEffectType_None" xml:space="preserve">
    <value>Без эффекта</value>
  </data>
  <data name="CutOutEffectType_TornEdge" xml:space="preserve">
    <value>Рваные края</value>
  </data>
  <data name="CutOutEffectType_Wave" xml:space="preserve">
    <value>Волна</value>
  </data>
  <data name="CutOutEffectType_ZigZag" xml:space="preserve">
    <value>Пила</value>
  </data>
  <data name="ShapeType_ToolCutOut" xml:space="preserve">
    <value>Разрыв (X)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromClipboard" xml:space="preserve">
    <value>Закрепить на экране (из буфера обмена)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromFile" xml:space="preserve">
    <value>Закрепить на экране (из файла)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromScreen" xml:space="preserve">
    <value>Закрепить на экране (из экрана)</value>
  </data>
  <data name="HotkeyType_PauseScreenRecording" xml:space="preserve">
    <value>Приостановить запись</value>
  </data>
  <data name="ShapeType_DrawingFreehandArrow" xml:space="preserve">
    <value>Стрелка от руки</value>
  </data>
  <data name="HotkeyType_ImageBeautifier" xml:space="preserve">
    <value>Украшатель изображений</value>
  </data>
  <data name="AfterCaptureTasks_BeautifyImage" xml:space="preserve">
    <value>Украсить изображение</value>
  </data>
  <data name="Check" xml:space="preserve">
    <value>Проверить</value>
  </data>
  <data name="HotkeyType_CustomWindow" xml:space="preserve">
    <value>Захват подготовленного окна</value>
  </data>
  <data name="DownloaderForm_FileDownloader_ProgressChanged_Progress" xml:space="preserve">
    <value>Прогресс</value>
  </data>
  <data name="DownloaderForm_FileDownloader_ProgressChanged_DownloadSpeed" xml:space="preserve">
    <value>Скорость загрузки</value>
  </data>
  <data name="DownloaderForm_FileDownloader_ProgressChanged_FileSize" xml:space="preserve">
    <value>Размер файла</value>
  </data>
  <data name="HotkeyType_ActiveWindowBorderless" xml:space="preserve">
    <value>Сделать активное окно безрамочным</value>
  </data>
  <data name="HotkeyType_ActiveWindowTopMost" xml:space="preserve">
    <value>Поместить активное окно поверх остальных</value>
  </data>
  <data name="HotkeyType_PinToScreenCloseAll" xml:space="preserve">
    <value>Закрепить на экране (Закрыть все)</value>
  </data>
  <data name="ImageBeautifierBackgroundType_Color" xml:space="preserve">
    <value>Цвет</value>
  </data>
  <data name="ImageBeautifierBackgroundType_Desktop" xml:space="preserve">
    <value>Рабочий стол</value>
  </data>
  <data name="ImageBeautifierBackgroundType_Gradient" xml:space="preserve">
    <value>Градиент</value>
  </data>
  <data name="ImageBeautifierBackgroundType_Image" xml:space="preserve">
    <value>Изображение</value>
  </data>
  <data name="ImageBeautifierBackgroundType_Transparent" xml:space="preserve">
    <value>Прозрачность</value>
  </data>
  <data name="HotkeyType_Metadata" xml:space="preserve">
    <value />
  </data>
</root>
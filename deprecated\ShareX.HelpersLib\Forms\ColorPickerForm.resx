﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnCancel.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>576, 296</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnCancel.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Name" xml:space="preserve">
    <value>btnCancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnCancel.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="btnOK.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>472, 296</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <data name="btnOK.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="&gt;&gt;btnOK.Name" xml:space="preserve">
    <value>btnOK</value>
  </data>
  <data name="&gt;&gt;btnOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOK.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnOK.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="lblOld.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblOld.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblOld.Location" type="System.Drawing.Point, System.Drawing">
    <value>309, 262</value>
  </data>
  <data name="lblOld.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="lblOld.TabIndex" type="System.Int32, mscorlib">
    <value>37</value>
  </data>
  <data name="lblOld.Text" xml:space="preserve">
    <value>Old:</value>
  </data>
  <data name="&gt;&gt;lblOld.Name" xml:space="preserve">
    <value>lblOld</value>
  </data>
  <data name="&gt;&gt;lblOld.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblOld.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblOld.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="lblNew.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblNew.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblNew.Location" type="System.Drawing.Point, System.Drawing">
    <value>309, 238</value>
  </data>
  <data name="lblNew.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 13</value>
  </data>
  <data name="lblNew.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="lblNew.Text" xml:space="preserve">
    <value>New:</value>
  </data>
  <data name="&gt;&gt;lblNew.Name" xml:space="preserve">
    <value>lblNew</value>
  </data>
  <data name="&gt;&gt;lblNew.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblNew.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblNew.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="txtHex.Location" type="System.Drawing.Point, System.Drawing">
    <value>600, 174</value>
  </data>
  <data name="txtHex.MaxLength" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="txtHex.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 20</value>
  </data>
  <data name="txtHex.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="txtHex.Text" xml:space="preserve">
    <value>FF00FF00</value>
  </data>
  <data name="txtHex.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;txtHex.Name" xml:space="preserve">
    <value>txtHex</value>
  </data>
  <data name="&gt;&gt;txtHex.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtHex.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtHex.ZOrder" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="lblHex.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblHex.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblHex.Location" type="System.Drawing.Point, System.Drawing">
    <value>485, 178</value>
  </data>
  <data name="lblHex.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="lblHex.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="lblHex.Text" xml:space="preserve">
    <value>Hex:</value>
  </data>
  <data name="&gt;&gt;lblHex.Name" xml:space="preserve">
    <value>lblHex</value>
  </data>
  <data name="&gt;&gt;lblHex.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblHex.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblHex.ZOrder" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="nudKey.Location" type="System.Drawing.Point, System.Drawing">
    <value>600, 142</value>
  </data>
  <data name="nudKey.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="nudKey.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="nudKey.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudKey.Name" xml:space="preserve">
    <value>nudKey</value>
  </data>
  <data name="&gt;&gt;nudKey.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudKey.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudKey.ZOrder" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="nudYellow.Location" type="System.Drawing.Point, System.Drawing">
    <value>600, 108</value>
  </data>
  <data name="nudYellow.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="nudYellow.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="nudYellow.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudYellow.Name" xml:space="preserve">
    <value>nudYellow</value>
  </data>
  <data name="&gt;&gt;nudYellow.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudYellow.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudYellow.ZOrder" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="nudMagenta.Location" type="System.Drawing.Point, System.Drawing">
    <value>600, 78</value>
  </data>
  <data name="nudMagenta.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="nudMagenta.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="nudMagenta.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudMagenta.Name" xml:space="preserve">
    <value>nudMagenta</value>
  </data>
  <data name="&gt;&gt;nudMagenta.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudMagenta.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudMagenta.ZOrder" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="nudCyan.Location" type="System.Drawing.Point, System.Drawing">
    <value>600, 46</value>
  </data>
  <data name="nudCyan.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="nudCyan.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="nudCyan.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudCyan.Name" xml:space="preserve">
    <value>nudCyan</value>
  </data>
  <data name="&gt;&gt;nudCyan.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudCyan.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudCyan.ZOrder" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="lblKey.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblKey.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblKey.Location" type="System.Drawing.Point, System.Drawing">
    <value>485, 146</value>
  </data>
  <data name="lblKey.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 13</value>
  </data>
  <data name="lblKey.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="lblKey.Text" xml:space="preserve">
    <value>Key:</value>
  </data>
  <data name="&gt;&gt;lblKey.Name" xml:space="preserve">
    <value>lblKey</value>
  </data>
  <data name="&gt;&gt;lblKey.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblKey.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblKey.ZOrder" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="lblYellow.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblYellow.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblYellow.Location" type="System.Drawing.Point, System.Drawing">
    <value>485, 112</value>
  </data>
  <data name="lblYellow.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 13</value>
  </data>
  <data name="lblYellow.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="lblYellow.Text" xml:space="preserve">
    <value>Yellow:</value>
  </data>
  <data name="&gt;&gt;lblYellow.Name" xml:space="preserve">
    <value>lblYellow</value>
  </data>
  <data name="&gt;&gt;lblYellow.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblYellow.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblYellow.ZOrder" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="lblMagenta.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblMagenta.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblMagenta.Location" type="System.Drawing.Point, System.Drawing">
    <value>485, 82</value>
  </data>
  <data name="lblMagenta.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 13</value>
  </data>
  <data name="lblMagenta.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="lblMagenta.Text" xml:space="preserve">
    <value>Magenta:</value>
  </data>
  <data name="&gt;&gt;lblMagenta.Name" xml:space="preserve">
    <value>lblMagenta</value>
  </data>
  <data name="&gt;&gt;lblMagenta.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblMagenta.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblMagenta.ZOrder" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="lblCyan.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblCyan.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblCyan.Location" type="System.Drawing.Point, System.Drawing">
    <value>485, 50</value>
  </data>
  <data name="lblCyan.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 13</value>
  </data>
  <data name="lblCyan.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lblCyan.Text" xml:space="preserve">
    <value>Cyan:</value>
  </data>
  <data name="&gt;&gt;lblCyan.Name" xml:space="preserve">
    <value>lblCyan</value>
  </data>
  <data name="&gt;&gt;lblCyan.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblCyan.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblCyan.ZOrder" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="lblHue.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblHue.Font" type="System.Drawing.Font, System.Drawing">
    <value>Verdana, 8.25pt</value>
  </data>
  <data name="lblHue.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblHue.Location" type="System.Drawing.Point, System.Drawing">
    <value>460, 112</value>
  </data>
  <data name="lblHue.Size" type="System.Drawing.Size, System.Drawing">
    <value>13, 13</value>
  </data>
  <data name="lblHue.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lblHue.Text" xml:space="preserve">
    <value>°</value>
  </data>
  <data name="&gt;&gt;lblHue.Name" xml:space="preserve">
    <value>lblHue</value>
  </data>
  <data name="&gt;&gt;lblHue.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblHue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblHue.ZOrder" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="lblBrightnessPerc.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblBrightnessPerc.Font" type="System.Drawing.Font, System.Drawing">
    <value>Verdana, 8.25pt</value>
  </data>
  <data name="lblBrightnessPerc.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblBrightnessPerc.Location" type="System.Drawing.Point, System.Drawing">
    <value>460, 178</value>
  </data>
  <data name="lblBrightnessPerc.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lblBrightnessPerc.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="lblBrightnessPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;lblBrightnessPerc.Name" xml:space="preserve">
    <value>lblBrightnessPerc</value>
  </data>
  <data name="&gt;&gt;lblBrightnessPerc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblBrightnessPerc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblBrightnessPerc.ZOrder" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="lblSaturationPerc.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblSaturationPerc.Font" type="System.Drawing.Font, System.Drawing">
    <value>Verdana, 8.25pt</value>
  </data>
  <data name="lblSaturationPerc.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblSaturationPerc.Location" type="System.Drawing.Point, System.Drawing">
    <value>460, 146</value>
  </data>
  <data name="lblSaturationPerc.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lblSaturationPerc.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="lblSaturationPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;lblSaturationPerc.Name" xml:space="preserve">
    <value>lblSaturationPerc</value>
  </data>
  <data name="&gt;&gt;lblSaturationPerc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblSaturationPerc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblSaturationPerc.ZOrder" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="nudBlue.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 78</value>
  </data>
  <data name="nudBlue.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="nudBlue.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="nudBlue.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudBlue.Name" xml:space="preserve">
    <value>nudBlue</value>
  </data>
  <data name="&gt;&gt;nudBlue.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudBlue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudBlue.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="nudGreen.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 46</value>
  </data>
  <data name="nudGreen.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="nudGreen.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="nudGreen.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudGreen.Name" xml:space="preserve">
    <value>nudGreen</value>
  </data>
  <data name="&gt;&gt;nudGreen.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudGreen.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudGreen.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="nudRed.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 14</value>
  </data>
  <data name="nudRed.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="nudRed.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="nudRed.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudRed.Name" xml:space="preserve">
    <value>nudRed</value>
  </data>
  <data name="&gt;&gt;nudRed.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudRed.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudRed.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="nudBrightness.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 174</value>
  </data>
  <data name="nudBrightness.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="nudBrightness.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="nudBrightness.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudBrightness.Name" xml:space="preserve">
    <value>nudBrightness</value>
  </data>
  <data name="&gt;&gt;nudBrightness.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudBrightness.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudBrightness.ZOrder" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="nudSaturation.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 142</value>
  </data>
  <data name="nudSaturation.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="nudSaturation.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="nudSaturation.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudSaturation.Name" xml:space="preserve">
    <value>nudSaturation</value>
  </data>
  <data name="&gt;&gt;nudSaturation.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudSaturation.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudSaturation.ZOrder" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="nudHue.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 108</value>
  </data>
  <data name="nudHue.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="nudHue.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="nudHue.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudHue.Name" xml:space="preserve">
    <value>nudHue</value>
  </data>
  <data name="&gt;&gt;nudHue.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudHue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudHue.ZOrder" xml:space="preserve">
    <value>47</value>
  </data>
  <data name="rbBlue.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbBlue.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="rbBlue.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 80</value>
  </data>
  <data name="rbBlue.Size" type="System.Drawing.Size, System.Drawing">
    <value>49, 17</value>
  </data>
  <data name="rbBlue.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="rbBlue.Text" xml:space="preserve">
    <value>Blue:</value>
  </data>
  <data name="&gt;&gt;rbBlue.Name" xml:space="preserve">
    <value>rbBlue</value>
  </data>
  <data name="&gt;&gt;rbBlue.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbBlue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;rbBlue.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="rbGreen.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbGreen.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="rbGreen.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 48</value>
  </data>
  <data name="rbGreen.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 17</value>
  </data>
  <data name="rbGreen.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="rbGreen.Text" xml:space="preserve">
    <value>Green:</value>
  </data>
  <data name="&gt;&gt;rbGreen.Name" xml:space="preserve">
    <value>rbGreen</value>
  </data>
  <data name="&gt;&gt;rbGreen.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbGreen.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;rbGreen.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="rbRed.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbRed.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="rbRed.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 16</value>
  </data>
  <data name="rbRed.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 17</value>
  </data>
  <data name="rbRed.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="rbRed.Text" xml:space="preserve">
    <value>Red:</value>
  </data>
  <data name="&gt;&gt;rbRed.Name" xml:space="preserve">
    <value>rbRed</value>
  </data>
  <data name="&gt;&gt;rbRed.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbRed.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;rbRed.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="rbBrightness.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbBrightness.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="rbBrightness.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 176</value>
  </data>
  <data name="rbBrightness.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 17</value>
  </data>
  <data name="rbBrightness.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="rbBrightness.Text" xml:space="preserve">
    <value>Brightness:</value>
  </data>
  <data name="&gt;&gt;rbBrightness.Name" xml:space="preserve">
    <value>rbBrightness</value>
  </data>
  <data name="&gt;&gt;rbBrightness.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbBrightness.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;rbBrightness.ZOrder" xml:space="preserve">
    <value>48</value>
  </data>
  <data name="rbSaturation.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbSaturation.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="rbSaturation.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 144</value>
  </data>
  <data name="rbSaturation.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 17</value>
  </data>
  <data name="rbSaturation.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="rbSaturation.Text" xml:space="preserve">
    <value>Saturation:</value>
  </data>
  <data name="&gt;&gt;rbSaturation.Name" xml:space="preserve">
    <value>rbSaturation</value>
  </data>
  <data name="&gt;&gt;rbSaturation.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbSaturation.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;rbSaturation.ZOrder" xml:space="preserve">
    <value>49</value>
  </data>
  <data name="rbHue.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbHue.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="rbHue.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 112</value>
  </data>
  <data name="rbHue.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 17</value>
  </data>
  <data name="rbHue.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="rbHue.Text" xml:space="preserve">
    <value>Hue:</value>
  </data>
  <data name="&gt;&gt;rbHue.Name" xml:space="preserve">
    <value>rbHue</value>
  </data>
  <data name="&gt;&gt;rbHue.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbHue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;rbHue.ZOrder" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="lblDecimal.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblDecimal.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblDecimal.Location" type="System.Drawing.Point, System.Drawing">
    <value>485, 208</value>
  </data>
  <data name="lblDecimal.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="lblDecimal.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="lblDecimal.Text" xml:space="preserve">
    <value>Decimal:</value>
  </data>
  <data name="&gt;&gt;lblDecimal.Name" xml:space="preserve">
    <value>lblDecimal</value>
  </data>
  <data name="&gt;&gt;lblDecimal.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblDecimal.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblDecimal.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="txtDecimal.Location" type="System.Drawing.Point, System.Drawing">
    <value>600, 204</value>
  </data>
  <data name="txtDecimal.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 20</value>
  </data>
  <data name="txtDecimal.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="txtDecimal.Text" xml:space="preserve">
    <value>12345678</value>
  </data>
  <data name="txtDecimal.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;txtDecimal.Name" xml:space="preserve">
    <value>txtDecimal</value>
  </data>
  <data name="&gt;&gt;txtDecimal.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtDecimal.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtDecimal.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="lblCyanPerc.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblCyanPerc.Font" type="System.Drawing.Font, System.Drawing">
    <value>Verdana, 8.25pt</value>
  </data>
  <data name="lblCyanPerc.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblCyanPerc.Location" type="System.Drawing.Point, System.Drawing">
    <value>658, 50</value>
  </data>
  <data name="lblCyanPerc.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lblCyanPerc.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="lblCyanPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;lblCyanPerc.Name" xml:space="preserve">
    <value>lblCyanPerc</value>
  </data>
  <data name="&gt;&gt;lblCyanPerc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblCyanPerc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblCyanPerc.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="lblMagentaPerc.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblMagentaPerc.Font" type="System.Drawing.Font, System.Drawing">
    <value>Verdana, 8.25pt</value>
  </data>
  <data name="lblMagentaPerc.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblMagentaPerc.Location" type="System.Drawing.Point, System.Drawing">
    <value>658, 82</value>
  </data>
  <data name="lblMagentaPerc.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lblMagentaPerc.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="lblMagentaPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;lblMagentaPerc.Name" xml:space="preserve">
    <value>lblMagentaPerc</value>
  </data>
  <data name="&gt;&gt;lblMagentaPerc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblMagentaPerc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblMagentaPerc.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="lblYellowPerc.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblYellowPerc.Font" type="System.Drawing.Font, System.Drawing">
    <value>Verdana, 8.25pt</value>
  </data>
  <data name="lblYellowPerc.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblYellowPerc.Location" type="System.Drawing.Point, System.Drawing">
    <value>658, 112</value>
  </data>
  <data name="lblYellowPerc.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lblYellowPerc.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="lblYellowPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;lblYellowPerc.Name" xml:space="preserve">
    <value>lblYellowPerc</value>
  </data>
  <data name="&gt;&gt;lblYellowPerc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblYellowPerc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblYellowPerc.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="lblKeyPerc.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblKeyPerc.Font" type="System.Drawing.Font, System.Drawing">
    <value>Verdana, 8.25pt</value>
  </data>
  <data name="lblKeyPerc.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblKeyPerc.Location" type="System.Drawing.Point, System.Drawing">
    <value>658, 146</value>
  </data>
  <data name="lblKeyPerc.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lblKeyPerc.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="lblKeyPerc.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;lblKeyPerc.Name" xml:space="preserve">
    <value>lblKeyPerc</value>
  </data>
  <data name="&gt;&gt;lblKeyPerc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblKeyPerc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblKeyPerc.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="nudAlpha.Location" type="System.Drawing.Point, System.Drawing">
    <value>600, 14</value>
  </data>
  <data name="nudAlpha.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="nudAlpha.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="nudAlpha.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudAlpha.Name" xml:space="preserve">
    <value>nudAlpha</value>
  </data>
  <data name="&gt;&gt;nudAlpha.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudAlpha.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudAlpha.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="lblAlpha.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblAlpha.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblAlpha.Location" type="System.Drawing.Point, System.Drawing">
    <value>485, 18</value>
  </data>
  <data name="lblAlpha.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 13</value>
  </data>
  <data name="lblAlpha.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="lblAlpha.Text" xml:space="preserve">
    <value>Alpha:</value>
  </data>
  <data name="&gt;&gt;lblAlpha.Name" xml:space="preserve">
    <value>lblAlpha</value>
  </data>
  <data name="&gt;&gt;lblAlpha.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblAlpha.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblAlpha.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <metadata name="ttMain.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="btnScreenColorPicker.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnScreenColorPicker.Location" type="System.Drawing.Point, System.Drawing">
    <value>392, 296</value>
  </data>
  <data name="btnScreenColorPicker.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 32</value>
  </data>
  <data name="btnScreenColorPicker.TabIndex" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="btnScreenColorPicker.ToolTip" xml:space="preserve">
    <value>Pick color from screen</value>
  </data>
  <data name="btnScreenColorPicker.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btnScreenColorPicker.Name" xml:space="preserve">
    <value>btnScreenColorPicker</value>
  </data>
  <data name="&gt;&gt;btnScreenColorPicker.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnScreenColorPicker.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnScreenColorPicker.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="btnClipboardColorPicker.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnClipboardColorPicker.Location" type="System.Drawing.Point, System.Drawing">
    <value>432, 296</value>
  </data>
  <data name="btnClipboardColorPicker.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 32</value>
  </data>
  <data name="btnClipboardColorPicker.TabIndex" type="System.Int32, mscorlib">
    <value>51</value>
  </data>
  <data name="btnClipboardColorPicker.ToolTip" xml:space="preserve">
    <value>Pick color from clipboard</value>
  </data>
  <data name="&gt;&gt;btnClipboardColorPicker.Name" xml:space="preserve">
    <value>btnClipboardColorPicker</value>
  </data>
  <data name="&gt;&gt;btnClipboardColorPicker.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnClipboardColorPicker.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnClipboardColorPicker.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cbTransparent.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="cbTransparent.Location" type="System.Drawing.Point, System.Drawing">
    <value>652, 13</value>
  </data>
  <data name="cbTransparent.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 23</value>
  </data>
  <data name="cbTransparent.TabIndex" type="System.Int32, mscorlib">
    <value>39</value>
  </data>
  <data name="cbTransparent.ToolTip" xml:space="preserve">
    <value>Transparent</value>
  </data>
  <data name="&gt;&gt;cbTransparent.Name" xml:space="preserve">
    <value>cbTransparent</value>
  </data>
  <data name="&gt;&gt;cbTransparent.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.ColorButton, ShareX.HelpersLib, Version=********, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;cbTransparent.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbTransparent.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <metadata name="cmsCopy.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>104, 17</value>
  </metadata>
  <data name="tsmiCopyAll.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 22</value>
  </data>
  <data name="tsmiCopyAll.Text" xml:space="preserve">
    <value>Copy all</value>
  </data>
  <data name="tsmiCopyRGB.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 22</value>
  </data>
  <data name="tsmiCopyRGB.Text" xml:space="preserve">
    <value>Copy RGB</value>
  </data>
  <data name="tsmiCopyHexadecimal.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 22</value>
  </data>
  <data name="tsmiCopyHexadecimal.Text" xml:space="preserve">
    <value>Copy hexadecimal</value>
  </data>
  <data name="tsmiCopyCMYK.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 22</value>
  </data>
  <data name="tsmiCopyCMYK.Text" xml:space="preserve">
    <value>Copy CMYK</value>
  </data>
  <data name="tsmiCopyHSB.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 22</value>
  </data>
  <data name="tsmiCopyHSB.Text" xml:space="preserve">
    <value>Copy HSB</value>
  </data>
  <data name="tsmiCopyDecimal.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 22</value>
  </data>
  <data name="tsmiCopyDecimal.Text" xml:space="preserve">
    <value>Copy decimal</value>
  </data>
  <data name="tsmiCopyPosition.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 22</value>
  </data>
  <data name="tsmiCopyPosition.Text" xml:space="preserve">
    <value>Copy position</value>
  </data>
  <data name="cmsCopy.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 158</value>
  </data>
  <data name="&gt;&gt;cmsCopy.Name" xml:space="preserve">
    <value>cmsCopy</value>
  </data>
  <data name="&gt;&gt;cmsCopy.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtY.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 24</value>
  </data>
  <data name="txtY.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="txtY.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="txtY.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtY.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;txtY.Name" xml:space="preserve">
    <value>txtY</value>
  </data>
  <data name="&gt;&gt;txtY.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtY.Parent" xml:space="preserve">
    <value>pCursorPosition</value>
  </data>
  <data name="&gt;&gt;txtY.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtX.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 0</value>
  </data>
  <data name="txtX.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 20</value>
  </data>
  <data name="txtX.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="txtX.Text" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtX.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;txtX.Name" xml:space="preserve">
    <value>txtX</value>
  </data>
  <data name="&gt;&gt;txtX.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtX.Parent" xml:space="preserve">
    <value>pCursorPosition</value>
  </data>
  <data name="&gt;&gt;txtX.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lblY.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblY.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblY.Location" type="System.Drawing.Point, System.Drawing">
    <value>112, 28</value>
  </data>
  <data name="lblY.Size" type="System.Drawing.Size, System.Drawing">
    <value>17, 13</value>
  </data>
  <data name="lblY.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lblY.Text" xml:space="preserve">
    <value>Y:</value>
  </data>
  <data name="&gt;&gt;lblY.Name" xml:space="preserve">
    <value>lblY</value>
  </data>
  <data name="&gt;&gt;lblY.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblY.Parent" xml:space="preserve">
    <value>pCursorPosition</value>
  </data>
  <data name="&gt;&gt;lblY.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lblX.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblX.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblX.Location" type="System.Drawing.Point, System.Drawing">
    <value>112, 4</value>
  </data>
  <data name="lblX.Size" type="System.Drawing.Size, System.Drawing">
    <value>17, 13</value>
  </data>
  <data name="lblX.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lblX.Text" xml:space="preserve">
    <value>X:</value>
  </data>
  <data name="&gt;&gt;lblX.Name" xml:space="preserve">
    <value>lblX</value>
  </data>
  <data name="&gt;&gt;lblX.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblX.Parent" xml:space="preserve">
    <value>pCursorPosition</value>
  </data>
  <data name="&gt;&gt;lblX.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="lblCursorPosition.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblCursorPosition.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblCursorPosition.Location" type="System.Drawing.Point, System.Drawing">
    <value>-3, 4</value>
  </data>
  <data name="lblCursorPosition.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 13</value>
  </data>
  <data name="lblCursorPosition.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblCursorPosition.Text" xml:space="preserve">
    <value>Cursor position:</value>
  </data>
  <data name="&gt;&gt;lblCursorPosition.Name" xml:space="preserve">
    <value>lblCursorPosition</value>
  </data>
  <data name="&gt;&gt;lblCursorPosition.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblCursorPosition.Parent" xml:space="preserve">
    <value>pCursorPosition</value>
  </data>
  <data name="&gt;&gt;lblCursorPosition.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="pCursorPosition.Location" type="System.Drawing.Point, System.Drawing">
    <value>488, 232</value>
  </data>
  <data name="pCursorPosition.Size" type="System.Drawing.Size, System.Drawing">
    <value>192, 48</value>
  </data>
  <data name="pCursorPosition.TabIndex" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="&gt;&gt;pCursorPosition.Name" xml:space="preserve">
    <value>pCursorPosition</value>
  </data>
  <data name="&gt;&gt;pCursorPosition.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pCursorPosition.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pCursorPosition.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="btnClose.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnClose.Location" type="System.Drawing.Point, System.Drawing">
    <value>576, 296</value>
  </data>
  <data name="btnClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <data name="btnClose.TabIndex" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="btnClose.Text" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="&gt;&gt;btnClose.Name" xml:space="preserve">
    <value>btnClose</value>
  </data>
  <data name="&gt;&gt;btnClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnClose.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnClose.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="flpColorPalette.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 293</value>
  </data>
  <data name="flpColorPalette.Size" type="System.Drawing.Size, System.Drawing">
    <value>296, 40</value>
  </data>
  <data name="flpColorPalette.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;flpColorPalette.Name" xml:space="preserve">
    <value>flpColorPalette</value>
  </data>
  <data name="&gt;&gt;flpColorPalette.Type" xml:space="preserve">
    <value>System.Windows.Forms.FlowLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;flpColorPalette.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;flpColorPalette.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="rbRecentColors.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbRecentColors.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="rbRecentColors.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="rbRecentColors.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 17</value>
  </data>
  <data name="rbRecentColors.TabIndex" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="rbRecentColors.Text" xml:space="preserve">
    <value>Recent colors</value>
  </data>
  <data name="&gt;&gt;rbRecentColors.Name" xml:space="preserve">
    <value>rbRecentColors</value>
  </data>
  <data name="&gt;&gt;rbRecentColors.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbRecentColors.Parent" xml:space="preserve">
    <value>flpColorPaletteSelection</value>
  </data>
  <data name="&gt;&gt;rbRecentColors.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rbStandardColors.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rbStandardColors.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="rbStandardColors.Location" type="System.Drawing.Point, System.Drawing">
    <value>100, 3</value>
  </data>
  <data name="rbStandardColors.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 17</value>
  </data>
  <data name="rbStandardColors.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="rbStandardColors.Text" xml:space="preserve">
    <value>Standard colors</value>
  </data>
  <data name="&gt;&gt;rbStandardColors.Name" xml:space="preserve">
    <value>rbStandardColors</value>
  </data>
  <data name="&gt;&gt;rbStandardColors.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rbStandardColors.Parent" xml:space="preserve">
    <value>flpColorPaletteSelection</value>
  </data>
  <data name="&gt;&gt;rbStandardColors.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="flpColorPaletteSelection.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="flpColorPaletteSelection.AutoSizeMode" type="System.Windows.Forms.AutoSizeMode, System.Windows.Forms">
    <value>GrowAndShrink</value>
  </data>
  <data name="flpColorPaletteSelection.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 269</value>
  </data>
  <data name="flpColorPaletteSelection.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 23</value>
  </data>
  <data name="flpColorPaletteSelection.TabIndex" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="&gt;&gt;flpColorPaletteSelection.Name" xml:space="preserve">
    <value>flpColorPaletteSelection</value>
  </data>
  <data name="&gt;&gt;flpColorPaletteSelection.Type" xml:space="preserve">
    <value>System.Windows.Forms.FlowLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;flpColorPaletteSelection.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;flpColorPaletteSelection.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lblName.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblName.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblName.Location" type="System.Drawing.Point, System.Drawing">
    <value>309, 208</value>
  </data>
  <data name="lblName.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 13</value>
  </data>
  <data name="lblName.TabIndex" type="System.Int32, mscorlib">
    <value>49</value>
  </data>
  <data name="lblName.Text" xml:space="preserve">
    <value>Name:</value>
  </data>
  <data name="&gt;&gt;lblName.Name" xml:space="preserve">
    <value>lblName</value>
  </data>
  <data name="&gt;&gt;lblName.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblName.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblName.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="lblNameValue.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="lblNameValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 203</value>
  </data>
  <data name="lblNameValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 23</value>
  </data>
  <data name="lblNameValue.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="lblNameValue.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;lblNameValue.Name" xml:space="preserve">
    <value>lblNameValue</value>
  </data>
  <data name="&gt;&gt;lblNameValue.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblNameValue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblNameValue.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="btnClipboardStatus.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnClipboardStatus.AutoSizeMode" type="System.Windows.Forms.AutoSizeMode, System.Windows.Forms">
    <value>GrowAndShrink</value>
  </data>
  <data name="btnClipboardStatus.FlatStyle" type="System.Windows.Forms.FlatStyle, System.Windows.Forms">
    <value>Flat</value>
  </data>
  <data name="btnClipboardStatus.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 9pt</value>
  </data>
  <data name="btnClipboardStatus.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnClipboardStatus.Location" type="System.Drawing.Point, System.Drawing">
    <value>436, 264</value>
  </data>
  <data name="btnClipboardStatus.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 24</value>
  </data>
  <data name="btnClipboardStatus.TabIndex" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="btnClipboardStatus.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>TextBeforeImage</value>
  </data>
  <data name="btnClipboardStatus.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btnClipboardStatus.Name" xml:space="preserve">
    <value>btnClipboardStatus</value>
  </data>
  <data name="&gt;&gt;btnClipboardStatus.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnClipboardStatus.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnClipboardStatus.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="mbCopy.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="mbCopy.Location" type="System.Drawing.Point, System.Drawing">
    <value>472, 296</value>
  </data>
  <data name="mbCopy.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 32</value>
  </data>
  <data name="mbCopy.TabIndex" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="mbCopy.Text" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="&gt;&gt;mbCopy.Name" xml:space="preserve">
    <value>mbCopy</value>
  </data>
  <data name="&gt;&gt;mbCopy.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.MenuButton, ShareX.HelpersLib, Version=********, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;mbCopy.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;mbCopy.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="pbColorPreview.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 232</value>
  </data>
  <data name="pbColorPreview.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 48</value>
  </data>
  <data name="pbColorPreview.TabIndex" type="System.Int32, mscorlib">
    <value>38</value>
  </data>
  <data name="&gt;&gt;pbColorPreview.Name" xml:space="preserve">
    <value>pbColorPreview</value>
  </data>
  <data name="&gt;&gt;pbColorPreview.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.MyPictureBox, ShareX.HelpersLib, Version=********, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;pbColorPreview.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pbColorPreview.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="colorPicker.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colorPicker.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 8</value>
  </data>
  <data name="colorPicker.Size" type="System.Drawing.Size, System.Drawing">
    <value>292, 261</value>
  </data>
  <data name="colorPicker.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;colorPicker.Name" xml:space="preserve">
    <value>colorPicker</value>
  </data>
  <data name="&gt;&gt;colorPicker.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.ColorPicker, ShareX.HelpersLib, Version=********, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;colorPicker.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;colorPicker.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>59</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>96, 96</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>681, 336</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Color picker</value>
  </data>
  <data name="&gt;&gt;ttMain.Name" xml:space="preserve">
    <value>ttMain</value>
  </data>
  <data name="&gt;&gt;ttMain.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolTip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiCopyAll.Name" xml:space="preserve">
    <value>tsmiCopyAll</value>
  </data>
  <data name="&gt;&gt;tsmiCopyAll.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiCopyRGB.Name" xml:space="preserve">
    <value>tsmiCopyRGB</value>
  </data>
  <data name="&gt;&gt;tsmiCopyRGB.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiCopyHexadecimal.Name" xml:space="preserve">
    <value>tsmiCopyHexadecimal</value>
  </data>
  <data name="&gt;&gt;tsmiCopyHexadecimal.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiCopyCMYK.Name" xml:space="preserve">
    <value>tsmiCopyCMYK</value>
  </data>
  <data name="&gt;&gt;tsmiCopyCMYK.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiCopyHSB.Name" xml:space="preserve">
    <value>tsmiCopyHSB</value>
  </data>
  <data name="&gt;&gt;tsmiCopyHSB.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiCopyDecimal.Name" xml:space="preserve">
    <value>tsmiCopyDecimal</value>
  </data>
  <data name="&gt;&gt;tsmiCopyDecimal.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmiCopyPosition.Name" xml:space="preserve">
    <value>tsmiCopyPosition</value>
  </data>
  <data name="&gt;&gt;tsmiCopyPosition.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>ColorPickerForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
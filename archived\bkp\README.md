# Sistema de Gravação de Tela para Cartório

Sistema completo para gravação de tela com certificação para uso em cartório, seguindo os requisitos da LGPD (Lei Geral de Proteção de Dados) e o Código de Processo Civil brasileiro para provas digitais.

## Estrutura do Projeto

O sistema é composto por três componentes principais, todos dockerizados para facilitar a implantação:

1. **Web App**: Aplicação web (PWA) para gravação de tela em desktop e dispositivos móveis
2. **Backend**: API Node.js + Express com banco de dados SQLite criptografado
3. **LaTeX Service**: Serviço para geração de relatórios PDF usando LaTeX

> **Nota**: A versão anterior incluía aplicativos nativos para desktop (C#) e mobile (Flutter). Essa abordagem foi substituída por uma solução web unificada em 04/07/2025. O código dos aplicativos nativos está preservado no diretório `archived-mobile` para referência futura.

## Requisitos do Sistema

Para executar o sistema completo, você precisará de:

- Docker e Docker Compose
- Node.js 16+ (para desenvolvimento da aplicação web)

## Configuração e Execução

### 1. Executando Todo o Sistema com Docker Compose

```bash
# Na raiz do projeto
docker-compose up -d
```

Isso iniciará:
- O servidor backend na porta 3000
- O serviço LaTeX para geração de relatórios PDF
- A aplicação web na porta 80

Você pode acessar a aplicação web em:
```
http://localhost
```

### 2. Executando a Aplicação Web em Modo de Desenvolvimento

```bash
# Na pasta da aplicação web
cd web-app
npm install
npm start
```

Isso iniciará o servidor de desenvolvimento na porta 3000, acessível em:
```
http://localhost:3000
```

## Capacidades da Aplicação Web

### Gravação de Tela no Navegador

A aplicação web utiliza a API [MediaDevices.getDisplayMedia()](https://developer.mozilla.org/en-US/docs/Web/API/MediaDevices/getDisplayMedia) para capturar a tela ou janelas, sem necessidade de instalação de software.

### Compatibilidade com Dispositivos Móveis

A aplicação funciona como um Progressive Web App (PWA), permitindo:
- Instalação na tela inicial
- Funcionamento offline parcial
- Acesso à câmera (em navegadores suportados)

### Segurança da Aplicação Web

- Armazenamento seguro de tokens
- Criptografia de dados sensíveis com Web Crypto API
- Validação de integridade com hash SHA-256

## Arquitetura e Segurança

### Segurança de Dados

- Todos os vídeos são armazenados com criptografia AES-256
- Cada gravação recebe um hash SHA-256 para verificação de integridade
- O consentimento LGPD é obrigatório antes de cada gravação
- Metadados sensíveis são criptografados no banco de dados

### Fluxo de Dados

1. Usuário acessa a aplicação web e faz login
2. Concede consentimento antes da gravação (conforme LGPD)
3. Gravação é realizada através da API do navegador
4. Hash SHA-256 é gerado para o arquivo de vídeo no navegador
5. Vídeo e metadados são enviados ao backend
6. Backend criptografa e armazena no SQLite
7. Relatório PDF pode ser gerado com LaTeX
8. PDF contém metadados e hash para autenticação em cartório

## Conformidade Legal

- **LGPD (Lei 13.709/2018)**: Consentimento explícito conforme Art. 7º
- **CPC (Lei 13.105/2015)**: Validação de integridade conforme Art. 369 para uso processual
- **Criptografia**: AES-256 para armazenamento seguro
- **Verificação de Integridade**: Hash SHA-256 para garantir autenticidade

## Contribuindo

Para contribuir com o projeto:

1. Faça um fork do repositório
2. Crie uma branch para sua feature (`git checkout -b feature/nova-funcionalidade`)
3. Commit suas mudanças (`git commit -m 'Adiciona nova funcionalidade'`)
4. Push para a branch (`git push origin feature/nova-funcionalidade`)
5. Abra um Pull Request

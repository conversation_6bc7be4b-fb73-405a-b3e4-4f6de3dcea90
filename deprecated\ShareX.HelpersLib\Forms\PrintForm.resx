﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="cbAutoRotate.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="cbAutoRotate.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 64</value>
  </data>
  <data name="cbAutoRotate.Size" type="System.Drawing.Size, System.Drawing">
    <value>109, 17</value>
  </data>
  <data name="cbAutoRotate.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cbAutoRotate.Text" xml:space="preserve">
    <value>Auto rotate image</value>
  </data>
  <data name="&gt;&gt;cbAutoRotate.Name" xml:space="preserve">
    <value>cbAutoRotate</value>
  </data>
  <data name="&gt;&gt;cbAutoRotate.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbAutoRotate.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbAutoRotate.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="nudMargin.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 32</value>
  </data>
  <data name="nudMargin.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="nudMargin.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="nudMargin.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudMargin.Name" xml:space="preserve">
    <value>nudMargin</value>
  </data>
  <data name="&gt;&gt;nudMargin.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudMargin.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudMargin.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="cbAutoScale.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbAutoScale.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 88</value>
  </data>
  <data name="cbAutoScale.Size" type="System.Drawing.Size, System.Drawing">
    <value>107, 17</value>
  </data>
  <data name="cbAutoScale.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="cbAutoScale.Text" xml:space="preserve">
    <value>Auto scale image</value>
  </data>
  <data name="&gt;&gt;cbAutoScale.Name" xml:space="preserve">
    <value>cbAutoScale</value>
  </data>
  <data name="&gt;&gt;cbAutoScale.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbAutoScale.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbAutoScale.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lblMargin.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblMargin.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 16</value>
  </data>
  <data name="lblMargin.Size" type="System.Drawing.Size, System.Drawing">
    <value>42, 13</value>
  </data>
  <data name="lblMargin.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lblMargin.Text" xml:space="preserve">
    <value>Margin:</value>
  </data>
  <data name="&gt;&gt;lblMargin.Name" xml:space="preserve">
    <value>lblMargin</value>
  </data>
  <data name="&gt;&gt;lblMargin.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblMargin.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblMargin.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="cbAllowEnlarge.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbAllowEnlarge.Location" type="System.Drawing.Point, System.Drawing">
    <value>40, 112</value>
  </data>
  <data name="cbAllowEnlarge.Size" type="System.Drawing.Size, System.Drawing">
    <value>132, 17</value>
  </data>
  <data name="cbAllowEnlarge.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="cbAllowEnlarge.Text" xml:space="preserve">
    <value>Allow image to enlarge</value>
  </data>
  <data name="&gt;&gt;cbAllowEnlarge.Name" xml:space="preserve">
    <value>cbAllowEnlarge</value>
  </data>
  <data name="&gt;&gt;cbAllowEnlarge.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbAllowEnlarge.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbAllowEnlarge.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="cbCenterImage.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbCenterImage.Location" type="System.Drawing.Point, System.Drawing">
    <value>40, 136</value>
  </data>
  <data name="cbCenterImage.Size" type="System.Drawing.Size, System.Drawing">
    <value>127, 17</value>
  </data>
  <data name="cbCenterImage.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="cbCenterImage.Text" xml:space="preserve">
    <value>Center image position</value>
  </data>
  <data name="&gt;&gt;cbCenterImage.Name" xml:space="preserve">
    <value>cbCenterImage</value>
  </data>
  <data name="&gt;&gt;cbCenterImage.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbCenterImage.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbCenterImage.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnShowPreview.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 162</value>
  </data>
  <data name="btnShowPreview.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 23</value>
  </data>
  <data name="btnShowPreview.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="btnShowPreview.Text" xml:space="preserve">
    <value>Preview...</value>
  </data>
  <data name="&gt;&gt;btnShowPreview.Name" xml:space="preserve">
    <value>btnShowPreview</value>
  </data>
  <data name="&gt;&gt;btnShowPreview.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnShowPreview.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnShowPreview.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnPrint.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 192</value>
  </data>
  <data name="btnPrint.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 23</value>
  </data>
  <data name="btnPrint.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="btnPrint.Text" xml:space="preserve">
    <value>Print...</value>
  </data>
  <data name="&gt;&gt;btnPrint.Name" xml:space="preserve">
    <value>btnPrint</value>
  </data>
  <data name="&gt;&gt;btnPrint.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnPrint.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnPrint.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>128, 192</value>
  </data>
  <data name="btnCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 23</value>
  </data>
  <data name="btnCancel.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Name" xml:space="preserve">
    <value>btnCancel</value>
  </data>
  <data name="&gt;&gt;btnCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnCancel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>96, 96</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>248, 222</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Print options</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>PrintForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="&gt;&gt;tscMain.BottomToolStripPanel.Name" xml:space="preserve">
    <value>tscMain.BottomToolStripPanel</value>
  </data>
  <data name="&gt;&gt;tscMain.BottomToolStripPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscMain.BottomToolStripPanel.Parent" xml:space="preserve">
    <value>tscMain</value>
  </data>
  <data name="&gt;&gt;tscMain.BottomToolStripPanel.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="ilvImages.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="ilvImages.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="ilvImages.Size" type="System.Drawing.Size, System.Drawing">
    <value>994, 636</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="ilvImages.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;ilvImages.Name" xml:space="preserve">
    <value>ilvImages</value>
  </data>
  <data name="&gt;&gt;ilvImages.Type" xml:space="preserve">
    <value>Manina.Windows.Forms.ImageListView, ImageListView, Version=13.8.2.0, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;ilvImages.Parent" xml:space="preserve">
    <value>tscMain.ContentPanel</value>
  </data>
  <data name="&gt;&gt;ilvImages.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tscMain.ContentPanel.Size" type="System.Drawing.Size, System.Drawing">
    <value>994, 636</value>
  </data>
  <data name="&gt;&gt;tscMain.ContentPanel.Name" xml:space="preserve">
    <value>tscMain.ContentPanel</value>
  </data>
  <data name="&gt;&gt;tscMain.ContentPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripContentPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscMain.ContentPanel.Parent" xml:space="preserve">
    <value>tscMain</value>
  </data>
  <data name="&gt;&gt;tscMain.ContentPanel.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tscMain.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="&gt;&gt;tscMain.LeftToolStripPanel.Name" xml:space="preserve">
    <value>tscMain.LeftToolStripPanel</value>
  </data>
  <data name="&gt;&gt;tscMain.LeftToolStripPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscMain.LeftToolStripPanel.Parent" xml:space="preserve">
    <value>tscMain</value>
  </data>
  <data name="&gt;&gt;tscMain.LeftToolStripPanel.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tscMain.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="&gt;&gt;tscMain.RightToolStripPanel.Name" xml:space="preserve">
    <value>tscMain.RightToolStripPanel</value>
  </data>
  <data name="&gt;&gt;tscMain.RightToolStripPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscMain.RightToolStripPanel.Parent" xml:space="preserve">
    <value>tscMain</value>
  </data>
  <data name="&gt;&gt;tscMain.RightToolStripPanel.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="tscMain.Size" type="System.Drawing.Size, System.Drawing">
    <value>994, 661</value>
  </data>
  <data name="tscMain.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <metadata name="tsMain.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="tsMain.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="tslSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 22</value>
  </data>
  <data name="tslSearch.Text" xml:space="preserve">
    <value>Search:</value>
  </data>
  <data name="tstbSearch.Font" type="System.Drawing.Font, System.Drawing">
    <value>Segoe UI, 9pt</value>
  </data>
  <data name="tstbSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>300, 25</value>
  </data>
  <data name="tsbSearch.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="tsbSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 22</value>
  </data>
  <data name="tsbSearch.Text" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="tsbSearch.ToolTipText" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="tss1.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 25</value>
  </data>
  <data name="tsbSettings.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="tsbSettings.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 22</value>
  </data>
  <data name="tsbSettings.Text" xml:space="preserve">
    <value>Settings...</value>
  </data>
  <data name="tsMain.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 0</value>
  </data>
  <data name="tsMain.Size" type="System.Drawing.Size, System.Drawing">
    <value>402, 25</value>
  </data>
  <data name="tsMain.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tsMain.Name" xml:space="preserve">
    <value>tsMain</value>
  </data>
  <data name="&gt;&gt;tsMain.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsMain.Parent" xml:space="preserve">
    <value>tscMain.TopToolStripPanel</value>
  </data>
  <data name="&gt;&gt;tsMain.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;tscMain.TopToolStripPanel.Name" xml:space="preserve">
    <value>tscMain.TopToolStripPanel</value>
  </data>
  <data name="&gt;&gt;tscMain.TopToolStripPanel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscMain.TopToolStripPanel.Parent" xml:space="preserve">
    <value>tscMain</value>
  </data>
  <data name="&gt;&gt;tscMain.TopToolStripPanel.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;tscMain.Name" xml:space="preserve">
    <value>tscMain</value>
  </data>
  <data name="&gt;&gt;tscMain.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripContainer, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tscMain.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;tscMain.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>96, 96</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>994, 661</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Image history</value>
  </data>
  <data name="&gt;&gt;tslSearch.Name" xml:space="preserve">
    <value>tslSearch</value>
  </data>
  <data name="&gt;&gt;tslSearch.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tstbSearch.Name" xml:space="preserve">
    <value>tstbSearch</value>
  </data>
  <data name="&gt;&gt;tstbSearch.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripTextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsbSearch.Name" xml:space="preserve">
    <value>tsbSearch</value>
  </data>
  <data name="&gt;&gt;tsbSearch.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tss1.Name" xml:space="preserve">
    <value>tss1</value>
  </data>
  <data name="&gt;&gt;tss1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsbSettings.Name" xml:space="preserve">
    <value>tsbSettings</value>
  </data>
  <data name="&gt;&gt;tsbSettings.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>ImageHistoryForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtException.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="txtException.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 48</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtException.Multiline" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtException.ScrollBars" type="System.Windows.Forms.ScrollBars, System.Windows.Forms">
    <value>Both</value>
  </data>
  <data name="txtException.Size" type="System.Drawing.Size, System.Drawing">
    <value>569, 320</value>
  </data>
  <data name="txtException.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtException.Name" xml:space="preserve">
    <value>txtException</value>
  </data>
  <data name="&gt;&gt;txtException.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtException.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtException.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnSendBugReport.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 3</value>
  </data>
  <data name="btnSendBugReport.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 3, 3, 3</value>
  </data>
  <data name="btnSendBugReport.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 26</value>
  </data>
  <data name="btnSendBugReport.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnSendBugReport.Text" xml:space="preserve">
    <value>Send bug report...</value>
  </data>
  <data name="&gt;&gt;btnSendBugReport.Name" xml:space="preserve">
    <value>btnSendBugReport</value>
  </data>
  <data name="&gt;&gt;btnSendBugReport.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnSendBugReport.Parent" xml:space="preserve">
    <value>flpMenu</value>
  </data>
  <data name="&gt;&gt;btnSendBugReport.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnClose.AutoSizeMode" type="System.Windows.Forms.AutoSizeMode, System.Windows.Forms">
    <value>GrowAndShrink</value>
  </data>
  <data name="btnClose.Location" type="System.Drawing.Point, System.Drawing">
    <value>438, 3</value>
  </data>
  <data name="btnClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 26</value>
  </data>
  <data name="btnClose.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnClose.Text" xml:space="preserve">
    <value>Quit</value>
  </data>
  <data name="&gt;&gt;btnClose.Name" xml:space="preserve">
    <value>btnClose</value>
  </data>
  <data name="&gt;&gt;btnClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnClose.Parent" xml:space="preserve">
    <value>flpMenu</value>
  </data>
  <data name="&gt;&gt;btnClose.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnOpenLogFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>156, 3</value>
  </data>
  <data name="btnOpenLogFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 26</value>
  </data>
  <data name="btnOpenLogFile.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnOpenLogFile.Text" xml:space="preserve">
    <value>Open log file...</value>
  </data>
  <data name="&gt;&gt;btnOpenLogFile.Name" xml:space="preserve">
    <value>btnOpenLogFile</value>
  </data>
  <data name="&gt;&gt;btnOpenLogFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOpenLogFile.Parent" xml:space="preserve">
    <value>flpMenu</value>
  </data>
  <data name="&gt;&gt;btnOpenLogFile.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="flpMenu.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="btnContinue.Location" type="System.Drawing.Point, System.Drawing">
    <value>312, 3</value>
  </data>
  <data name="btnContinue.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 26</value>
  </data>
  <data name="btnContinue.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnContinue.Text" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="&gt;&gt;btnContinue.Name" xml:space="preserve">
    <value>btnContinue</value>
  </data>
  <data name="&gt;&gt;btnContinue.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnContinue.Parent" xml:space="preserve">
    <value>flpMenu</value>
  </data>
  <data name="&gt;&gt;btnContinue.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="btnOK.AutoSizeMode" type="System.Windows.Forms.AutoSizeMode, System.Windows.Forms">
    <value>GrowAndShrink</value>
  </data>
  <data name="btnOK.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnOK.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 35</value>
  </data>
  <data name="btnOK.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 26</value>
  </data>
  <data name="btnOK.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="&gt;&gt;btnOK.Name" xml:space="preserve">
    <value>btnOK</value>
  </data>
  <data name="&gt;&gt;btnOK.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOK.Parent" xml:space="preserve">
    <value>flpMenu</value>
  </data>
  <data name="&gt;&gt;btnOK.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="flpMenu.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 373</value>
  </data>
  <data name="flpMenu.Size" type="System.Drawing.Size, System.Drawing">
    <value>569, 32</value>
  </data>
  <data name="flpMenu.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;flpMenu.Name" xml:space="preserve">
    <value>flpMenu</value>
  </data>
  <data name="&gt;&gt;flpMenu.Type" xml:space="preserve">
    <value>System.Windows.Forms.FlowLayoutPanel, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;flpMenu.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;flpMenu.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lblErrorMessage.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lblErrorMessage.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft Sans Serif, 9.75pt</value>
  </data>
  <data name="lblErrorMessage.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 8</value>
  </data>
  <data name="lblErrorMessage.Size" type="System.Drawing.Size, System.Drawing">
    <value>569, 34</value>
  </data>
  <data name="lblErrorMessage.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblErrorMessage.Text" xml:space="preserve">
    <value>Error
Error 2</value>
  </data>
  <data name="&gt;&gt;lblErrorMessage.Name" xml:space="preserve">
    <value>lblErrorMessage</value>
  </data>
  <data name="&gt;&gt;lblErrorMessage.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblErrorMessage.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblErrorMessage.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>96, 96</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>584, 411</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Error</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>ErrorForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
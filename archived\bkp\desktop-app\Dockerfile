FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /app

# Copiar os arquivos csproj e restaurar dependências
COPY *.csproj ./
RUN dotnet restore

# Copiar todo o código-fonte e compilar
COPY . ./
RUN dotnet publish -c Release -o out

FROM mcr.microsoft.com/dotnet/runtime:8.0 AS runtime
WORKDIR /app
COPY --from=build /app/out ./

# Expor porta para comunicação (se necessário)
EXPOSE 8080

ENTRYPOINT ["dotnet", "CartorioDesktopApp.dll"]

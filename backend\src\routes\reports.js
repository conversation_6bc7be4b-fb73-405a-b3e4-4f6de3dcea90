const express = require('express');
const path = require('path');
const fs = require('fs');
const { getRecordingById, saveReport, updateNotaryInfo } = require('../database');

const router = express.Router();

// Gerar um relatório PDF para uma gravação
router.post('/:recordingId', async (req, res, next) => {
  try {
    const recording = await getRecordingById(req.params.recordingId);
    
    if (!recording) {
      return res.status(404).json({ error: 'Gravação não encontrada' });
    }
    
    // Chamar o serviço LaTeX para gerar o relatório PDF
    // Este é apenas um endpoint de API que delegará a geração para o serviço LaTeX
    try {
      // Aqui, em vez de gerar o PDF diretamente, enviaremos uma requisição para o serviço LaTeX
      // Em um ambiente de produção real, isso seria feito com uma chamada HTTP para o serviço LaTeX
      // Para simplificar, vamos simular que o PDF foi gerado
      
      const pdfFileName = `report-${req.params.recordingId}.pdf`;
      const pdfPath = path.join(__dirname, '../../storage', pdfFileName);
      
      // Na implementação real, o serviço LaTeX irá gerar esse arquivo
      // Aqui, apenas criamos um placeholder para simular
      fs.writeFileSync(pdfPath, 'PDF placeholder'); // Na implementação real, este trecho não existe
      
      // Salvar o relatório no banco de dados
      const reportId = await saveReport(req.params.recordingId, pdfPath);
      
      res.json({
        id: reportId,
        recording_id: req.params.recordingId,
        pdf_path: pdfPath,
        message: 'Relatório em PDF solicitado com sucesso'
      });
    } catch (error) {
      console.error('Erro ao gerar PDF:', error);
      res.status(500).json({ error: 'Erro ao gerar o relatório em PDF' });
    }
  } catch (error) {
    next(error);
  }
});

// Download de um relatório PDF
router.get('/:reportId/download', async (req, res, next) => {
  try {
    // Buscar relatório no banco de dados
    const report = await getReportById(req.params.reportId);
    
    if (!report) {
      return res.status(404).json({ error: 'Relatório não encontrado' });
    }
    
    // Verificar se o arquivo existe
    if (!fs.existsSync(report.pdf_path)) {
      return res.status(404).json({ error: 'Arquivo PDF não encontrado' });
    }
    
    // Enviar o arquivo
    res.download(report.pdf_path, path.basename(report.pdf_path));
  } catch (error) {
    next(error);
  }
});

// Atualizar informações de autenticação em cartório
router.post('/:reportId/notary', async (req, res, next) => {
  try {
    const { notary_name, notary_registration, authentication_date, authentication_code } = req.body;
    
    // Validar campos obrigatórios
    if (!notary_name || !notary_registration || !authentication_date || !authentication_code) {
      return res.status(400).json({ error: 'Todos os campos de autenticação são obrigatórios' });
    }
    
    const notaryInfo = {
      notary_name,
      notary_registration,
      authentication_date,
      authentication_code,
      updated_at: new Date().toISOString()
    };
    
    const updated = await updateNotaryInfo(req.params.reportId, notaryInfo);
    
    if (!updated) {
      return res.status(404).json({ error: 'Relatório não encontrado' });
    }
    
    res.json({
      report_id: req.params.reportId,
      message: 'Informações de autenticação atualizadas com sucesso',
      notary_info: notaryInfo
    });
  } catch (error) {
    next(error);
  }
});

// Função auxiliar para buscar um relatório por ID
async function getReportById(id) {
  return new Promise((resolve, reject) => {
    db.get('SELECT * FROM reports WHERE id = ?', [id], (err, row) => {
      if (err) {
        console.error('Erro ao buscar relatório:', err.message);
        reject(err);
        return;
      }
      resolve(row);
    });
  });
}

module.exports = router;

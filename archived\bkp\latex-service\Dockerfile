FROM ubuntu:22.04

# Evitar perguntas interativas durante a instalação
ENV DEBIAN_FRONTEND=noninteractive

# Instalar dependências e TeX Live
RUN apt-get update && apt-get install -y \
    texlive-full \
    texlive-fonts-extra \
    nodejs \
    npm \
    python3 \
    python3-pip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY package*.json ./

RUN npm install

COPY . .

VOLUME /app/storage

CMD ["node", "src/service.js"]

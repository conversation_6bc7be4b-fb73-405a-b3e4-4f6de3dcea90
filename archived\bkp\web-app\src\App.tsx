import { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';

import { useApp } from './contexts/AppContext';
import { getTheme } from './utils/theme';

// Layouts
import MainLayout from './components/MainLayout';

// Páginas
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import RecordPage from './pages/RecordPage';
import RecordingsPage from './pages/RecordingsPage';
import SettingsPage from './pages/SettingsPage';
import ProfilePage from './pages/ProfilePage';
import AdminPage from './pages/AdminPage';
import SubscriptionPage from './pages/SubscriptionPage';
import NotFoundPage from './pages/NotFoundPage';

// Interface para rotas protegidas
interface ProtectedRouteProps {
  children: JSX.Element;
  isAuthenticated: boolean;
  adminRequired?: boolean;
  isAdmin?: boolean;
  subscriptionRequired?: boolean;
  hasSubscription?: boolean;
  redirectTo?: string;
}

// Componente para proteger rotas
const ProtectedRoute = ({
  children,
  isAuthenticated,
  adminRequired = false,
  isAdmin = false,
  subscriptionRequired = false,
  hasSubscription = false,
  redirectTo = '/login'
}: ProtectedRouteProps) => {
  // Verificar autenticação básica
  if (!isAuthenticated) {
    return <Navigate to={redirectTo} replace />;
  }
  
  // Verificar se é admin quando necessário
  if (adminRequired && !isAdmin) {
    return <Navigate to="/" replace />;
  }
  
  // Verificar se tem assinatura quando necessário
  if (subscriptionRequired && !hasSubscription && !isAdmin) {
    return <Navigate to="/subscription" replace />;
  }
  
  return children;
};

function App() {
  const { 
    darkMode, 
    isAuthenticated, 
    isAdmin,
    hasActiveSubscription
  } = useApp();
  
  const theme = getTheme(darkMode);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Simular carregamento inicial
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  if (loading) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          height: '100vh'
        }}>
          Carregando...
        </div>
      </ThemeProvider>
    );
  }
  
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Routes>
        {/* Rotas públicas */}
        <Route path="/login" element={
          isAuthenticated ? <Navigate to="/" replace /> : <LoginPage />
        } />
        <Route path="/register" element={
          isAuthenticated ? <Navigate to="/" replace /> : <RegisterPage />
        } />
        
        {/* Rotas protegidas */}
        <Route path="/" element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <MainLayout>
              <HomePage />
            </MainLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/record" element={
          <ProtectedRoute 
            isAuthenticated={isAuthenticated}
            subscriptionRequired={true}
            hasSubscription={hasActiveSubscription}
            isAdmin={isAdmin}
          >
            <MainLayout>
              <RecordPage />
            </MainLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/recordings" element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <MainLayout>
              <RecordingsPage />
            </MainLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/settings" element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <MainLayout>
              <SettingsPage />
            </MainLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/profile" element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <MainLayout>
              <ProfilePage />
            </MainLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/subscription" element={
          <ProtectedRoute isAuthenticated={isAuthenticated}>
            <MainLayout>
              <SubscriptionPage />
            </MainLayout>
          </ProtectedRoute>
        } />
        
        <Route path="/admin" element={
          <ProtectedRoute 
            isAuthenticated={isAuthenticated}
            adminRequired={true}
            isAdmin={isAdmin}
          >
            <MainLayout>
              <AdminPage />
            </MainLayout>
          </ProtectedRoute>
        } />
        
        {/* Rota 404 */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </ThemeProvider>
  );
}

export default App;

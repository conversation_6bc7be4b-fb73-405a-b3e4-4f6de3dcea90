﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ShareX.HelpersLib.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ShareX.HelpersLib.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File path.
        /// </summary>
        internal static string ActionsCodeMenuEntry_FilePath_File_path {
            get {
                return ResourceManager.GetString("ActionsCodeMenuEntry_FilePath_File_path", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File path with output file name extension.
        /// </summary>
        internal static string ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_ {
            get {
                return ResourceManager.GetString("ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_na" +
                        "me_extension_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to abandoned
        ///able
        ///absolute
        ///academic
        ///acceptable
        ///acclaimed
        ///accomplished
        ///accurate
        ///aching
        ///acidic
        ///acrobatic
        ///adorable
        ///adventurous
        ///babyish
        ///back
        ///bad
        ///baggy
        ///bare
        ///barren
        ///basic
        ///beautiful
        ///belated
        ///beloved
        ///calculating
        ///calm
        ///candid
        ///canine
        ///capital
        ///carefree
        ///careful
        ///careless
        ///caring
        ///cautious
        ///cavernous
        ///celebrated
        ///charming
        ///damaged
        ///damp
        ///dangerous
        ///dapper
        ///daring
        ///dark
        ///darling
        ///dazzling
        ///dead
        ///deadly
        ///deafening
        ///dear
        ///dearest
        ///each
        ///eager
        ///early
        ///earnest
        ///easy
        ///easygoing
        ///ecstatic
        ///edible
        ///educat [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string adjectives {
            get {
                return ResourceManager.GetString("adjectives", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add image effects.
        /// </summary>
        internal static string AfterCaptureTasks_AddImageEffects {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_AddImageEffects", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open in image editor.
        /// </summary>
        internal static string AfterCaptureTasks_AnnotateImage {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_AnnotateImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beautify image.
        /// </summary>
        internal static string AfterCaptureTasks_BeautifyImage {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_BeautifyImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy file path to clipboard.
        /// </summary>
        internal static string AfterCaptureTasks_CopyFilePathToClipboard {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_CopyFilePathToClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy file to clipboard.
        /// </summary>
        internal static string AfterCaptureTasks_CopyFileToClipboard {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_CopyFileToClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy image to clipboard.
        /// </summary>
        internal static string AfterCaptureTasks_CopyImageToClipboard {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_CopyImageToClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete file locally.
        /// </summary>
        internal static string AfterCaptureTasks_DeleteFile {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_DeleteFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recognize text (OCR).
        /// </summary>
        internal static string AfterCaptureTasks_DoOCR {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_DoOCR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Perform actions.
        /// </summary>
        internal static string AfterCaptureTasks_PerformActions {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_PerformActions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pin to screen.
        /// </summary>
        internal static string AfterCaptureTasks_PinToScreen {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_PinToScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save image to file.
        /// </summary>
        internal static string AfterCaptureTasks_SaveImageToFile {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_SaveImageToFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save image to file as....
        /// </summary>
        internal static string AfterCaptureTasks_SaveImageToFileWithDialog {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_SaveImageToFileWithDialog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save thumbnail image to file.
        /// </summary>
        internal static string AfterCaptureTasks_SaveThumbnailImageToFile {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_SaveThumbnailImageToFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scan QR code.
        /// </summary>
        internal static string AfterCaptureTasks_ScanQRCode {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_ScanQRCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print image.
        /// </summary>
        internal static string AfterCaptureTasks_SendImageToPrinter {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_SendImageToPrinter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show &quot;After capture&quot; window.
        /// </summary>
        internal static string AfterCaptureTasks_ShowAfterCaptureWindow {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_ShowAfterCaptureWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show &quot;Before upload&quot; window.
        /// </summary>
        internal static string AfterCaptureTasks_ShowBeforeUploadWindow {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_ShowBeforeUploadWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show file in explorer.
        /// </summary>
        internal static string AfterCaptureTasks_ShowInExplorer {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_ShowInExplorer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show quick task menu.
        /// </summary>
        internal static string AfterCaptureTasks_ShowQuickTaskMenu {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_ShowQuickTaskMenu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload image to host.
        /// </summary>
        internal static string AfterCaptureTasks_UploadImageToHost {
            get {
                return ResourceManager.GetString("AfterCaptureTasks_UploadImageToHost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy URL to clipboard.
        /// </summary>
        internal static string AfterUploadTasks_CopyURLToClipboard {
            get {
                return ResourceManager.GetString("AfterUploadTasks_CopyURLToClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open URL.
        /// </summary>
        internal static string AfterUploadTasks_OpenURL {
            get {
                return ResourceManager.GetString("AfterUploadTasks_OpenURL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Share URL.
        /// </summary>
        internal static string AfterUploadTasks_ShareURL {
            get {
                return ResourceManager.GetString("AfterUploadTasks_ShareURL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show &quot;After upload&quot; window.
        /// </summary>
        internal static string AfterUploadTasks_ShowAfterUploadWindow {
            get {
                return ResourceManager.GetString("AfterUploadTasks_ShowAfterUploadWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show QR code window.
        /// </summary>
        internal static string AfterUploadTasks_ShowQRCode {
            get {
                return ResourceManager.GetString("AfterUploadTasks_ShowQRCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shorten URL.
        /// </summary>
        internal static string AfterUploadTasks_UseURLShortener {
            get {
                return ResourceManager.GetString("AfterUploadTasks_UseURLShortener", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to aardvark
        ///aardwolf
        ///abalone
        ///abyssiniancat
        ///abyssiniangroundhornbill
        ///acaciarat
        ///achillestang
        ///acornbarnacle
        ///acornweevil
        ///acornwoodpecker
        ///acouchi
        ///adamsstaghornedbeetle
        ///addax
        ///adder
        ///adeliepenguin
        ///admiralbutterfly
        ///adouri
        ///aegeancat
        ///affenpinscher
        ///afghanhound
        ///africanaugurbuzzard
        ///africanbushviper
        ///africancivet
        ///africanclawedfrog
        ///africanelephant
        ///africanfisheagle
        ///africangoldencat
        ///africangroundhornbill
        ///africanharrierhawk
        ///africanhornbill
        ///africanjacana
        ///africanmolesnake
        ///africanparadiseflycatcher
        ///a [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string animals {
            get {
                return ResourceManager.GetString("animals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Both.
        /// </summary>
        internal static string ArrowHeadDirection_Both {
            get {
                return ResourceManager.GetString("ArrowHeadDirection_Both", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End.
        /// </summary>
        internal static string ArrowHeadDirection_End {
            get {
                return ResourceManager.GetString("ArrowHeadDirection_End", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        internal static string ArrowHeadDirection_Start {
            get {
                return ResourceManager.GetString("ArrowHeadDirection_Start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dash.
        /// </summary>
        internal static string BorderStyle_Dash {
            get {
                return ResourceManager.GetString("BorderStyle_Dash", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DashDot.
        /// </summary>
        internal static string BorderStyle_DashDot {
            get {
                return ResourceManager.GetString("BorderStyle_DashDot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DashDotDot.
        /// </summary>
        internal static string BorderStyle_DashDotDot {
            get {
                return ResourceManager.GetString("BorderStyle_DashDotDot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dot.
        /// </summary>
        internal static string BorderStyle_Dot {
            get {
                return ResourceManager.GetString("BorderStyle_Dot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Solid.
        /// </summary>
        internal static string BorderStyle_Solid {
            get {
                return ResourceManager.GetString("BorderStyle_Solid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check.
        /// </summary>
        internal static string Check {
            get {
                return ResourceManager.GetString("Check", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap clipboard_block {
            get {
                object obj = ResourceManager.GetObject("clipboard-block", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cyan: {0:0.0}%, Magenta: {1:0.0}%, Yellow: {2:0.0}%, Key: {3:0.0}%.
        /// </summary>
        internal static string CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__ {
            get {
                return ResourceManager.GetString("CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        internal static string CodeMenu_Create_Close {
            get {
                return ResourceManager.GetString("CodeMenu_Create_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random adjective.
        /// </summary>
        internal static string CodeMenuEntryFilename_RandomAdjective {
            get {
                return ResourceManager.GetString("CodeMenuEntryFilename_RandomAdjective", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random animal.
        /// </summary>
        internal static string CodeMenuEntryFilename_RandomAnimal {
            get {
                return ResourceManager.GetString("CodeMenuEntryFilename_RandomAnimal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom center.
        /// </summary>
        internal static string ContentAlignment_BottomCenter {
            get {
                return ResourceManager.GetString("ContentAlignment_BottomCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom left.
        /// </summary>
        internal static string ContentAlignment_BottomLeft {
            get {
                return ResourceManager.GetString("ContentAlignment_BottomLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom right.
        /// </summary>
        internal static string ContentAlignment_BottomRight {
            get {
                return ResourceManager.GetString("ContentAlignment_BottomRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle center.
        /// </summary>
        internal static string ContentAlignment_MiddleCenter {
            get {
                return ResourceManager.GetString("ContentAlignment_MiddleCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle left.
        /// </summary>
        internal static string ContentAlignment_MiddleLeft {
            get {
                return ResourceManager.GetString("ContentAlignment_MiddleLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Middle right.
        /// </summary>
        internal static string ContentAlignment_MiddleRight {
            get {
                return ResourceManager.GetString("ContentAlignment_MiddleRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top center.
        /// </summary>
        internal static string ContentAlignment_TopCenter {
            get {
                return ResourceManager.GetString("ContentAlignment_TopCenter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top left.
        /// </summary>
        internal static string ContentAlignment_TopLeft {
            get {
                return ResourceManager.GetString("ContentAlignment_TopLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top right.
        /// </summary>
        internal static string ContentAlignment_TopRight {
            get {
                return ResourceManager.GetString("ContentAlignment_TopRight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap cross {
            get {
                object obj = ResourceManager.GetObject("cross", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Byte[].
        /// </summary>
        internal static byte[] Crosshair {
            get {
                object obj = ResourceManager.GetObject("Crosshair", resourceCulture);
                return ((byte[])(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse for a Cascading Style Sheet....
        /// </summary>
        internal static string CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___ {
            get {
                return ResourceManager.GetString("CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File uploader.
        /// </summary>
        internal static string CustomUploaderDestinationType_FileUploader {
            get {
                return ResourceManager.GetString("CustomUploaderDestinationType_FileUploader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image uploader.
        /// </summary>
        internal static string CustomUploaderDestinationType_ImageUploader {
            get {
                return ResourceManager.GetString("CustomUploaderDestinationType_ImageUploader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text uploader.
        /// </summary>
        internal static string CustomUploaderDestinationType_TextUploader {
            get {
                return ResourceManager.GetString("CustomUploaderDestinationType_TextUploader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URL sharing service.
        /// </summary>
        internal static string CustomUploaderDestinationType_URLSharingService {
            get {
                return ResourceManager.GetString("CustomUploaderDestinationType_URLSharingService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URL shortener.
        /// </summary>
        internal static string CustomUploaderDestinationType_URLShortener {
            get {
                return ResourceManager.GetString("CustomUploaderDestinationType_URLShortener", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No effect.
        /// </summary>
        internal static string CutOutEffectType_None {
            get {
                return ResourceManager.GetString("CutOutEffectType_None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Torn edges.
        /// </summary>
        internal static string CutOutEffectType_TornEdge {
            get {
                return ResourceManager.GetString("CutOutEffectType_TornEdge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wave.
        /// </summary>
        internal static string CutOutEffectType_Wave {
            get {
                return ResourceManager.GetString("CutOutEffectType_Wave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sawtooth.
        /// </summary>
        internal static string CutOutEffectType_ZigZag {
            get {
                return ResourceManager.GetString("CutOutEffectType_ZigZag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse for a folder....
        /// </summary>
        internal static string DirectoryNameEditor_EditValue_Browse_for_a_folder___ {
            get {
                return ResourceManager.GetString("DirectoryNameEditor_EditValue_Browse_for_a_folder___", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status: {0}.
        /// </summary>
        internal static string DownloaderForm_ChangeStatus_Status___0_ {
            get {
                return ResourceManager.GetString("DownloaderForm_ChangeStatus_Status___0_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filename: {0}.
        /// </summary>
        internal static string DownloaderForm_DownloaderForm_Filename___0_ {
            get {
                return ResourceManager.GetString("DownloaderForm_DownloaderForm_Filename___0_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Waiting....
        /// </summary>
        internal static string DownloaderForm_DownloaderForm_Waiting_ {
            get {
                return ResourceManager.GetString("DownloaderForm_DownloaderForm_Waiting_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download completed..
        /// </summary>
        internal static string DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_ {
            get {
                return ResourceManager.GetString("DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Install.
        /// </summary>
        internal static string DownloaderForm_fileDownloader_DownloadCompleted_Install {
            get {
                return ResourceManager.GetString("DownloaderForm_fileDownloader_DownloadCompleted_Install", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download speed.
        /// </summary>
        internal static string DownloaderForm_FileDownloader_ProgressChanged_DownloadSpeed {
            get {
                return ResourceManager.GetString("DownloaderForm_FileDownloader_ProgressChanged_DownloadSpeed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File size.
        /// </summary>
        internal static string DownloaderForm_FileDownloader_ProgressChanged_FileSize {
            get {
                return ResourceManager.GetString("DownloaderForm_FileDownloader_ProgressChanged_FileSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Progress.
        /// </summary>
        internal static string DownloaderForm_FileDownloader_ProgressChanged_Progress {
            get {
                return ResourceManager.GetString("DownloaderForm_FileDownloader_ProgressChanged_Progress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        internal static string DownloaderForm_StartDownload_Cancel {
            get {
                return ResourceManager.GetString("DownloaderForm_StartDownload_Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Downloading....
        /// </summary>
        internal static string DownloaderForm_StartDownload_Downloading_ {
            get {
                return ResourceManager.GetString("DownloaderForm_StartDownload_Downloading_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Getting file size....
        /// </summary>
        internal static string DownloaderForm_StartDownload_Getting_file_size_ {
            get {
                return ResourceManager.GetString("DownloaderForm_StartDownload_Getting_file_size_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Absolute size.
        /// </summary>
        internal static string DrawImageSizeMode_AbsoluteSize {
            get {
                return ResourceManager.GetString("DrawImageSizeMode_AbsoluteSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Don&apos;t resize.
        /// </summary>
        internal static string DrawImageSizeMode_DontResize {
            get {
                return ResourceManager.GetString("DrawImageSizeMode_DontResize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Percentage of canvas.
        /// </summary>
        internal static string DrawImageSizeMode_PercentageOfCanvas {
            get {
                return ResourceManager.GetString("DrawImageSizeMode_PercentageOfCanvas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Percentage of image.
        /// </summary>
        internal static string DrawImageSizeMode_PercentageOfWatermark {
            get {
                return ResourceManager.GetString("DrawImageSizeMode_PercentageOfWatermark", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default.
        /// </summary>
        internal static string EDataType_Default {
            get {
                return ResourceManager.GetString("EDataType_Default", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File.
        /// </summary>
        internal static string EDataType_File {
            get {
                return ResourceManager.GetString("EDataType_File", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image.
        /// </summary>
        internal static string EDataType_Image {
            get {
                return ResourceManager.GetString("EDataType_Image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text.
        /// </summary>
        internal static string EDataType_Text {
            get {
                return ResourceManager.GetString("EDataType_Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URL.
        /// </summary>
        internal static string EDataType_URL {
            get {
                return ResourceManager.GetString("EDataType_URL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse for executable....
        /// </summary>
        internal static string ExeFileNameEditor_EditValue_Browse_for_executable___ {
            get {
                return ResourceManager.GetString("ExeFileNameEditor_EditValue_Browse_for_executable___", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import failed..
        /// </summary>
        internal static string ExportImportControl_Deserialize_Import_failed_ {
            get {
                return ResourceManager.GetString("ExportImportControl_Deserialize_Import_failed_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export failed..
        /// </summary>
        internal static string ExportImportControl_Serialize_Export_failed_ {
            get {
                return ResourceManager.GetString("ExportImportControl_Serialize_Export_failed_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings copied to your clipboard..
        /// </summary>
        internal static string ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_ {
            get {
                return ResourceManager.GetString("ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URL to download settings from.
        /// </summary>
        internal static string ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from {
            get {
                return ResourceManager.GetString("ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy.
        /// </summary>
        internal static string Extensions_AddContextMenu_Copy {
            get {
                return ResourceManager.GetString("Extensions_AddContextMenu_Copy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cut.
        /// </summary>
        internal static string Extensions_AddContextMenu_Cut {
            get {
                return ResourceManager.GetString("Extensions_AddContextMenu_Cut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        internal static string Extensions_AddContextMenu_Delete {
            get {
                return ResourceManager.GetString("Extensions_AddContextMenu_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paste.
        /// </summary>
        internal static string Extensions_AddContextMenu_Paste {
            get {
                return ResourceManager.GetString("Extensions_AddContextMenu_Paste", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Redo.
        /// </summary>
        internal static string Extensions_AddContextMenu_Redo {
            get {
                return ResourceManager.GetString("Extensions_AddContextMenu_Redo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select All.
        /// </summary>
        internal static string Extensions_AddContextMenu_SelectAll {
            get {
                return ResourceManager.GetString("Extensions_AddContextMenu_SelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Undo.
        /// </summary>
        internal static string Extensions_AddContextMenu_Undo {
            get {
                return ResourceManager.GetString("Extensions_AddContextMenu_Undo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom file uploader.
        /// </summary>
        internal static string FileDestination_CustomFileUploader {
            get {
                return ResourceManager.GetString("FileDestination_CustomFileUploader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string FileDestination_Email {
            get {
                return ResourceManager.GetString("FileDestination_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shared folder.
        /// </summary>
        internal static string FileDestination_SharedFolder {
            get {
                return ResourceManager.GetString("FileDestination_SharedFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ask what to do.
        /// </summary>
        internal static string FileExistAction_Ask {
            get {
                return ResourceManager.GetString("FileExistAction_Ask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do not save.
        /// </summary>
        internal static string FileExistAction_Cancel {
            get {
                return ResourceManager.GetString("FileExistAction_Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overwrite file.
        /// </summary>
        internal static string FileExistAction_Overwrite {
            get {
                return ResourceManager.GetString("FileExistAction_Overwrite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Append number to the filename.
        /// </summary>
        internal static string FileExistAction_UniqueName {
            get {
                return ResourceManager.GetString("FileExistAction_UniqueName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a folder.
        /// </summary>
        internal static string FolderSelectDialog_Title_Select_a_folder {
            get {
                return ResourceManager.GetString("FolderSelectDialog_Title_Select_a_folder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Octree quantizer 16 colors.
        /// </summary>
        internal static string GIFQuality_Bit4 {
            get {
                return ResourceManager.GetString("GIFQuality_Bit4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Octree quantizer 256 colors (Slow encoding but better quality).
        /// </summary>
        internal static string GIFQuality_Bit8 {
            get {
                return ResourceManager.GetString("GIFQuality_Bit8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default .NET encoding (Fast encoding but average quality).
        /// </summary>
        internal static string GIFQuality_Default {
            get {
                return ResourceManager.GetString("GIFQuality_Default", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Palette quantizer grayscale 256 colors.
        /// </summary>
        internal static string GIFQuality_Grayscale {
            get {
                return ResourceManager.GetString("GIFQuality_Grayscale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose file.
        /// </summary>
        internal static string Helpers_BrowseFile_Choose_file {
            get {
                return ResourceManager.GetString("Helpers_BrowseFile_Choose_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose folder.
        /// </summary>
        internal static string Helpers_BrowseFolder_Choose_folder {
            get {
                return ResourceManager.GetString("Helpers_BrowseFolder_Choose_folder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Could not create directory..
        /// </summary>
        internal static string Helpers_CreateDirectoryIfNotExist_Create_failed_ {
            get {
                return ResourceManager.GetString("Helpers_CreateDirectoryIfNotExist_Create_failed_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download failed:.
        /// </summary>
        internal static string Helpers_DownloadString_Download_failed_ {
            get {
                return ResourceManager.GetString("Helpers_DownloadString_Download_failed_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File does not exist:.
        /// </summary>
        internal static string Helpers_OpenFile_File_not_exist_ {
            get {
                return ResourceManager.GetString("Helpers_OpenFile_File_not_exist_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Folder does not exist:.
        /// </summary>
        internal static string Helpers_OpenFolder_Folder_not_exist_ {
            get {
                return ResourceManager.GetString("Helpers_OpenFolder_Folder_not_exist_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abort screen recording.
        /// </summary>
        internal static string HotkeyType_AbortScreenRecording {
            get {
                return ResourceManager.GetString("HotkeyType_AbortScreenRecording", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture active monitor.
        /// </summary>
        internal static string HotkeyType_ActiveMonitor {
            get {
                return ResourceManager.GetString("HotkeyType_ActiveMonitor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture active window.
        /// </summary>
        internal static string HotkeyType_ActiveWindow {
            get {
                return ResourceManager.GetString("HotkeyType_ActiveWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make active window borderless.
        /// </summary>
        internal static string HotkeyType_ActiveWindowBorderless {
            get {
                return ResourceManager.GetString("HotkeyType_ActiveWindowBorderless", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make active window top most.
        /// </summary>
        internal static string HotkeyType_ActiveWindowTopMost {
            get {
                return ResourceManager.GetString("HotkeyType_ActiveWindowTopMost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto capture.
        /// </summary>
        internal static string HotkeyType_AutoCapture {
            get {
                return ResourceManager.GetString("HotkeyType_AutoCapture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Borderless window.
        /// </summary>
        internal static string HotkeyType_BorderlessWindow {
            get {
                return ResourceManager.GetString("HotkeyType_BorderlessWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Webpage capture.
        /// </summary>
        internal static string HotkeyType_CaptureWebpage {
            get {
                return ResourceManager.GetString("HotkeyType_CaptureWebpage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other.
        /// </summary>
        internal static string HotkeyType_Category_Other {
            get {
                return ResourceManager.GetString("HotkeyType_Category_Other", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Screen capture.
        /// </summary>
        internal static string HotkeyType_Category_ScreenCapture {
            get {
                return ResourceManager.GetString("HotkeyType_Category_ScreenCapture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Screen record.
        /// </summary>
        internal static string HotkeyType_Category_ScreenRecord {
            get {
                return ResourceManager.GetString("HotkeyType_Category_ScreenRecord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tools.
        /// </summary>
        internal static string HotkeyType_Category_Tools {
            get {
                return ResourceManager.GetString("HotkeyType_Category_Tools", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload.
        /// </summary>
        internal static string HotkeyType_Category_Upload {
            get {
                return ResourceManager.GetString("HotkeyType_Category_Upload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload from clipboard.
        /// </summary>
        internal static string HotkeyType_ClipboardUpload {
            get {
                return ResourceManager.GetString("HotkeyType_ClipboardUpload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload from clipboard with content viewer.
        /// </summary>
        internal static string HotkeyType_ClipboardUploadWithContentViewer {
            get {
                return ResourceManager.GetString("HotkeyType_ClipboardUploadWithContentViewer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clipboard viewer.
        /// </summary>
        internal static string HotkeyType_ClipboardViewer {
            get {
                return ResourceManager.GetString("HotkeyType_ClipboardViewer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color picker.
        /// </summary>
        internal static string HotkeyType_ColorPicker {
            get {
                return ResourceManager.GetString("HotkeyType_ColorPicker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture pre configured region.
        /// </summary>
        internal static string HotkeyType_CustomRegion {
            get {
                return ResourceManager.GetString("HotkeyType_CustomRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture pre configured window.
        /// </summary>
        internal static string HotkeyType_CustomWindow {
            get {
                return ResourceManager.GetString("HotkeyType_CustomWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable/Enable hotkeys.
        /// </summary>
        internal static string HotkeyType_DisableHotkeys {
            get {
                return ResourceManager.GetString("HotkeyType_DisableHotkeys", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drag and drop upload.
        /// </summary>
        internal static string HotkeyType_DragDropUpload {
            get {
                return ResourceManager.GetString("HotkeyType_DragDropUpload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exit ShareX.
        /// </summary>
        internal static string HotkeyType_ExitShareX {
            get {
                return ResourceManager.GetString("HotkeyType_ExitShareX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload file.
        /// </summary>
        internal static string HotkeyType_FileUpload {
            get {
                return ResourceManager.GetString("HotkeyType_FileUpload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload folder.
        /// </summary>
        internal static string HotkeyType_FolderUpload {
            get {
                return ResourceManager.GetString("HotkeyType_FolderUpload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hash checker.
        /// </summary>
        internal static string HotkeyType_HashCheck {
            get {
                return ResourceManager.GetString("HotkeyType_HashCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image beautifier.
        /// </summary>
        internal static string HotkeyType_ImageBeautifier {
            get {
                return ResourceManager.GetString("HotkeyType_ImageBeautifier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image combiner.
        /// </summary>
        internal static string HotkeyType_ImageCombiner {
            get {
                return ResourceManager.GetString("HotkeyType_ImageCombiner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image editor.
        /// </summary>
        internal static string HotkeyType_ImageEditor {
            get {
                return ResourceManager.GetString("HotkeyType_ImageEditor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image effects.
        /// </summary>
        internal static string HotkeyType_ImageEffects {
            get {
                return ResourceManager.GetString("HotkeyType_ImageEffects", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image splitter.
        /// </summary>
        internal static string HotkeyType_ImageSplitter {
            get {
                return ResourceManager.GetString("HotkeyType_ImageSplitter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image thumbnailer.
        /// </summary>
        internal static string HotkeyType_ImageThumbnailer {
            get {
                return ResourceManager.GetString("HotkeyType_ImageThumbnailer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image viewer.
        /// </summary>
        internal static string HotkeyType_ImageViewer {
            get {
                return ResourceManager.GetString("HotkeyType_ImageViewer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Directory indexer.
        /// </summary>
        internal static string HotkeyType_IndexFolder {
            get {
                return ResourceManager.GetString("HotkeyType_IndexFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspect window.
        /// </summary>
        internal static string HotkeyType_InspectWindow {
            get {
                return ResourceManager.GetString("HotkeyType_InspectWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture last region.
        /// </summary>
        internal static string HotkeyType_LastRegion {
            get {
                return ResourceManager.GetString("HotkeyType_LastRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Metadata.
        /// </summary>
        internal static string HotkeyType_Metadata {
            get {
                return ResourceManager.GetString("HotkeyType_Metadata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Monitor test.
        /// </summary>
        internal static string HotkeyType_MonitorTest {
            get {
                return ResourceManager.GetString("HotkeyType_MonitorTest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None.
        /// </summary>
        internal static string HotkeyType_None {
            get {
                return ResourceManager.GetString("HotkeyType_None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OCR.
        /// </summary>
        internal static string HotkeyType_OCR {
            get {
                return ResourceManager.GetString("HotkeyType_OCR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open history window.
        /// </summary>
        internal static string HotkeyType_OpenHistory {
            get {
                return ResourceManager.GetString("HotkeyType_OpenHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open image history window.
        /// </summary>
        internal static string HotkeyType_OpenImageHistory {
            get {
                return ResourceManager.GetString("HotkeyType_OpenImageHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open main window.
        /// </summary>
        internal static string HotkeyType_OpenMainWindow {
            get {
                return ResourceManager.GetString("HotkeyType_OpenMainWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open screenshots folder.
        /// </summary>
        internal static string HotkeyType_OpenScreenshotsFolder {
            get {
                return ResourceManager.GetString("HotkeyType_OpenScreenshotsFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pause screen recording.
        /// </summary>
        internal static string HotkeyType_PauseScreenRecording {
            get {
                return ResourceManager.GetString("HotkeyType_PauseScreenRecording", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pin to screen.
        /// </summary>
        internal static string HotkeyType_PinToScreen {
            get {
                return ResourceManager.GetString("HotkeyType_PinToScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pin to screen (Close all).
        /// </summary>
        internal static string HotkeyType_PinToScreenCloseAll {
            get {
                return ResourceManager.GetString("HotkeyType_PinToScreenCloseAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pin to screen (From clipboard).
        /// </summary>
        internal static string HotkeyType_PinToScreenFromClipboard {
            get {
                return ResourceManager.GetString("HotkeyType_PinToScreenFromClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pin to screen (From file).
        /// </summary>
        internal static string HotkeyType_PinToScreenFromFile {
            get {
                return ResourceManager.GetString("HotkeyType_PinToScreenFromFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pin to screen (From screen).
        /// </summary>
        internal static string HotkeyType_PinToScreenFromScreen {
            get {
                return ResourceManager.GetString("HotkeyType_PinToScreenFromScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture entire screen.
        /// </summary>
        internal static string HotkeyType_PrintScreen {
            get {
                return ResourceManager.GetString("HotkeyType_PrintScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QR code.
        /// </summary>
        internal static string HotkeyType_QRCode {
            get {
                return ResourceManager.GetString("HotkeyType_QRCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QR code (Scan screen).
        /// </summary>
        internal static string HotkeyType_QRCodeDecodeFromScreen {
            get {
                return ResourceManager.GetString("HotkeyType_QRCodeDecodeFromScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QR code (Scan region).
        /// </summary>
        internal static string HotkeyType_QRCodeScanRegion {
            get {
                return ResourceManager.GetString("HotkeyType_QRCodeScanRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture region (Light).
        /// </summary>
        internal static string HotkeyType_RectangleLight {
            get {
                return ResourceManager.GetString("HotkeyType_RectangleLight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture region.
        /// </summary>
        internal static string HotkeyType_RectangleRegion {
            get {
                return ResourceManager.GetString("HotkeyType_RectangleRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture region (Transparent).
        /// </summary>
        internal static string HotkeyType_RectangleTransparent {
            get {
                return ResourceManager.GetString("HotkeyType_RectangleTransparent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ruler.
        /// </summary>
        internal static string HotkeyType_Ruler {
            get {
                return ResourceManager.GetString("HotkeyType_Ruler", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Screen color picker.
        /// </summary>
        internal static string HotkeyType_ScreenColorPicker {
            get {
                return ResourceManager.GetString("HotkeyType_ScreenColorPicker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start/Stop screen recording.
        /// </summary>
        internal static string HotkeyType_ScreenRecorder {
            get {
                return ResourceManager.GetString("HotkeyType_ScreenRecorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start/Stop screen recording using active window region.
        /// </summary>
        internal static string HotkeyType_ScreenRecorderActiveWindow {
            get {
                return ResourceManager.GetString("HotkeyType_ScreenRecorderActiveWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start/Stop screen recording using pre configured region.
        /// </summary>
        internal static string HotkeyType_ScreenRecorderCustomRegion {
            get {
                return ResourceManager.GetString("HotkeyType_ScreenRecorderCustomRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start/Stop screen recording (GIF).
        /// </summary>
        internal static string HotkeyType_ScreenRecorderGIF {
            get {
                return ResourceManager.GetString("HotkeyType_ScreenRecorderGIF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start/Stop screen recording (GIF) using active window region.
        /// </summary>
        internal static string HotkeyType_ScreenRecorderGIFActiveWindow {
            get {
                return ResourceManager.GetString("HotkeyType_ScreenRecorderGIFActiveWindow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start/Stop screen recording (GIF) using pre configured region.
        /// </summary>
        internal static string HotkeyType_ScreenRecorderGIFCustomRegion {
            get {
                return ResourceManager.GetString("HotkeyType_ScreenRecorderGIFCustomRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start/Stop scrolling capture.
        /// </summary>
        internal static string HotkeyType_ScrollingCapture {
            get {
                return ResourceManager.GetString("HotkeyType_ScrollingCapture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shorten URL.
        /// </summary>
        internal static string HotkeyType_ShortenURL {
            get {
                return ResourceManager.GetString("HotkeyType_ShortenURL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start auto capture using last region.
        /// </summary>
        internal static string HotkeyType_StartAutoCapture {
            get {
                return ResourceManager.GetString("HotkeyType_StartAutoCapture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start/Stop screen recording using last region.
        /// </summary>
        internal static string HotkeyType_StartScreenRecorder {
            get {
                return ResourceManager.GetString("HotkeyType_StartScreenRecorder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start/Stop screen recording (GIF) using last region.
        /// </summary>
        internal static string HotkeyType_StartScreenRecorderGIF {
            get {
                return ResourceManager.GetString("HotkeyType_StartScreenRecorderGIF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stop screen recording.
        /// </summary>
        internal static string HotkeyType_StopScreenRecording {
            get {
                return ResourceManager.GetString("HotkeyType_StopScreenRecording", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stop all active uploads.
        /// </summary>
        internal static string HotkeyType_StopUploads {
            get {
                return ResourceManager.GetString("HotkeyType_StopUploads", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strip metadata.
        /// </summary>
        internal static string HotkeyType_StripMetadata {
            get {
                return ResourceManager.GetString("HotkeyType_StripMetadata", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Toggle actions toolbar.
        /// </summary>
        internal static string HotkeyType_ToggleActionsToolbar {
            get {
                return ResourceManager.GetString("HotkeyType_ToggleActionsToolbar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Toggle tray menu.
        /// </summary>
        internal static string HotkeyType_ToggleTrayMenu {
            get {
                return ResourceManager.GetString("HotkeyType_ToggleTrayMenu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload text.
        /// </summary>
        internal static string HotkeyType_UploadText {
            get {
                return ResourceManager.GetString("HotkeyType_UploadText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload from URL.
        /// </summary>
        internal static string HotkeyType_UploadURL {
            get {
                return ResourceManager.GetString("HotkeyType_UploadURL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Video converter.
        /// </summary>
        internal static string HotkeyType_VideoConverter {
            get {
                return ResourceManager.GetString("HotkeyType_VideoConverter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Video thumbnailer.
        /// </summary>
        internal static string HotkeyType_VideoThumbnailer {
            get {
                return ResourceManager.GetString("HotkeyType_VideoThumbnailer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hue: {0:0.0}°, Saturation: {1:0.0}%, Brightness: {2:0.0}%.
        /// </summary>
        internal static string HSB_ToString_ {
            get {
                return ResourceManager.GetString("HSB_ToString_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Color.
        /// </summary>
        internal static string ImageBeautifierBackgroundType_Color {
            get {
                return ResourceManager.GetString("ImageBeautifierBackgroundType_Color", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Desktop.
        /// </summary>
        internal static string ImageBeautifierBackgroundType_Desktop {
            get {
                return ResourceManager.GetString("ImageBeautifierBackgroundType_Desktop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gradient.
        /// </summary>
        internal static string ImageBeautifierBackgroundType_Gradient {
            get {
                return ResourceManager.GetString("ImageBeautifierBackgroundType_Gradient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image.
        /// </summary>
        internal static string ImageBeautifierBackgroundType_Image {
            get {
                return ResourceManager.GetString("ImageBeautifierBackgroundType_Image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transparent.
        /// </summary>
        internal static string ImageBeautifierBackgroundType_Transparent {
            get {
                return ResourceManager.GetString("ImageBeautifierBackgroundType_Transparent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom image uploader.
        /// </summary>
        internal static string ImageDestination_CustomImageUploader {
            get {
                return ResourceManager.GetString("ImageDestination_CustomImageUploader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File uploader.
        /// </summary>
        internal static string ImageDestination_FileUploader {
            get {
                return ResourceManager.GetString("ImageDestination_FileUploader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto size.
        /// </summary>
        internal static string ImageEditorStartMode_AutoSize {
            get {
                return ResourceManager.GetString("ImageEditorStartMode_AutoSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fullscreen.
        /// </summary>
        internal static string ImageEditorStartMode_Fullscreen {
            get {
                return ResourceManager.GetString("ImageEditorStartMode_Fullscreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximized.
        /// </summary>
        internal static string ImageEditorStartMode_Maximized {
            get {
                return ResourceManager.GetString("ImageEditorStartMode_Maximized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Normal.
        /// </summary>
        internal static string ImageEditorStartMode_Normal {
            get {
                return ResourceManager.GetString("ImageEditorStartMode_Normal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Previous state.
        /// </summary>
        internal static string ImageEditorStartMode_PreviousState {
            get {
                return ResourceManager.GetString("ImageEditorStartMode_PreviousState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom.
        /// </summary>
        internal static string ImagePreviewLocation_Bottom {
            get {
                return ResourceManager.GetString("ImagePreviewLocation_Bottom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Side.
        /// </summary>
        internal static string ImagePreviewLocation_Side {
            get {
                return ResourceManager.GetString("ImagePreviewLocation_Side", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatic.
        /// </summary>
        internal static string ImagePreviewVisibility_Automatic {
            get {
                return ResourceManager.GetString("ImagePreviewVisibility_Automatic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hide.
        /// </summary>
        internal static string ImagePreviewVisibility_Hide {
            get {
                return ResourceManager.GetString("ImagePreviewVisibility_Hide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show.
        /// </summary>
        internal static string ImagePreviewVisibility_Show {
            get {
                return ResourceManager.GetString("ImagePreviewVisibility_Show", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Big square.
        /// </summary>
        internal static string ImgurThumbnailType_Big_Square {
            get {
                return ResourceManager.GetString("ImgurThumbnailType_Big_Square", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Huge thumbnail.
        /// </summary>
        internal static string ImgurThumbnailType_Huge_Thumbnail {
            get {
                return ResourceManager.GetString("ImgurThumbnailType_Huge_Thumbnail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Large thumbnail.
        /// </summary>
        internal static string ImgurThumbnailType_Large_Thumbnail {
            get {
                return ResourceManager.GetString("ImgurThumbnailType_Large_Thumbnail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medium thumbnail.
        /// </summary>
        internal static string ImgurThumbnailType_Medium_Thumbnail {
            get {
                return ResourceManager.GetString("ImgurThumbnailType_Medium_Thumbnail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Small square.
        /// </summary>
        internal static string ImgurThumbnailType_Small_Square {
            get {
                return ResourceManager.GetString("ImgurThumbnailType_Small_Square", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Small thumbnail.
        /// </summary>
        internal static string ImgurThumbnailType_Small_Thumbnail {
            get {
                return ResourceManager.GetString("ImgurThumbnailType_Small_Thumbnail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Backward diagonal.
        /// </summary>
        internal static string LinearGradientMode_BackwardDiagonal {
            get {
                return ResourceManager.GetString("LinearGradientMode_BackwardDiagonal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forward diagonal.
        /// </summary>
        internal static string LinearGradientMode_ForwardDiagonal {
            get {
                return ResourceManager.GetString("LinearGradientMode_ForwardDiagonal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Horizontal.
        /// </summary>
        internal static string LinearGradientMode_Horizontal {
            get {
                return ResourceManager.GetString("LinearGradientMode_Horizontal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vertical.
        /// </summary>
        internal static string LinearGradientMode_Vertical {
            get {
                return ResourceManager.GetString("LinearGradientMode_Vertical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap Loading {
            get {
                object obj = ResourceManager.GetObject("Loading", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap LoadingSmallBlack {
            get {
                object obj = ResourceManager.GetObject("LoadingSmallBlack", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap LoadingSmallWhite {
            get {
                object obj = ResourceManager.GetObject("LoadingSmallWhite", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        internal static string MyMessageBox_MyMessageBox_Cancel {
            get {
                return ResourceManager.GetString("MyMessageBox_MyMessageBox_Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string MyMessageBox_MyMessageBox_No {
            get {
                return ResourceManager.GetString("MyMessageBox_MyMessageBox_No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        internal static string MyMessageBox_MyMessageBox_OK {
            get {
                return ResourceManager.GetString("MyMessageBox_MyMessageBox_OK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        internal static string MyMessageBox_MyMessageBox_Yes {
            get {
                return ResourceManager.GetString("MyMessageBox_MyMessageBox_Yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading image....
        /// </summary>
        internal static string MyPictureBox_LoadImageAsync_Loading_image___ {
            get {
                return ResourceManager.GetString("MyPictureBox_LoadImageAsync_Loading_image___", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading image: {0}%.
        /// </summary>
        internal static string MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__ {
            get {
                return ResourceManager.GetString("MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy name.
        /// </summary>
        internal static string ObjectListView_ObjectListView_Copy_name {
            get {
                return ResourceManager.GetString("ObjectListView_ObjectListView_Copy_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy value.
        /// </summary>
        internal static string ObjectListView_ObjectListView_Copy_value {
            get {
                return ResourceManager.GetString("ObjectListView_ObjectListView_Copy_value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string ObjectListView_ObjectListView_Name {
            get {
                return ResourceManager.GetString("ObjectListView_ObjectListView_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value.
        /// </summary>
        internal static string ObjectListView_ObjectListView_Value {
            get {
                return ResourceManager.GetString("ObjectListView_ObjectListView_Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 Day.
        /// </summary>
        internal static string PastebinExpiration_D1 {
            get {
                return ResourceManager.GetString("PastebinExpiration_D1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 Hour.
        /// </summary>
        internal static string PastebinExpiration_H1 {
            get {
                return ResourceManager.GetString("PastebinExpiration_H1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 Month.
        /// </summary>
        internal static string PastebinExpiration_M1 {
            get {
                return ResourceManager.GetString("PastebinExpiration_M1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 10 Minutes.
        /// </summary>
        internal static string PastebinExpiration_M10 {
            get {
                return ResourceManager.GetString("PastebinExpiration_M10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Never.
        /// </summary>
        internal static string PastebinExpiration_N {
            get {
                return ResourceManager.GetString("PastebinExpiration_N", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 Week.
        /// </summary>
        internal static string PastebinExpiration_W1 {
            get {
                return ResourceManager.GetString("PastebinExpiration_W1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2 Weeks.
        /// </summary>
        internal static string PastebinExpiration_W2 {
            get {
                return ResourceManager.GetString("PastebinExpiration_W2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private (members only).
        /// </summary>
        internal static string PastebinPrivacy_Private {
            get {
                return ResourceManager.GetString("PastebinPrivacy_Private", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Public.
        /// </summary>
        internal static string PastebinPrivacy_Public {
            get {
                return ResourceManager.GetString("PastebinPrivacy_Public", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unlisted.
        /// </summary>
        internal static string PastebinPrivacy_Unlisted {
            get {
                return ResourceManager.GetString("PastebinPrivacy_Unlisted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap pipette {
            get {
                object obj = ResourceManager.GetObject("pipette", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatically detect.
        /// </summary>
        internal static string PNGBitDepth_Automatic {
            get {
                return ResourceManager.GetString("PNGBitDepth_Automatic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 24 bit.
        /// </summary>
        internal static string PNGBitDepth_Bit24 {
            get {
                return ResourceManager.GetString("PNGBitDepth_Bit24", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 32 bit.
        /// </summary>
        internal static string PNGBitDepth_Bit32 {
            get {
                return ResourceManager.GetString("PNGBitDepth_Bit32", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default.
        /// </summary>
        internal static string PNGBitDepth_Default {
            get {
                return ResourceManager.GetString("PNGBitDepth_Default", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print.
        /// </summary>
        internal static string PrintForm_LoadSettings_Print {
            get {
                return ResourceManager.GetString("PrintForm_LoadSettings_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name: {0}, Size: {1}.
        /// </summary>
        internal static string PrintTextForm_LoadSettings_Name___0___Size___1_ {
            get {
                return ResourceManager.GetString("PrintTextForm_LoadSettings_Name___0___Size___1_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatic.
        /// </summary>
        internal static string ProxyMethod_Automatic {
            get {
                return ResourceManager.GetString("ProxyMethod_Automatic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manual.
        /// </summary>
        internal static string ProxyMethod_Manual {
            get {
                return ResourceManager.GetString("ProxyMethod_Manual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None.
        /// </summary>
        internal static string ProxyMethod_None {
            get {
                return ResourceManager.GetString("ProxyMethod_None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random emoji (Repeat using {n}).
        /// </summary>
        internal static string RandomEmojiRepeatUsingN {
            get {
                return ResourceManager.GetString("RandomEmojiRepeatUsingN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random non ambiguous alphanumeric char (Repeat using {n}).
        /// </summary>
        internal static string RandomNonAmbiguousAlphanumericCharRepeatUsingN {
            get {
                return ResourceManager.GetString("RandomNonAmbiguousAlphanumericCharRepeatUsingN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel capture.
        /// </summary>
        internal static string RegionCaptureAction_CancelCapture {
            get {
                return ResourceManager.GetString("RegionCaptureAction_CancelCapture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture active monitor.
        /// </summary>
        internal static string RegionCaptureAction_CaptureActiveMonitor {
            get {
                return ResourceManager.GetString("RegionCaptureAction_CaptureActiveMonitor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture fullscreen.
        /// </summary>
        internal static string RegionCaptureAction_CaptureFullscreen {
            get {
                return ResourceManager.GetString("RegionCaptureAction_CaptureFullscreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Capture last region.
        /// </summary>
        internal static string RegionCaptureAction_CaptureLastRegion {
            get {
                return ResourceManager.GetString("RegionCaptureAction_CaptureLastRegion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do nothing.
        /// </summary>
        internal static string RegionCaptureAction_None {
            get {
                return ResourceManager.GetString("RegionCaptureAction_None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove shape.
        /// </summary>
        internal static string RegionCaptureAction_RemoveShape {
            get {
                return ResourceManager.GetString("RegionCaptureAction_RemoveShape", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove shape or cancel capture.
        /// </summary>
        internal static string RegionCaptureAction_RemoveShapeCancelCapture {
            get {
                return ResourceManager.GetString("RegionCaptureAction_RemoveShapeCancelCapture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Swap tool type.
        /// </summary>
        internal static string RegionCaptureAction_SwapToolType {
            get {
                return ResourceManager.GetString("RegionCaptureAction_SwapToolType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Computer.
        /// </summary>
        internal static string ReplCodeMenuCategory_Computer {
            get {
                return ResourceManager.GetString("ReplCodeMenuCategory_Computer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date and time.
        /// </summary>
        internal static string ReplCodeMenuCategory_Date_and_Time {
            get {
                return ResourceManager.GetString("ReplCodeMenuCategory_Date_and_Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image.
        /// </summary>
        internal static string ReplCodeMenuCategory_Image {
            get {
                return ResourceManager.GetString("ReplCodeMenuCategory_Image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incremental.
        /// </summary>
        internal static string ReplCodeMenuCategory_Incremental {
            get {
                return ResourceManager.GetString("ReplCodeMenuCategory_Incremental", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random.
        /// </summary>
        internal static string ReplCodeMenuCategory_Random {
            get {
                return ResourceManager.GetString("ReplCodeMenuCategory_Random", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Window.
        /// </summary>
        internal static string ReplCodeMenuCategory_Window {
            get {
                return ResourceManager.GetString("ReplCodeMenuCategory_Window", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Computer name.
        /// </summary>
        internal static string ReplCodeMenuEntry_cn_Computer_name {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_cn_Computer_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day.
        /// </summary>
        internal static string ReplCodeMenuEntry_d_Current_day {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_d_Current_day", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random GUID.
        /// </summary>
        internal static string ReplCodeMenuEntry_guid_Random_guid {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_guid_Random_guid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hour.
        /// </summary>
        internal static string ReplCodeMenuEntry_h_Current_hour {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_h_Current_hour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image height.
        /// </summary>
        internal static string ReplCodeMenuEntry_height_Gets_image_height {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_height_Gets_image_height", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto increment number (0 pad left using {n}).
        /// </summary>
        internal static string ReplCodeMenuEntry_i_Auto_increment_number {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_i_Auto_increment_number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto increment alphanumeric case-insensitive (0 pad left using {n}).
        /// </summary>
        internal static string ReplCodeMenuEntry_ia_Auto_increment_alphanumeric {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_ia_Auto_increment_alphanumeric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto increment alphanumeric case-sensitive (0 pad left using {n}).
        /// </summary>
        internal static string ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto increment by base {n} using alphanumeric (1 &lt; n &lt; 63).
        /// </summary>
        internal static string ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto increment hexadecimal (0 pad left using {n}).
        /// </summary>
        internal static string ReplCodeMenuEntry_ix_Auto_increment_hexadecimal {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_ix_Auto_increment_hexadecimal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minute.
        /// </summary>
        internal static string ReplCodeMenuEntry_mi_Current_minute {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_mi_Current_minute", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month.
        /// </summary>
        internal static string ReplCodeMenuEntry_mo_Current_month {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_mo_Current_month", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month name (Local language).
        /// </summary>
        internal static string ReplCodeMenuEntry_mon_Current_month_name__Local_language_ {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_mon_Current_month_name__Local_language_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month name (English).
        /// </summary>
        internal static string ReplCodeMenuEntry_mon2_Current_month_name__English_ {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_mon2_Current_month_name__English_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Millisecond.
        /// </summary>
        internal static string ReplCodeMenuEntry_ms_Current_millisecond {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_ms_Current_millisecond", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New line.
        /// </summary>
        internal static string ReplCodeMenuEntry_n_New_line {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_n_New_line", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AM/PM.
        /// </summary>
        internal static string ReplCodeMenuEntry_pm_Gets_AM_PM {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_pm_Gets_AM_PM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Process name of window.
        /// </summary>
        internal static string ReplCodeMenuEntry_pn_Process_name_of_active_window {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_pn_Process_name_of_active_window", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random alphanumeric char (Repeat using {n}).
        /// </summary>
        internal static string ReplCodeMenuEntry_ra_Random_alphanumeric_char {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_ra_Random_alphanumeric_char", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random line from a file (Use {filepath} to determine the file).
        /// </summary>
        internal static string ReplCodeMenuEntry_rf_Random_line_from_file {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_rf_Random_line_from_file", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random number 0 to 9 (Repeat using {n}).
        /// </summary>
        internal static string ReplCodeMenuEntry_rn_Random_number_0_to_9 {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_rn_Random_number_0_to_9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random hexadecimal char (Repeat using {n}).
        /// </summary>
        internal static string ReplCodeMenuEntry_rx_Random_hexadecimal {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_rx_Random_hexadecimal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Second.
        /// </summary>
        internal static string ReplCodeMenuEntry_s_Current_second {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_s_Current_second", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title of window.
        /// </summary>
        internal static string ReplCodeMenuEntry_t_Title_of_active_window {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_t_Title_of_active_window", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User login name.
        /// </summary>
        internal static string ReplCodeMenuEntry_uln_User_login_name {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_uln_User_login_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User name.
        /// </summary>
        internal static string ReplCodeMenuEntry_un_User_name {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_un_User_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unix timestamp.
        /// </summary>
        internal static string ReplCodeMenuEntry_unix_Unix_timestamp {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_unix_Unix_timestamp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Week name (Local language).
        /// </summary>
        internal static string ReplCodeMenuEntry_w_Current_week_name__Local_language_ {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_w_Current_week_name__Local_language_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Week name (English).
        /// </summary>
        internal static string ReplCodeMenuEntry_w2_Current_week_name__English_ {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_w2_Current_week_name__English_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image width.
        /// </summary>
        internal static string ReplCodeMenuEntry_width_Gets_image_width {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_width_Gets_image_width", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Week of year.
        /// </summary>
        internal static string ReplCodeMenuEntry_wy_Week_of_year {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_wy_Week_of_year", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Year.
        /// </summary>
        internal static string ReplCodeMenuEntry_y_Current_year {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_y_Current_year", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Year (2 digits).
        /// </summary>
        internal static string ReplCodeMenuEntry_yy_Current_year__2_digits_ {
            get {
                return ResourceManager.GetString("ReplCodeMenuEntry_yy_Current_year__2_digits_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Result:.
        /// </summary>
        internal static string Result {
            get {
                return ResourceManager.GetString("Result", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Result of first file:.
        /// </summary>
        internal static string ResultOfFirstFile {
            get {
                return ResourceManager.GetString("ResultOfFirstFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Result of second file:.
        /// </summary>
        internal static string ResultOfSecondFile {
            get {
                return ResourceManager.GetString("ResultOfSecondFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FFmpeg (Good quality).
        /// </summary>
        internal static string ScreenRecordGIFEncoding_FFmpeg {
            get {
                return ResourceManager.GetString("ScreenRecordGIFEncoding_FFmpeg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .NET (Bad quality).
        /// </summary>
        internal static string ScreenRecordGIFEncoding_NET {
            get {
                return ResourceManager.GetString("ScreenRecordGIFEncoding_NET", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Octree quantizer (Medium quality).
        /// </summary>
        internal static string ScreenRecordGIFEncoding_OctreeQuantizer {
            get {
                return ResourceManager.GetString("ScreenRecordGIFEncoding_OctreeQuantizer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Down arrow.
        /// </summary>
        internal static string ScrollMethod_DownArrow {
            get {
                return ResourceManager.GetString("ScrollMethod_DownArrow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mouse wheel.
        /// </summary>
        internal static string ScrollMethod_MouseWheel {
            get {
                return ResourceManager.GetString("ScrollMethod_MouseWheel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Page down.
        /// </summary>
        internal static string ScrollMethod_PageDown {
            get {
                return ResourceManager.GetString("ScrollMethod_PageDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scroll message.
        /// </summary>
        internal static string ScrollMethod_ScrollMessage {
            get {
                return ResourceManager.GetString("ScrollMethod_ScrollMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Arrow (A).
        /// </summary>
        internal static string ShapeType_DrawingArrow {
            get {
                return ResourceManager.GetString("ShapeType_DrawingArrow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cursor.
        /// </summary>
        internal static string ShapeType_DrawingCursor {
            get {
                return ResourceManager.GetString("ShapeType_DrawingCursor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ellipse (E).
        /// </summary>
        internal static string ShapeType_DrawingEllipse {
            get {
                return ResourceManager.GetString("ShapeType_DrawingEllipse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freehand (F).
        /// </summary>
        internal static string ShapeType_DrawingFreehand {
            get {
                return ResourceManager.GetString("ShapeType_DrawingFreehand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freehand arrow.
        /// </summary>
        internal static string ShapeType_DrawingFreehandArrow {
            get {
                return ResourceManager.GetString("ShapeType_DrawingFreehandArrow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image (File).
        /// </summary>
        internal static string ShapeType_DrawingImage {
            get {
                return ResourceManager.GetString("ShapeType_DrawingImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image (Screen).
        /// </summary>
        internal static string ShapeType_DrawingImageScreen {
            get {
                return ResourceManager.GetString("ShapeType_DrawingImageScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line (L).
        /// </summary>
        internal static string ShapeType_DrawingLine {
            get {
                return ResourceManager.GetString("ShapeType_DrawingLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Magnify.
        /// </summary>
        internal static string ShapeType_DrawingMagnify {
            get {
                return ResourceManager.GetString("ShapeType_DrawingMagnify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rectangle (R).
        /// </summary>
        internal static string ShapeType_DrawingRectangle {
            get {
                return ResourceManager.GetString("ShapeType_DrawingRectangle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Smart eraser.
        /// </summary>
        internal static string ShapeType_DrawingSmartEraser {
            get {
                return ResourceManager.GetString("ShapeType_DrawingSmartEraser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Speech balloon (S).
        /// </summary>
        internal static string ShapeType_DrawingSpeechBalloon {
            get {
                return ResourceManager.GetString("ShapeType_DrawingSpeechBalloon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Step (I).
        /// </summary>
        internal static string ShapeType_DrawingStep {
            get {
                return ResourceManager.GetString("ShapeType_DrawingStep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sticker.
        /// </summary>
        internal static string ShapeType_DrawingSticker {
            get {
                return ResourceManager.GetString("ShapeType_DrawingSticker", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text (Background) (T).
        /// </summary>
        internal static string ShapeType_DrawingTextBackground {
            get {
                return ResourceManager.GetString("ShapeType_DrawingTextBackground", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text (Outline) (O).
        /// </summary>
        internal static string ShapeType_DrawingTextOutline {
            get {
                return ResourceManager.GetString("ShapeType_DrawingTextOutline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Blur (B).
        /// </summary>
        internal static string ShapeType_EffectBlur {
            get {
                return ResourceManager.GetString("ShapeType_EffectBlur", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Highlight (H).
        /// </summary>
        internal static string ShapeType_EffectHighlight {
            get {
                return ResourceManager.GetString("ShapeType_EffectHighlight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pixelate (P).
        /// </summary>
        internal static string ShapeType_EffectPixelate {
            get {
                return ResourceManager.GetString("ShapeType_EffectPixelate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ellipse region.
        /// </summary>
        internal static string ShapeType_RegionEllipse {
            get {
                return ResourceManager.GetString("ShapeType_RegionEllipse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freehand region.
        /// </summary>
        internal static string ShapeType_RegionFreehand {
            get {
                return ResourceManager.GetString("ShapeType_RegionFreehand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rectangle region.
        /// </summary>
        internal static string ShapeType_RegionRectangle {
            get {
                return ResourceManager.GetString("ShapeType_RegionRectangle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Crop image (C).
        /// </summary>
        internal static string ShapeType_ToolCrop {
            get {
                return ResourceManager.GetString("ShapeType_ToolCrop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cut out (X).
        /// </summary>
        internal static string ShapeType_ToolCutOut {
            get {
                return ResourceManager.GetString("ShapeType_ToolCutOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select and move (M).
        /// </summary>
        internal static string ShapeType_ToolSelect {
            get {
                return ResourceManager.GetString("ShapeType_ToolSelect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Icon similar to (Icon).
        /// </summary>
        internal static System.Drawing.Icon ShareX_Icon {
            get {
                object obj = ResourceManager.GetObject("ShareX_Icon", resourceCulture);
                return ((System.Drawing.Icon)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Icon similar to (Icon).
        /// </summary>
        internal static System.Drawing.Icon ShareX_Icon_White {
            get {
                object obj = ResourceManager.GetObject("ShareX_Icon_White", resourceCulture);
                return ((System.Drawing.Icon)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap ShareX_Logo {
            get {
                object obj = ResourceManager.GetObject("ShareX_Logo", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ShareX - Image viewer.
        /// </summary>
        internal static string ShareXImageViewer {
            get {
                return ResourceManager.GetString("ShareXImageViewer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Letters (Lowercase).
        /// </summary>
        internal static string StepType_LettersLowercase {
            get {
                return ResourceManager.GetString("StepType_LettersLowercase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Letters (Uppercase).
        /// </summary>
        internal static string StepType_LettersUppercase {
            get {
                return ResourceManager.GetString("StepType_LettersUppercase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Numbers.
        /// </summary>
        internal static string StepType_Numbers {
            get {
                return ResourceManager.GetString("StepType_Numbers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roman numerals (Lowercase).
        /// </summary>
        internal static string StepType_RomanNumeralsLowercase {
            get {
                return ResourceManager.GetString("StepType_RomanNumeralsLowercase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Roman numerals (Uppercase).
        /// </summary>
        internal static string StepType_RomanNumeralsUppercase {
            get {
                return ResourceManager.GetString("StepType_RomanNumeralsUppercase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stop.
        /// </summary>
        internal static string Stop {
            get {
                return ResourceManager.GetString("Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatic.
        /// </summary>
        internal static string SupportedLanguage_Automatic {
            get {
                return ResourceManager.GetString("SupportedLanguage_Automatic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target:.
        /// </summary>
        internal static string Target {
            get {
                return ResourceManager.GetString("Target", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List view.
        /// </summary>
        internal static string TaskViewMode_ListView {
            get {
                return ResourceManager.GetString("TaskViewMode_ListView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thumbnail view.
        /// </summary>
        internal static string TaskViewMode_ThumbnailView {
            get {
                return ResourceManager.GetString("TaskViewMode_ThumbnailView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom text uploader.
        /// </summary>
        internal static string TextDestination_CustomTextUploader {
            get {
                return ResourceManager.GetString("TextDestination_CustomTextUploader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File uploader.
        /// </summary>
        internal static string TextDestination_FileUploader {
            get {
                return ResourceManager.GetString("TextDestination_FileUploader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bottom.
        /// </summary>
        internal static string ThumbnailTitleLocation_Bottom {
            get {
                return ResourceManager.GetString("ThumbnailTitleLocation_Bottom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top.
        /// </summary>
        internal static string ThumbnailTitleLocation_Top {
            get {
                return ResourceManager.GetString("ThumbnailTitleLocation_Top", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default.
        /// </summary>
        internal static string ThumbnailViewClickAction_Default {
            get {
                return ResourceManager.GetString("ThumbnailViewClickAction_Default", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit image.
        /// </summary>
        internal static string ThumbnailViewClickAction_EditImage {
            get {
                return ResourceManager.GetString("ThumbnailViewClickAction_EditImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open file.
        /// </summary>
        internal static string ThumbnailViewClickAction_OpenFile {
            get {
                return ResourceManager.GetString("ThumbnailViewClickAction_OpenFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open folder.
        /// </summary>
        internal static string ThumbnailViewClickAction_OpenFolder {
            get {
                return ResourceManager.GetString("ThumbnailViewClickAction_OpenFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open image viewer.
        /// </summary>
        internal static string ThumbnailViewClickAction_OpenImageViewer {
            get {
                return ResourceManager.GetString("ThumbnailViewClickAction_OpenImageViewer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open URL.
        /// </summary>
        internal static string ThumbnailViewClickAction_OpenURL {
            get {
                return ResourceManager.GetString("ThumbnailViewClickAction_OpenURL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select.
        /// </summary>
        internal static string ThumbnailViewClickAction_Select {
            get {
                return ResourceManager.GetString("ThumbnailViewClickAction_Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap tick {
            get {
                object obj = ResourceManager.GetObject("tick", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit image.
        /// </summary>
        internal static string ToastClickAction_AnnotateImage {
            get {
                return ResourceManager.GetString("ToastClickAction_AnnotateImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close notification.
        /// </summary>
        internal static string ToastClickAction_CloseNotification {
            get {
                return ResourceManager.GetString("ToastClickAction_CloseNotification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy file.
        /// </summary>
        internal static string ToastClickAction_CopyFile {
            get {
                return ResourceManager.GetString("ToastClickAction_CopyFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy file path.
        /// </summary>
        internal static string ToastClickAction_CopyFilePath {
            get {
                return ResourceManager.GetString("ToastClickAction_CopyFilePath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy image.
        /// </summary>
        internal static string ToastClickAction_CopyImageToClipboard {
            get {
                return ResourceManager.GetString("ToastClickAction_CopyImageToClipboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy link.
        /// </summary>
        internal static string ToastClickAction_CopyUrl {
            get {
                return ResourceManager.GetString("ToastClickAction_CopyUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete file locally.
        /// </summary>
        internal static string ToastClickAction_DeleteFile {
            get {
                return ResourceManager.GetString("ToastClickAction_DeleteFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open file.
        /// </summary>
        internal static string ToastClickAction_OpenFile {
            get {
                return ResourceManager.GetString("ToastClickAction_OpenFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open folder.
        /// </summary>
        internal static string ToastClickAction_OpenFolder {
            get {
                return ResourceManager.GetString("ToastClickAction_OpenFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open link.
        /// </summary>
        internal static string ToastClickAction_OpenUrl {
            get {
                return ResourceManager.GetString("ToastClickAction_OpenUrl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pin to screen.
        /// </summary>
        internal static string ToastClickAction_PinToScreen {
            get {
                return ResourceManager.GetString("ToastClickAction_PinToScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload file.
        /// </summary>
        internal static string ToastClickAction_Upload {
            get {
                return ResourceManager.GetString("ToastClickAction_Upload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dev.
        /// </summary>
        internal static string UpdateChannel_Dev {
            get {
                return ResourceManager.GetString("UpdateChannel_Dev", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pre-release.
        /// </summary>
        internal static string UpdateChannel_PreRelease {
            get {
                return ResourceManager.GetString("UpdateChannel_PreRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release.
        /// </summary>
        internal static string UpdateChannel_Release {
            get {
                return ResourceManager.GetString("UpdateChannel_Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A newer version of {0} is available.
        /// </summary>
        internal static string UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available {
            get {
                return ResourceManager.GetString("UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} is up to date.
        /// </summary>
        internal static string UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date {
            get {
                return ResourceManager.GetString("UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update check failed.
        /// </summary>
        internal static string UpdateCheckerLabel_UpdateControls_Update_check_failed {
            get {
                return ResourceManager.GetString("UpdateCheckerLabel_UpdateControls_Update_check_failed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A newer version of {0} is available.
        ///Would you like to download and install it?.
        /// </summary>
        internal static string UpdateMessageBox_UpdateMessageBox_ {
            get {
                return ResourceManager.GetString("UpdateMessageBox_UpdateMessageBox_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current version.
        /// </summary>
        internal static string UpdateMessageBox_UpdateMessageBox_CurrentVersion {
            get {
                return ResourceManager.GetString("UpdateMessageBox_UpdateMessageBox_CurrentVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Latest version.
        /// </summary>
        internal static string UpdateMessageBox_UpdateMessageBox_LatestVersion {
            get {
                return ResourceManager.GetString("UpdateMessageBox_UpdateMessageBox_LatestVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A newer version of {0} is available.
        ///Would you like to download it?.
        /// </summary>
        internal static string UpdateMessageBox_UpdateMessageBox_Portable {
            get {
                return ResourceManager.GetString("UpdateMessageBox_UpdateMessageBox_Portable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update is available.
        /// </summary>
        internal static string UpdateMessageBox_UpdateMessageBox_update_is_available {
            get {
                return ResourceManager.GetString("UpdateMessageBox_UpdateMessageBox_update_is_available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bing visual search.
        /// </summary>
        internal static string URLSharingServices_BingVisualSearch {
            get {
                return ResourceManager.GetString("URLSharingServices_BingVisualSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom URL sharing service.
        /// </summary>
        internal static string URLSharingServices_CustomURLSharingService {
            get {
                return ResourceManager.GetString("URLSharingServices_CustomURLSharingService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string URLSharingServices_Email {
            get {
                return ResourceManager.GetString("URLSharingServices_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Google Lens.
        /// </summary>
        internal static string URLSharingServices_GoogleImageSearch {
            get {
                return ResourceManager.GetString("URLSharingServices_GoogleImageSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom URL shortener.
        /// </summary>
        internal static string UrlShortenerType_CustomURLShortener {
            get {
                return ResourceManager.GetString("UrlShortenerType_CustomURLShortener", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse for a sound file....
        /// </summary>
        internal static string WavFileNameEditor_EditValue_Browse_for_a_sound_file___ {
            get {
                return ResourceManager.GetString("WavFileNameEditor_EditValue_Browse_for_a_sound_file___", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Private.
        /// </summary>
        internal static string YouTubeVideoPrivacy_Private {
            get {
                return ResourceManager.GetString("YouTubeVideoPrivacy_Private", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Public.
        /// </summary>
        internal static string YouTubeVideoPrivacy_Public {
            get {
                return ResourceManager.GetString("YouTubeVideoPrivacy_Public", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unlisted.
        /// </summary>
        internal static string YouTubeVideoPrivacy_Unlisted {
            get {
                return ResourceManager.GetString("YouTubeVideoPrivacy_Unlisted", resourceCulture);
            }
        }
    }
}

<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ShapeType_RegionFreehand" xml:space="preserve">
    <value>Region odręczny</value>
  </data>
  <data name="ReplCodeMenuEntry_w_Current_week_name__Local_language_" xml:space="preserve">
    <value>Nazwa tygodnia (język lokalny)</value>
  </data>
  <data name="ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_" xml:space="preserve">
    <value>Ustawienia skopiowane do schowka.</value>
  </data>
  <data name="ImgurThumbnailType_Big_Square" xml:space="preserve">
    <value>Duży kwadrat</value>
  </data>
  <data name="ReplCodeMenuEntry_s_Current_second" xml:space="preserve">
    <value>Sekunda</value>
  </data>
  <data name="TextDestination_CustomTextUploader" xml:space="preserve">
    <value>Własny serwis tekstu</value>
  </data>
  <data name="ProxyMethod_None" xml:space="preserve">
    <value>Brak</value>
  </data>
  <data name="ReplCodeMenuEntry_mo_Current_month" xml:space="preserve">
    <value>Miesiąc</value>
  </data>
  <data name="CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___" xml:space="preserve">
    <value>Przeglądaj w poszukiwaniu kaskadowego arkusza stylów...</value>
  </data>
  <data name="Extensions_AddContextMenu_Redo" xml:space="preserve">
    <value>Ponów</value>
  </data>
  <data name="HotkeyType_VideoThumbnailer" xml:space="preserve">
    <value>Generator miniatur wideo</value>
  </data>
  <data name="ShapeType_EffectBlur" xml:space="preserve">
    <value>Rozmycie (B)</value>
  </data>
  <data name="AfterCaptureTasks_ShowQuickTaskMenu" xml:space="preserve">
    <value>Pokaż menu szybkich zadań</value>
  </data>
  <data name="CustomUploaderDestinationType_URLShortener" xml:space="preserve">
    <value>Skracacz linku</value>
  </data>
  <data name="ReplCodeMenuEntry_uln_User_login_name" xml:space="preserve">
    <value>Login użytkownika</value>
  </data>
  <data name="HotkeyType_ImageEffects" xml:space="preserve">
    <value>Efekty obrazu</value>
  </data>
  <data name="ShapeType_DrawingImageScreen" xml:space="preserve">
    <value>Obraz (ekran)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available" xml:space="preserve">
    <value>Dostępna jest nowsza wersja {0}</value>
  </data>
  <data name="AfterUploadTasks_ShowQRCode" xml:space="preserve">
    <value>Pokaż okno kodu QR</value>
  </data>
  <data name="ShapeType_DrawingSpeechBalloon" xml:space="preserve">
    <value>Dymek (S)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date" xml:space="preserve">
    <value>{0} jest aktualny</value>
  </data>
  <data name="HotkeyType_Category_ScreenRecord" xml:space="preserve">
    <value>Nagrywanie ekranu</value>
  </data>
  <data name="PastebinExpiration_H1" xml:space="preserve">
    <value>1 godzina</value>
  </data>
  <data name="HotkeyType_ScrollingCapture" xml:space="preserve">
    <value>Przechwytywanie przy przewijaniu</value>
  </data>
  <data name="ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all" xml:space="preserve">
    <value>Automatycznie rosnąca liczba alfanumeryczna z uwzględnieniem wielkości liter (liczba 0 na początku: {n})</value>
  </data>
  <data name="ReplCodeMenuEntry_t_Title_of_active_window" xml:space="preserve">
    <value>Tytuł okna</value>
  </data>
  <data name="AfterCaptureTasks_SendImageToPrinter" xml:space="preserve">
    <value>Drukuj obraz</value>
  </data>
  <data name="ShapeType_RegionRectangle" xml:space="preserve">
    <value>Region prostokątny</value>
  </data>
  <data name="HotkeyType_ToggleActionsToolbar" xml:space="preserve">
    <value>Przełącz pasek akcji</value>
  </data>
  <data name="AfterCaptureTasks_PerformActions" xml:space="preserve">
    <value>Wykonaj działania</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfCanvas" xml:space="preserve">
    <value>Procent płótna</value>
  </data>
  <data name="ReplCodeMenuCategory_Date_and_Time" xml:space="preserve">
    <value>Data i czas</value>
  </data>
  <data name="HotkeyType_ImageCombiner" xml:space="preserve">
    <value>Łącznik obrazów</value>
  </data>
  <data name="HotkeyType_RectangleTransparent" xml:space="preserve">
    <value>Przechwytywanie regionu (przezroczysty)</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_" xml:space="preserve">
    <value>Pobranie zakończone.</value>
  </data>
  <data name="YouTubeVideoPrivacy_Private" xml:space="preserve">
    <value>Prywatny</value>
  </data>
  <data name="AfterUploadTasks_ShareURL" xml:space="preserve">
    <value>Udostępnij link</value>
  </data>
  <data name="CustomUploaderDestinationType_FileUploader" xml:space="preserve">
    <value>Serwis plików</value>
  </data>
  <data name="ReplCodeMenuEntry_h_Current_hour" xml:space="preserve">
    <value>Godzina</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_Update_check_failed" xml:space="preserve">
    <value>Sprawdzanie aktualizacji nie powiodło się</value>
  </data>
  <data name="ReplCodeMenuEntry_ms_Current_millisecond" xml:space="preserve">
    <value>Milisekunda</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Install" xml:space="preserve">
    <value>Instaluj</value>
  </data>
  <data name="AfterCaptureTasks_UploadImageToHost" xml:space="preserve">
    <value>Prześlij obraz do hosta</value>
  </data>
  <data name="ReplCodeMenuEntry_ix_Auto_increment_hexadecimal" xml:space="preserve">
    <value>Automatycznie rosnąca liczba szesnastkowa (liczba 0 na początku: {n})</value>
  </data>
  <data name="CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__" xml:space="preserve">
    <value>Cyjan: {0:0.0}%, Magenta: {1:0.0}%, Żółty: {2:0.0}%, Klucz: {3:0.0}%</value>
  </data>
  <data name="HotkeyType_FolderUpload" xml:space="preserve">
    <value>Prześlij folder</value>
  </data>
  <data name="ReplCodeMenuEntry_mi_Current_minute" xml:space="preserve">
    <value>Minuta</value>
  </data>
  <data name="ShapeType_EffectPixelate" xml:space="preserve">
    <value>Pikselacja (P)</value>
  </data>
  <data name="ReplCodeMenuEntry_d_Current_day" xml:space="preserve">
    <value>Dzień</value>
  </data>
  <data name="PastebinExpiration_D1" xml:space="preserve">
    <value>1 dzień</value>
  </data>
  <data name="ShapeType_DrawingArrow" xml:space="preserve">
    <value>Strzałka (A)</value>
  </data>
  <data name="ShapeType_DrawingSmartEraser" xml:space="preserve">
    <value>Inteligentna gumka</value>
  </data>
  <data name="PastebinPrivacy_Unlisted" xml:space="preserve">
    <value>Niepubliczny</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_update_is_available" xml:space="preserve">
    <value>Dostępna jest aktualizacja</value>
  </data>
  <data name="HotkeyType_Category_Upload" xml:space="preserve">
    <value>Przeslij</value>
  </data>
  <data name="Extensions_AddContextMenu_Cut" xml:space="preserve">
    <value>Wytnij</value>
  </data>
  <data name="FileExistAction_Cancel" xml:space="preserve">
    <value>Nie zapisuj</value>
  </data>
  <data name="AfterCaptureTasks_CopyImageToClipboard" xml:space="preserve">
    <value>Skopiuj obraz do schowka</value>
  </data>
  <data name="PNGBitDepth_Bit32" xml:space="preserve">
    <value>32 bity</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFActiveWindow" xml:space="preserve">
    <value>Rozpocznij/Zatrzymaj nagrywanie ekranu (GIF) przy użyciu aktywnego obszaru okna</value>
  </data>
  <data name="HotkeyType_PrintScreen" xml:space="preserve">
    <value>Przechwytywanie całego ekranu</value>
  </data>
  <data name="ImageEditorStartMode_Normal" xml:space="preserve">
    <value>Normalny</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFCustomRegion" xml:space="preserve">
    <value>Rozpocznij/Zatrzymaj nagrywanie ekranu (GIF) przy użyciu wstępnie skonfigurowanego regionu</value>
  </data>
  <data name="HotkeyType_CustomRegion" xml:space="preserve">
    <value>Przechwytywanie wstępnie skonfigurowanego regionu</value>
  </data>
  <data name="ReplCodeMenuCategory_Image" xml:space="preserve">
    <value>Obraz</value>
  </data>
  <data name="PastebinExpiration_M10" xml:space="preserve">
    <value>10 minut</value>
  </data>
  <data name="RegionCaptureAction_SwapToolType" xml:space="preserve">
    <value>Zmiana typu narzędzia</value>
  </data>
  <data name="HotkeyType_RectangleRegion" xml:space="preserve">
    <value>Przechwytywanie regionu</value>
  </data>
  <data name="AfterCaptureTasks_DoOCR" xml:space="preserve">
    <value>Rozpoznaj tekst (OCR)</value>
  </data>
  <data name="HotkeyType_ExitShareX" xml:space="preserve">
    <value>Zamknij ShareX</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_Portable" xml:space="preserve">
    <value>Dostępna jest nowsza wersja {0}.
Czy chcesz ją pobrać?</value>
  </data>
  <data name="Helpers_DownloadString_Download_failed_" xml:space="preserve">
    <value>Pobieranie nie powiodło się:</value>
  </data>
  <data name="ShapeType_DrawingTextOutline" xml:space="preserve">
    <value>Tekst (kontur) (O)</value>
  </data>
  <data name="RegionCaptureAction_CaptureActiveMonitor" xml:space="preserve">
    <value>Przechwytywanie aktywnego monitora</value>
  </data>
  <data name="ImgurThumbnailType_Small_Thumbnail" xml:space="preserve">
    <value>Mała miniatura</value>
  </data>
  <data name="PrintForm_LoadSettings_Print" xml:space="preserve">
    <value>Drukuj</value>
  </data>
  <data name="GIFQuality_Bit4" xml:space="preserve">
    <value>Octree quantizer 16 kolorów</value>
  </data>
  <data name="AfterUploadTasks_ShowAfterUploadWindow" xml:space="preserve">
    <value>Pokaż okno "Po przesłaniu"</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAdjective" xml:space="preserve">
    <value>Losowy przymiotnik</value>
  </data>
  <data name="Extensions_AddContextMenu_SelectAll" xml:space="preserve">
    <value>Wybierz wszystko</value>
  </data>
  <data name="FileDestination_CustomFileUploader" xml:space="preserve">
    <value>Własny serwis plików</value>
  </data>
  <data name="LinearGradientMode_Vertical" xml:space="preserve">
    <value>Pionowo</value>
  </data>
  <data name="ReplCodeMenuCategory_Random" xml:space="preserve">
    <value>Losowo</value>
  </data>
  <data name="CustomUploaderDestinationType_ImageUploader" xml:space="preserve">
    <value>Serwis obrazów</value>
  </data>
  <data name="HotkeyType_HashCheck" xml:space="preserve">
    <value>Sprawdź hash</value>
  </data>
  <data name="HotkeyType_ScreenRecorderActiveWindow" xml:space="preserve">
    <value>Rozpocznij/Zatrzymaj nagrywanie ekranu przy użyciu aktywnego obszaru okna</value>
  </data>
  <data name="ReplCodeMenuEntry_rn_Random_number_0_to_9" xml:space="preserve">
    <value>Liczba losowa od 0 do 9 (powtarzaj za pomocą {n})</value>
  </data>
  <data name="HotkeyType_ClipboardUploadWithContentViewer" xml:space="preserve">
    <value>Prześlij ze schowka z przeglądarką treści</value>
  </data>
  <data name="YouTubeVideoPrivacy_Public" xml:space="preserve">
    <value>Publiczny</value>
  </data>
  <data name="HSB_ToString_" xml:space="preserve">
    <value>Barwa: {0:0.0}°, Nasycenie: {1:0.0}%, Jasność: {2:0.0}%</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="HotkeyType_DragDropUpload" xml:space="preserve">
    <value>Przeciągnij i upuść przesyłanie</value>
  </data>
  <data name="PastebinExpiration_N" xml:space="preserve">
    <value>Nigdy</value>
  </data>
  <data name="HotkeyType_StartScreenRecorder" xml:space="preserve">
    <value>Rozpocznij/Zatrzymaj nagrywanie ekranu przy użyciu ostatniego regionu</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Yes" xml:space="preserve">
    <value>Tak</value>
  </data>
  <data name="HotkeyType_ImageThumbnailer" xml:space="preserve">
    <value>Generator miniatur obrazów</value>
  </data>
  <data name="ReplCodeMenuEntry_mon_Current_month_name__Local_language_" xml:space="preserve">
    <value>Nazwa miesiąca (język lokalny)</value>
  </data>
  <data name="GIFQuality_Bit8" xml:space="preserve">
    <value>Octree quantizer 256 kolorów (wolne kodowanie, ale lepsza jakość)</value>
  </data>
  <data name="ShapeType_DrawingImage" xml:space="preserve">
    <value>Obraz (plik)</value>
  </data>
  <data name="ScreenRecordGIFEncoding_NET" xml:space="preserve">
    <value>.NET (zła jakość)</value>
  </data>
  <data name="ReplCodeMenuEntry_ia_Auto_increment_alphanumeric" xml:space="preserve">
    <value>Automatycznie rosnąca liczba alfanumeryczna bez uwzględniania wielkości liter (liczba 0 na początku: {n})</value>
  </data>
  <data name="AfterCaptureTasks_AddImageEffects" xml:space="preserve">
    <value>Dodaj efekty graficzne</value>
  </data>
  <data name="AfterCaptureTasks_DeleteFile" xml:space="preserve">
    <value>Usuń plik lokalnie</value>
  </data>
  <data name="ExportImportControl_Serialize_Export_failed_" xml:space="preserve">
    <value>Eksport nie powiódł się.</value>
  </data>
  <data name="ReplCodeMenuCategory_Computer" xml:space="preserve">
    <value>Komputer</value>
  </data>
  <data name="FileExistAction_UniqueName" xml:space="preserve">
    <value>Dodaj numer do nazwy pliku</value>
  </data>
  <data name="ImgurThumbnailType_Large_Thumbnail" xml:space="preserve">
    <value>Duża miniatura</value>
  </data>
  <data name="ReplCodeMenuEntry_yy_Current_year__2_digits_" xml:space="preserve">
    <value>Rok (2 cyfry)</value>
  </data>
  <data name="PNGBitDepth_Automatic" xml:space="preserve">
    <value>Wykryj automatycznie</value>
  </data>
  <data name="ImageEditorStartMode_PreviousState" xml:space="preserve">
    <value>Poprzedni stan</value>
  </data>
  <data name="ShapeType_RegionEllipse" xml:space="preserve">
    <value>Region elipsy</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIF" xml:space="preserve">
    <value>Rozpocznij/Zatrzymaj nagrywanie ekranu (GIF)</value>
  </data>
  <data name="YouTubeVideoPrivacy_Unlisted" xml:space="preserve">
    <value>Niepubliczny</value>
  </data>
  <data name="ObjectListView_ObjectListView_Name" xml:space="preserve">
    <value>Nazwa</value>
  </data>
  <data name="ReplCodeMenuCategory_Window" xml:space="preserve">
    <value>Okno</value>
  </data>
  <data name="HotkeyType_Ruler" xml:space="preserve">
    <value>Linijka</value>
  </data>
  <data name="ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from" xml:space="preserve">
    <value>Link do pobrania ustawień</value>
  </data>
  <data name="ShapeType_DrawingFreehand" xml:space="preserve">
    <value>Odręczne (F)</value>
  </data>
  <data name="ReplCodeMenuEntry_pm_Gets_AM_PM" xml:space="preserve">
    <value>AM/PM</value>
  </data>
  <data name="DirectoryNameEditor_EditValue_Browse_for_a_folder___" xml:space="preserve">
    <value>Przeglądaj w poszukiwaniu folderu...</value>
  </data>
  <data name="LinearGradientMode_BackwardDiagonal" xml:space="preserve">
    <value>Przekątna w dół</value>
  </data>
  <data name="ShapeType_DrawingCursor" xml:space="preserve">
    <value>Kursor</value>
  </data>
  <data name="ImgurThumbnailType_Huge_Thumbnail" xml:space="preserve">
    <value>Olbrzymia miniatura</value>
  </data>
  <data name="LinearGradientMode_Horizontal" xml:space="preserve">
    <value>Poziomo</value>
  </data>
  <data name="HotkeyType_AbortScreenRecording" xml:space="preserve">
    <value>O nagrywaniu ekranu</value>
  </data>
  <data name="ReplCodeMenuEntry_y_Current_year" xml:space="preserve">
    <value>Rok</value>
  </data>
  <data name="PastebinExpiration_W2" xml:space="preserve">
    <value>2 tygodnie</value>
  </data>
  <data name="ImageEditorStartMode_Fullscreen" xml:space="preserve">
    <value>Pełny ekran</value>
  </data>
  <data name="AfterCaptureTasks_CopyFilePathToClipboard" xml:space="preserve">
    <value>Skopiuj ścieżkę pliku do schowka</value>
  </data>
  <data name="HotkeyType_ScreenRecorder" xml:space="preserve">
    <value>Rozpocznij/Zatrzymaj nagrywanie ekranu</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFile" xml:space="preserve">
    <value>Zapisz obraz do pliku</value>
  </data>
  <data name="ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_" xml:space="preserve">
    <value>Ścieżka pliku z rozszerzeniem nazwy pliku wyjściowego</value>
  </data>
  <data name="URLSharingServices_GoogleImageSearch" xml:space="preserve">
    <value>Google Lens</value>
  </data>
  <data name="HotkeyType_IndexFolder" xml:space="preserve">
    <value>Indeksator katalogów</value>
  </data>
  <data name="ReplCodeMenuEntry_unix_Unix_timestamp" xml:space="preserve">
    <value>Unix timestamp</value>
  </data>
  <data name="ScreenRecordGIFEncoding_FFmpeg" xml:space="preserve">
    <value>FFmpeg (dobra jakość)</value>
  </data>
  <data name="HotkeyType_TweetMessage" xml:space="preserve">
    <value>Tweetnij wiadomość</value>
  </data>
  <data name="DrawImageSizeMode_DontResize" xml:space="preserve">
    <value>Nie zmieniaj rozmiaru</value>
  </data>
  <data name="HotkeyType_StopUploads" xml:space="preserve">
    <value>Zatrzymaj wszystkie aktywne przesyłania</value>
  </data>
  <data name="AfterUploadTasks_OpenURL" xml:space="preserve">
    <value>Otwórz link</value>
  </data>
  <data name="AfterCaptureTasks_AnnotateImage" xml:space="preserve">
    <value>Otwórz w edytorze obrazów</value>
  </data>
  <data name="MyPictureBox_LoadImageAsync_Loading_image___" xml:space="preserve">
    <value>Ładowanie obrazu...</value>
  </data>
  <data name="HotkeyType_LastRegion" xml:space="preserve">
    <value>Przechwytywanie ostatniego regionu</value>
  </data>
  <data name="Helpers_OpenFolder_Folder_not_exist_" xml:space="preserve">
    <value>Folder nie istnieje:</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_CurrentVersion" xml:space="preserve">
    <value>Aktualna wersja</value>
  </data>
  <data name="FileDestination_Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_" xml:space="preserve">
    <value>Dostępna jest nowsza wersja {0}.
Czy chcesz ją pobrać i zainstalować?</value>
  </data>
  <data name="WavFileNameEditor_EditValue_Browse_for_a_sound_file___" xml:space="preserve">
    <value>Przeglądaj w poszukiwaniu pliku dźwiękowego...</value>
  </data>
  <data name="Helpers_OpenFile_File_not_exist_" xml:space="preserve">
    <value>Plik nie istnieje:</value>
  </data>
  <data name="Helpers_BrowseFolder_Choose_folder" xml:space="preserve">
    <value>Wybierz folder</value>
  </data>
  <data name="ExportImportControl_Deserialize_Import_failed_" xml:space="preserve">
    <value>Import nie powiódł się.</value>
  </data>
  <data name="Extensions_AddContextMenu_Delete" xml:space="preserve">
    <value>Usuń</value>
  </data>
  <data name="Extensions_AddContextMenu_Paste" xml:space="preserve">
    <value>Wklej</value>
  </data>
  <data name="HotkeyType_QRCodeDecodeFromScreen" xml:space="preserve">
    <value>Kod QR (dekoduj z ekranu)</value>
  </data>
  <data name="LinearGradientMode_ForwardDiagonal" xml:space="preserve">
    <value>Przekątna w górę</value>
  </data>
  <data name="PNGBitDepth_Bit24" xml:space="preserve">
    <value>24 bity</value>
  </data>
  <data name="ReplCodeMenuEntry_wy_Week_of_year" xml:space="preserve">
    <value>Tydzień roku</value>
  </data>
  <data name="DrawImageSizeMode_AbsoluteSize" xml:space="preserve">
    <value>Rozmiar absolutny</value>
  </data>
  <data name="HotkeyType_OpenImageHistory" xml:space="preserve">
    <value>Otwórz okno historii obrazów</value>
  </data>
  <data name="ReplCodeMenuCategory_Incremental" xml:space="preserve">
    <value>Przyrostowo</value>
  </data>
  <data name="RandomEmojiRepeatUsingN" xml:space="preserve">
    <value>Losowe emoji (powtórz przy użyciu {n})</value>
  </data>
  <data name="AfterCaptureTasks_SaveThumbnailImageToFile" xml:space="preserve">
    <value>Zapisz miniaturkę obrazu do pliku</value>
  </data>
  <data name="DownloaderForm_StartDownload_Downloading_" xml:space="preserve">
    <value>Pobieranie.</value>
  </data>
  <data name="RegionCaptureAction_RemoveShapeCancelCapture" xml:space="preserve">
    <value>Usuń kształt lub anuluj przechwytywanie</value>
  </data>
  <data name="ReplCodeMenuEntry_un_User_name" xml:space="preserve">
    <value>Nazwa użytkownika</value>
  </data>
  <data name="ShapeType_DrawingMagnify" xml:space="preserve">
    <value>Lupa</value>
  </data>
  <data name="CodeMenu_Create_Close" xml:space="preserve">
    <value>Zamknij</value>
  </data>
  <data name="ShapeType_DrawingSticker" xml:space="preserve">
    <value>Naklejka</value>
  </data>
  <data name="HotkeyType_QRCode" xml:space="preserve">
    <value>Kod QR</value>
  </data>
  <data name="PastebinExpiration_W1" xml:space="preserve">
    <value>1 tydzień</value>
  </data>
  <data name="CustomUploaderDestinationType_URLSharingService" xml:space="preserve">
    <value>Usługa udostępniania linku</value>
  </data>
  <data name="ShapeType_EffectHighlight" xml:space="preserve">
    <value>Podświetlenie (H)</value>
  </data>
  <data name="GIFQuality_Grayscale" xml:space="preserve">
    <value>Palette quantizer grayscale 256 kolorów</value>
  </data>
  <data name="GIFQuality_Default" xml:space="preserve">
    <value>Domyślne kodowanie .NET (szybkie kodowanie, ale średnia jakość)</value>
  </data>
  <data name="ReplCodeMenuEntry_rx_Random_hexadecimal" xml:space="preserve">
    <value>Losowy znak szesnastkowy (powtarzaj za pomocą {n})</value>
  </data>
  <data name="PastebinPrivacy_Private" xml:space="preserve">
    <value>Prywatny (tylko członkowie)</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Błąd</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAnimal" xml:space="preserve">
    <value>Losowe zwierzę</value>
  </data>
  <data name="URLSharingServices_CustomURLSharingService" xml:space="preserve">
    <value>Własna usługa udostępniania linku</value>
  </data>
  <data name="RegionCaptureAction_CaptureFullscreen" xml:space="preserve">
    <value>Przechwytywanie całego ekranu</value>
  </data>
  <data name="ReplCodeMenuEntry_pn_Process_name_of_active_window" xml:space="preserve">
    <value>Nazwa procesu okna</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Waiting_" xml:space="preserve">
    <value>Oczekiwanie.</value>
  </data>
  <data name="HotkeyType_ImageEditor" xml:space="preserve">
    <value>Edytor obrazu</value>
  </data>
  <data name="URLSharingServices_Email" xml:space="preserve">
    <value>E-mail</value>
  </data>
  <data name="HotkeyType_OpenHistory" xml:space="preserve">
    <value>Otwórz okno historii</value>
  </data>
  <data name="ShapeType_ToolSelect" xml:space="preserve">
    <value>Wybierz i przesuń (M)</value>
  </data>
  <data name="ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric" xml:space="preserve">
    <value>Automatyczne zwiększanie o podstawę {n} za pomocą znaków alfanumerycznych (1 &lt; n &lt; 63)</value>
  </data>
  <data name="HotkeyType_CaptureWebpage" xml:space="preserve">
    <value>Przechwytywanie strony WWW</value>
  </data>
  <data name="RegionCaptureAction_CancelCapture" xml:space="preserve">
    <value>Anuluj przechwytywanie</value>
  </data>
  <data name="AfterCaptureTasks_ScanQRCode" xml:space="preserve">
    <value>Skanuj kod QR</value>
  </data>
  <data name="HotkeyType_RectangleLight" xml:space="preserve">
    <value>Przechwytywanie regionu (jasny)</value>
  </data>
  <data name="ProxyMethod_Automatic" xml:space="preserve">
    <value>Automatycznie</value>
  </data>
  <data name="HotkeyType_FileUpload" xml:space="preserve">
    <value>Prześlij plik</value>
  </data>
  <data name="ReplCodeMenuEntry_guid_Random_guid" xml:space="preserve">
    <value>Losowy GUID</value>
  </data>
  <data name="ShapeType_DrawingLine" xml:space="preserve">
    <value>Linia (L)</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_value" xml:space="preserve">
    <value>Skopiuj wartość</value>
  </data>
  <data name="AfterCaptureTasks_ShowBeforeUploadWindow" xml:space="preserve">
    <value>Pokaż okno "Przed przesłaniem"</value>
  </data>
  <data name="AfterCaptureTasks_ShowInExplorer" xml:space="preserve">
    <value>Pokaż plik w eksploratorze</value>
  </data>
  <data name="ImageDestination_CustomImageUploader" xml:space="preserve">
    <value>Własny serwis obrazów</value>
  </data>
  <data name="HotkeyType_Category_ScreenCapture" xml:space="preserve">
    <value>Przechwytywanie ekranu</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_No" xml:space="preserve">
    <value>Nie</value>
  </data>
  <data name="HotkeyType_ActiveWindow" xml:space="preserve">
    <value>Przechwytywanie aktywnego okna</value>
  </data>
  <data name="ShapeType_DrawingStep" xml:space="preserve">
    <value>Krok (I)</value>
  </data>
  <data name="ReplCodeMenuEntry_i_Auto_increment_number" xml:space="preserve">
    <value>Automatycznie rosnąca liczba (liczba 0 na początku: {n})</value>
  </data>
  <data name="HotkeyType_ClipboardUpload" xml:space="preserve">
    <value>Prześlij ze schowka</value>
  </data>
  <data name="ReplCodeMenuEntry_n_New_line" xml:space="preserve">
    <value>Nowa linia</value>
  </data>
  <data name="ReplCodeMenuEntry_mon2_Current_month_name__English_" xml:space="preserve">
    <value>Nazwa miesiąca (w języku angielskim)</value>
  </data>
  <data name="HotkeyType_OpenScreenshotsFolder" xml:space="preserve">
    <value>Otwórz folder zrzutów ekranu</value>
  </data>
  <data name="ReplCodeMenuEntry_width_Gets_image_width" xml:space="preserve">
    <value>Szerokość obrazu</value>
  </data>
  <data name="ReplCodeMenuEntry_w2_Current_week_name__English_" xml:space="preserve">
    <value>Nazwa tygodnia (angielski)</value>
  </data>
  <data name="ExeFileNameEditor_EditValue_Browse_for_executable___" xml:space="preserve">
    <value>Przeglądaj w poszukiwaniu pliku wykonywalnego...</value>
  </data>
  <data name="ImageDestination_FileUploader" xml:space="preserve">
    <value>Serwis plików</value>
  </data>
  <data name="ImageEditorStartMode_AutoSize" xml:space="preserve">
    <value>Rozmiar automatyczny</value>
  </data>
  <data name="HotkeyType_None" xml:space="preserve">
    <value>Brak</value>
  </data>
  <data name="PNGBitDepth_Default" xml:space="preserve">
    <value>Domyślnie</value>
  </data>
  <data name="Helpers_CreateDirectoryIfNotExist_Create_failed_" xml:space="preserve">
    <value>Nie udało się utworzyć katalogu.</value>
  </data>
  <data name="ProxyMethod_Manual" xml:space="preserve">
    <value>Ręcznie</value>
  </data>
  <data name="DownloaderForm_ChangeStatus_Status___0_" xml:space="preserve">
    <value>Status: {0}</value>
  </data>
  <data name="HotkeyType_StartScreenRecorderGIF" xml:space="preserve">
    <value>Rozpocznij/Zatrzymaj nagrywanie ekranu (GIF) przy użyciu ostatniego regionu</value>
  </data>
  <data name="ImgurThumbnailType_Small_Square" xml:space="preserve">
    <value>Mały kwadrat</value>
  </data>
  <data name="HotkeyType_MonitorTest" xml:space="preserve">
    <value>Test monitora</value>
  </data>
  <data name="Extensions_AddContextMenu_Copy" xml:space="preserve">
    <value>Kopiuj</value>
  </data>
  <data name="AfterUploadTasks_UseURLShortener" xml:space="preserve">
    <value>Skróć link</value>
  </data>
  <data name="ReplCodeMenuEntry_rf_Random_line_from_file" xml:space="preserve">
    <value>Losowa linia z pliku (Użyj {filepath} aby określić plik)</value>
  </data>
  <data name="DownloaderForm_StartDownload_Cancel" xml:space="preserve">
    <value>Anuluj</value>
  </data>
  <data name="HotkeyType_Category_Tools" xml:space="preserve">
    <value>Narzędzia</value>
  </data>
  <data name="FileDestination_SharedFolder" xml:space="preserve">
    <value>Udostępniony folder</value>
  </data>
  <data name="HotkeyType_ActiveMonitor" xml:space="preserve">
    <value>Przechwytywanie aktywnego monitora</value>
  </data>
  <data name="DownloaderForm_StartDownload_Getting_file_size_" xml:space="preserve">
    <value>Uzyskaj rozmiar pliku.</value>
  </data>
  <data name="HotkeyType_Category_Other" xml:space="preserve">
    <value>Inne</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Filename___0_" xml:space="preserve">
    <value>Nazwa pliku: {0}</value>
  </data>
  <data name="ShapeType_DrawingEllipse" xml:space="preserve">
    <value>Elipsa (E)</value>
  </data>
  <data name="HotkeyType_ColorPicker" xml:space="preserve">
    <value>Selektor kolorów</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>Zatrzymaj</value>
  </data>
  <data name="TextDestination_FileUploader" xml:space="preserve">
    <value>Serwis plików</value>
  </data>
  <data name="MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__" xml:space="preserve">
    <value>Ładowanie obrazu: {0}%</value>
  </data>
  <data name="ReplCodeMenuEntry_ra_Random_alphanumeric_char" xml:space="preserve">
    <value>Losowy znak alfanumeryczny (powtarzaj za pomocą {n})</value>
  </data>
  <data name="ObjectListView_ObjectListView_Value" xml:space="preserve">
    <value>Wartość</value>
  </data>
  <data name="HotkeyType_DisableHotkeys" xml:space="preserve">
    <value>Wyłącz/Włącz skróty klawiszowe</value>
  </data>
  <data name="RegionCaptureAction_None" xml:space="preserve">
    <value>Nie rób nic</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFileWithDialog" xml:space="preserve">
    <value>Zapisz obraz do pliku jako...</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_name" xml:space="preserve">
    <value>Skopiuj nazwe</value>
  </data>
  <data name="RegionCaptureAction_RemoveShape" xml:space="preserve">
    <value>Usuń kształt</value>
  </data>
  <data name="ActionsCodeMenuEntry_FilePath_File_path" xml:space="preserve">
    <value>Ścieżka pliku</value>
  </data>
  <data name="SupportedLanguage_Automatic" xml:space="preserve">
    <value>Automatycznie</value>
  </data>
  <data name="HotkeyType_VideoConverter" xml:space="preserve">
    <value>Konwerter wideo</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Cancel" xml:space="preserve">
    <value>Anuluj</value>
  </data>
  <data name="FolderSelectDialog_Title_Select_a_folder" xml:space="preserve">
    <value>Wybierz folder</value>
  </data>
  <data name="HotkeyType_OpenMainWindow" xml:space="preserve">
    <value>Otwórz okno główne</value>
  </data>
  <data name="HotkeyType_ScreenColorPicker" xml:space="preserve">
    <value>Selektor kolorów ekranu</value>
  </data>
  <data name="PrintTextForm_LoadSettings_Name___0___Size___1_" xml:space="preserve">
    <value>Nazwa: {0}, Rozmiar: {1}</value>
  </data>
  <data name="HotkeyType_AutoCapture" xml:space="preserve">
    <value>Automatyczne przechwytywanie</value>
  </data>
  <data name="ShapeType_DrawingRectangle" xml:space="preserve">
    <value>Prostokąt (R)</value>
  </data>
  <data name="ImageEditorStartMode_Maximized" xml:space="preserve">
    <value>Zmaksymalizowany</value>
  </data>
  <data name="HotkeyType_ScreenRecorderCustomRegion" xml:space="preserve">
    <value>Rozpocznij/Zatrzymaj nagrywanie ekranu przy użyciu wstępnie skonfigurowanego regionu</value>
  </data>
  <data name="ScreenRecordGIFEncoding_OctreeQuantizer" xml:space="preserve">
    <value>Octree quantizer (Średnia jakość)</value>
  </data>
  <data name="Helpers_BrowseFile_Choose_file" xml:space="preserve">
    <value>Wybierz plik</value>
  </data>
  <data name="ReplCodeMenuEntry_height_Gets_image_height" xml:space="preserve">
    <value>Wysokość obrazu</value>
  </data>
  <data name="PastebinExpiration_M1" xml:space="preserve">
    <value>1 miesiąc</value>
  </data>
  <data name="ShapeType_DrawingTextBackground" xml:space="preserve">
    <value>Tekst (tło) (T)</value>
  </data>
  <data name="RandomNonAmbiguousAlphanumericCharRepeatUsingN" xml:space="preserve">
    <value>Losowy, nieoznaczny znak alfanumeryczny (powtórz przy użyciu {n})</value>
  </data>
  <data name="UrlShortenerType_CustomURLShortener" xml:space="preserve">
    <value>Własny skracacz linku</value>
  </data>
  <data name="PastebinPrivacy_Public" xml:space="preserve">
    <value>Publiczny</value>
  </data>
  <data name="FileExistAction_Overwrite" xml:space="preserve">
    <value>Nadpisz plik</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfWatermark" xml:space="preserve">
    <value>Procent obrazu</value>
  </data>
  <data name="HotkeyType_ShortenURL" xml:space="preserve">
    <value>Skróć link</value>
  </data>
  <data name="CustomUploaderDestinationType_TextUploader" xml:space="preserve">
    <value>Serwis tekstu</value>
  </data>
  <data name="FileExistAction_Ask" xml:space="preserve">
    <value>Zapytaj, co robić</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_LatestVersion" xml:space="preserve">
    <value>Najnowsza wersja</value>
  </data>
  <data name="AfterCaptureTasks_ShowAfterCaptureWindow" xml:space="preserve">
    <value>Pokaż okno "Po przechwyceniu"</value>
  </data>
  <data name="HotkeyType_UploadText" xml:space="preserve">
    <value>Prześlij tekst</value>
  </data>
  <data name="ShapeType_ToolCrop" xml:space="preserve">
    <value>Przytnij obraz (C)</value>
  </data>
  <data name="HotkeyType_UploadURL" xml:space="preserve">
    <value>Prześlij z linku</value>
  </data>
  <data name="HotkeyType_ImageSplitter" xml:space="preserve">
    <value>Rozdzielacz obrazu</value>
  </data>
  <data name="AfterUploadTasks_CopyURLToClipboard" xml:space="preserve">
    <value>Skopiuj link do schowka</value>
  </data>
  <data name="ReplCodeMenuEntry_cn_Computer_name" xml:space="preserve">
    <value>Nazwa komputera</value>
  </data>
  <data name="HotkeyType_StartAutoCapture" xml:space="preserve">
    <value>Rozpocznij automatyczne przechwytywanie przy użyciu ostatniego regionu</value>
  </data>
  <data name="ImgurThumbnailType_Medium_Thumbnail" xml:space="preserve">
    <value>Średnia miniatura</value>
  </data>
  <data name="Extensions_AddContextMenu_Undo" xml:space="preserve">
    <value>Cofnij</value>
  </data>
  <data name="AfterCaptureTasks_CopyFileToClipboard" xml:space="preserve">
    <value>Skopiuj plik do schowka</value>
  </data>
  <data name="ResultOfFirstFile" xml:space="preserve">
    <value>Wynik pierwszego pliku:</value>
  </data>
  <data name="ResultOfSecondFile" xml:space="preserve">
    <value>Wynik drugiego pliku:</value>
  </data>
  <data name="Result" xml:space="preserve">
    <value>Wynik:</value>
  </data>
  <data name="Target" xml:space="preserve">
    <value>Cel:</value>
  </data>
  <data name="ArrowHeadDirection_End" xml:space="preserve">
    <value>Koniec</value>
  </data>
  <data name="ArrowHeadDirection_Start" xml:space="preserve">
    <value>Rozpocznij</value>
  </data>
  <data name="ArrowHeadDirection_Both" xml:space="preserve">
    <value>Obie</value>
  </data>
  <data name="StepType_LettersLowercase" xml:space="preserve">
    <value>Litery (małe)</value>
  </data>
  <data name="StepType_LettersUppercase" xml:space="preserve">
    <value>Litery (wielkie)</value>
  </data>
  <data name="StepType_Numbers" xml:space="preserve">
    <value>Cyfry</value>
  </data>
  <data name="StepType_RomanNumeralsLowercase" xml:space="preserve">
    <value>Cyfry rzymskie (małe litery)</value>
  </data>
  <data name="StepType_RomanNumeralsUppercase" xml:space="preserve">
    <value>Cyfry rzymskie (wielkie litery)</value>
  </data>
  <data name="HotkeyType_ClipboardViewer" xml:space="preserve">
    <value>Przeglądarka schowka</value>
  </data>
  <data name="HotkeyType_InspectWindow" xml:space="preserve">
    <value>Sprawdź okno</value>
  </data>
  <data name="BorderStyle_Solid" xml:space="preserve">
    <value>Linia stała</value>
  </data>
  <data name="BorderStyle_Dash" xml:space="preserve">
    <value>Linia przerywana</value>
  </data>
  <data name="BorderStyle_Dot" xml:space="preserve">
    <value>Kropka</value>
  </data>
  <data name="BorderStyle_DashDot" xml:space="preserve">
    <value>Linia przerywana z kropkami</value>
  </data>
  <data name="BorderStyle_DashDotDot" xml:space="preserve">
    <value>Linia przerywana z dwoma kropkami</value>
  </data>
  <data name="ToastClickAction_CloseNotification" xml:space="preserve">
    <value>Zamknij powiadomienie</value>
  </data>
  <data name="ToastClickAction_AnnotateImage" xml:space="preserve">
    <value>Edytuj obraz</value>
  </data>
  <data name="ToastClickAction_CopyImageToClipboard" xml:space="preserve">
    <value>Skopiuj obraz</value>
  </data>
  <data name="ToastClickAction_CopyFile" xml:space="preserve">
    <value>Skopiuj plik</value>
  </data>
  <data name="ToastClickAction_CopyFilePath" xml:space="preserve">
    <value>Skopiuj ścieżkę pliku</value>
  </data>
  <data name="ToastClickAction_CopyUrl" xml:space="preserve">
    <value>Skopiuj link</value>
  </data>
  <data name="ToastClickAction_OpenFile" xml:space="preserve">
    <value>Otwórz plik</value>
  </data>
  <data name="ToastClickAction_OpenFolder" xml:space="preserve">
    <value>Otwórz folder</value>
  </data>
  <data name="ToastClickAction_OpenUrl" xml:space="preserve">
    <value>Otwórz link</value>
  </data>
  <data name="ToastClickAction_Upload" xml:space="preserve">
    <value>Prześlij plik</value>
  </data>
  <data name="ContentAlignment_TopLeft" xml:space="preserve">
    <value>Góra po lewej</value>
  </data>
  <data name="ContentAlignment_TopCenter" xml:space="preserve">
    <value>Górny środek</value>
  </data>
  <data name="ContentAlignment_TopRight" xml:space="preserve">
    <value>Góra po prawej</value>
  </data>
  <data name="ContentAlignment_MiddleLeft" xml:space="preserve">
    <value>Środek po lewej</value>
  </data>
  <data name="ContentAlignment_MiddleCenter" xml:space="preserve">
    <value>Środek</value>
  </data>
  <data name="ContentAlignment_MiddleRight" xml:space="preserve">
    <value>Środek po prawej</value>
  </data>
  <data name="ContentAlignment_BottomLeft" xml:space="preserve">
    <value>Dół po lewej</value>
  </data>
  <data name="ContentAlignment_BottomCenter" xml:space="preserve">
    <value>Dolny środek</value>
  </data>
  <data name="ContentAlignment_BottomRight" xml:space="preserve">
    <value>Dół po prawej</value>
  </data>
  <data name="URLSharingServices_BingVisualSearch" xml:space="preserve">
    <value>Wyszukiwanie wizualne Bing</value>
  </data>
  <data name="EDataType_Default" xml:space="preserve">
    <value>Domyślnie</value>
  </data>
  <data name="EDataType_File" xml:space="preserve">
    <value>Plik</value>
  </data>
  <data name="EDataType_Image" xml:space="preserve">
    <value>Obraz</value>
  </data>
  <data name="EDataType_Text" xml:space="preserve">
    <value>Tekst</value>
  </data>
  <data name="EDataType_URL" xml:space="preserve">
    <value>Link</value>
  </data>
  <data name="RegionCaptureAction_CaptureLastRegion" xml:space="preserve">
    <value>Przechwycenie ostatniego regionu</value>
  </data>
  <data name="HotkeyType_StopScreenRecording" xml:space="preserve">
    <value>Zatrzymaj nagrywanie ekranu</value>
  </data>
  <data name="HotkeyType_ToggleTrayMenu" xml:space="preserve">
    <value>Przełącz menu zasobnika</value>
  </data>
  <data name="ThumbnailViewClickAction_Default" xml:space="preserve">
    <value>Domyślnie</value>
  </data>
  <data name="ThumbnailViewClickAction_EditImage" xml:space="preserve">
    <value>Edytuj obraz</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFile" xml:space="preserve">
    <value>Otwórz plik</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFolder" xml:space="preserve">
    <value>Otwórz folder</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenImageViewer" xml:space="preserve">
    <value>Otwórz przeglądarkę obrazów</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenURL" xml:space="preserve">
    <value>Otwórz link</value>
  </data>
  <data name="ThumbnailViewClickAction_Select" xml:space="preserve">
    <value>Wybierz</value>
  </data>
  <data name="ImagePreviewLocation_Bottom" xml:space="preserve">
    <value>Dół</value>
  </data>
  <data name="ImagePreviewLocation_Side" xml:space="preserve">
    <value>Bok</value>
  </data>
  <data name="ImagePreviewVisibility_Automatic" xml:space="preserve">
    <value>Automatycznie</value>
  </data>
  <data name="ImagePreviewVisibility_Hide" xml:space="preserve">
    <value>Ukryj</value>
  </data>
  <data name="ImagePreviewVisibility_Show" xml:space="preserve">
    <value>Pokaż</value>
  </data>
  <data name="TaskViewMode_ListView" xml:space="preserve">
    <value>Widok listy</value>
  </data>
  <data name="TaskViewMode_ThumbnailView" xml:space="preserve">
    <value>Widok miniatur</value>
  </data>
  <data name="ThumbnailTitleLocation_Bottom" xml:space="preserve">
    <value>Dół</value>
  </data>
  <data name="ThumbnailTitleLocation_Top" xml:space="preserve">
    <value>Góra</value>
  </data>
  <data name="HotkeyType_ImageViewer" xml:space="preserve">
    <value>Przeglądarka obrazów</value>
  </data>
  <data name="HotkeyType_OCR" xml:space="preserve">
    <value>OCR</value>
  </data>
  <data name="HotkeyType_BorderlessWindow" xml:space="preserve">
    <value>Okno bez obramowania</value>
  </data>
  <data name="AfterCaptureTasks_PinToScreen" xml:space="preserve">
    <value>Przypnij do ekranu</value>
  </data>
  <data name="ToastClickAction_PinToScreen" xml:space="preserve">
    <value>Przypnij do ekranu</value>
  </data>
  <data name="ShareXImageViewer" xml:space="preserve">
    <value>ShareX - Przeglądarka obrazów</value>
  </data>
  <data name="HotkeyType_PinToScreen" xml:space="preserve">
    <value>Przypnij do ekranu</value>
  </data>
  <data name="CutOutEffectType_None" xml:space="preserve">
    <value>Bez efektu</value>
  </data>
  <data name="CutOutEffectType_TornEdge" xml:space="preserve">
    <value>Poszarpane krawędzie</value>
  </data>
  <data name="CutOutEffectType_Wave" xml:space="preserve">
    <value>Fala</value>
  </data>
  <data name="ShapeType_ToolCutOut" xml:space="preserve">
    <value>Wycięcie (X)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromClipboard" xml:space="preserve">
    <value>Przypnij do ekranu (ze schowka)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromFile" xml:space="preserve">
    <value>Przypnij do ekranu (z pliku)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromScreen" xml:space="preserve">
    <value>Przypnij do ekranu (z ekranu)</value>
  </data>
  <data name="HotkeyType_PauseScreenRecording" xml:space="preserve">
    <value>Wstrzymaj nagrywanie ekranu</value>
  </data>
  <data name="ShapeType_DrawingFreehandArrow" xml:space="preserve">
    <value>Strzałka odręczna</value>
  </data>
  <data name="HotkeyType_ImageBeautifier" xml:space="preserve">
    <value>Upiększanie obrazu</value>
  </data>
  <data name="AfterCaptureTasks_BeautifyImage" xml:space="preserve">
    <value>Upiększ obraz</value>
  </data>
  <data name="HotkeyType_Metadata" xml:space="preserve">
    <value />
  </data>
</root>
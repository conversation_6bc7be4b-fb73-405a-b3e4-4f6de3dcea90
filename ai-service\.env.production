# Configurações de Produção - AI Service
# ATENÇÃO: Configure estas variáveis antes do deploy em produção

# Ambiente
NODE_ENV=production
PORT=3003

# URLs dos serviços (ajustar conforme necessário)
BACKEND_URL=http://backend:3001

# Redis (OBRIGATÓRIO)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# OpenAI API (OBRIGATÓRIO - SUBSTITUA PELA CHAVE REAL)
# Obtenha em: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-your-real-openai-api-key-here

# Configurações de logging
LOG_LEVEL=info

# Performance e limites
MAX_CONCURRENT_JOBS=3
QUEUE_CLEANUP_INTERVAL=3600000
MAX_FILE_SIZE=104857600

# Diretórios (não alterar em Docker)
TEMP_DIR=/app/storage/temp
AI_DATA_DIR=/app/storage/ai-data

# Timeouts (em millisegundos)
TRANSCRIPTION_TIMEOUT=300000
ANALYSIS_TIMEOUT=180000
SEARCH_TIMEOUT=30000

# Configurações de qualidade
MIN_AUDIO_QUALITY=64000
MIN_VIDEO_RESOLUTION=480
CONFIDENCE_THRESHOLD=0.7

# Rate limiting (implementação futura)
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX_REQUESTS=100

# Backup e limpeza
AUTO_CLEANUP_TEMP_FILES=true
TEMP_FILE_TTL=3600000
BACKUP_EMBEDDINGS=true

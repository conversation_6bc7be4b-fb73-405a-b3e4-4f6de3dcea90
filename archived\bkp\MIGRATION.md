# Plano de Migração para Aplicação Web

Este documento descreve o processo de migração do sistema de gravação para cartórios de uma abordagem com aplicativos nativos (desktop C# e mobile Flutter) para uma solução web unificada.

## Cronograma

- **Data de início**: 04 de julho de 2025
- **Fase de desenvolvimento**: 04/07/2025 - 25/07/2025
- **Fase de testes**: 26/07/2025 - 10/08/2025
- **Lançamento beta**: 15/08/2025
- **Lançamento final**: 01/09/2025
- **Fim do suporte para aplicativos nativos**: 31/12/2025

## Motivos da migração

1. **Desenvolvimento simplificado**: Manter uma única base de código em vez de três projetos separados.
2. **Atualizações imediatas**: As alterações na aplicação web são disponibilizadas instantaneamente para todos os usuários.
3. **Acessibilidade**: Não requer instalação, permitindo uso em qualquer dispositivo com navegador.
4. **Distribuição simplificada**: Elimina a necessidade de distribuição por lojas de aplicativos.
5. **Manutenção reduzida**: Uma única plataforma para manter e atualizar.

## Tecnologias adotadas

- **Frontend**: React, TypeScript, Material UI
- **PWA**: Workbox para funcionalidades offline
- **Segurança Web**: Web Crypto API, JWT, HTTPS
- **UI Responsiva**: Design adaptável para desktop e dispositivos móveis

## Limitações da migração

- **Acesso a hardware**: Algumas APIs de gravação são limitadas em navegadores móveis, especialmente iOS.
- **Permissões**: Exigência de HTTPS para acesso a recursos como câmera, microfone e compartilhamento de tela.
- **Performance**: Potencialmente menor performance em comparação com aplicativos nativos.

## Plano de execução

### Fase 1: Desenvolvimento (04/07 - 25/07)

1. **Configuração do ambiente**
   - Criar estrutura de projeto React + TypeScript
   - Configurar ferramentas de build e lint
   - Configurar Docker e CI/CD

2. **Desenvolvimento de componentes principais**
   - Implementar sistema de autenticação
   - Desenvolver componente de gravação de tela
   - Implementar consentimento LGPD
   - Desenvolver criptografia local e geração de hash

3. **Integração com backend**
   - Adaptar endpoints existentes conforme necessário
   - Implementar upload seguro de gravações
   - Integrar sistema de assinaturas

### Fase 2: Testes (26/07 - 10/08)

1. **Testes de compatibilidade**
   - Testar em diferentes navegadores (Chrome, Firefox, Safari, Edge)
   - Testar em dispositivos móveis Android e iOS
   - Verificar funcionalidades em diferentes sistemas operacionais

2. **Testes de segurança**
   - Realizar auditoria de segurança na aplicação web
   - Verificar criptografia e proteção de dados
   - Validar conformidade LGPD

3. **Testes de desempenho**
   - Otimizar carregamento de recursos
   - Verificar performance de gravação em diferentes condições
   - Testar upload de arquivos grandes

### Fase 3: Implantação (11/08 - 01/09)

1. **Lançamento beta (15/08)**
   - Disponibilizar para um grupo seleto de usuários
   - Coletar feedback e métricas de uso
   - Realizar ajustes finais

2. **Comunicação e treinamento**
   - Preparar documentação para usuários
   - Criar tutoriais em vídeo
   - Treinar equipe de suporte

3. **Lançamento completo (01/09)**
   - Migrar gradualmente os usuários para a nova plataforma
   - Monitorar métricas de adoção e uso
   - Suporte intensivo nas primeiras semanas

## Estratégia de transição para usuários

### Comunicação

- **Email informativo**: Envio 30 dias antes do lançamento beta
- **Banner no aplicativo**: Aviso sobre a nova versão web 2 semanas antes
- **Webinars de demonstração**: 1 semana antes do lançamento beta

### Migração de dados

- Todas as gravações e metadados serão preservados
- Usuários poderão acessar gravações antigas na nova plataforma
- Perfis, assinaturas e preferências serão transferidos automaticamente

### Período de coexistência

- Os aplicativos nativos e a versão web funcionarão em paralelo até 31/12/2025
- Novas funcionalidades serão implementadas apenas na versão web
- Suporte técnico continuará disponível para ambas as versões durante a transição

## Código arquivado

O código original do aplicativo Flutter foi preservado no diretório `archived-mobile` para referência futura. O código não está mais em desenvolvimento ativo, mas pode ser útil para consulta de algoritmos e técnicas implementadas.

### Tratamento do código ShareX (desktop)

O código relacionado à integração com ShareX, incluindo o plugin `ShareX.CartorioPlugin` e suas dependências, será mantido na estrutura original do repositório por estas razões:

1. **Compatibilidade com instalações existentes**: Usuários com o plugin já instalado poderão continuar usando até 31/12/2025.
2. **Referência para implementações nativas**: O código contém implementações específicas para acesso a recursos de sistema no Windows.
3. **Preservação de algoritmos**: Algoritmos de captura, compressão e hash serão referência para implementações web.

Os seguintes diretórios serão mantidos intactos:
- `ShareX.CartorioPlugin/` - Plugin customizado
- `ShareX/` - Código fonte principal (quando necessário para o plugin)
- `Libs/` - Bibliotecas dependentes

Durante o período de coexistência, correções críticas de segurança serão aplicadas quando necessário, mas nenhum desenvolvimento de novas funcionalidades será feito neste código.

## Status dos componentes

| Componente | Status | Localização |
|------------|--------|------------|
| Aplicativo Mobile Flutter | Arquivado | `/archived-mobile` |
| Aplicativo Desktop C# (ShareX) | Mantido até 31/12/2025 | Diretórios originais: `ShareX/`, `ShareX.CartorioPlugin/`, etc. |
| Backend Node.js | Mantido | `/backend` |
| Serviço LaTeX | Mantido | `/latex-service` |
| Aplicação Web PWA | Ativo (novo) | `/web-app` |

## Próximos passos

- Implementar notificações push para a PWA
- Melhorar suporte offline
- Ampliar testes em diversos navegadores e dispositivos
- Otimizar o tamanho dos arquivos para melhor experiência em redes móveis

## Considerações técnicas adicionais

### Melhorias na API Web de gravação

- Implementar detecção automática de recursos do navegador
- Desenvolver fallbacks para navegadores com suporte limitado
- Explorar o uso de WebCodecs para melhor compressão em tempo real

### Transição técnica do ShareX para Web

A migração do ShareX (aplicativo desktop C#) para web apresenta desafios específicos que serão abordados das seguintes formas:

1. **Equivalência de APIs**: 
   - ShareX usa `System.Drawing` e `SharpDX` para captura de tela
   - Versão web usará `MediaDevices.getDisplayMedia()`
   - Tabela de compatibilidade foi criada para mapear funcionalidades

2. **Recursos específicos do Windows**:
   - Acesso direto ao GDI para captura será substituído por APIs web padronizadas
   - Hotkeys globais do sistema serão substituídas por atalhos de teclado dentro do navegador
   - Integração com área de transferência usará Clipboard API do navegador

3. **Manuseio de arquivos**:
   - Acesso direto ao sistema de arquivos será substituído por File System Access API (quando disponível)
   - Implementação de armazenamento temporário em IndexedDB para navegadores sem suporte
   - Uso de Web Workers para processamento pesado sem bloquear UI

4. **Plugin de integração com cartório**:
   - As funcionalidades do `ShareX.CartorioPlugin` serão reimplementadas como módulos JavaScript
   - Mesmos algoritmos de hash e verificação serão portados para Web Crypto API
   - Interface similar será mantida para minimizar a curva de aprendizado

### Armazenamento offline

- Implementar cache estratégico com Workbox
- Utilizar IndexedDB para armazenamento temporário de gravações
- Criar sistema de sincronização em segundo plano para uploads pendentes

### Segurança aprimorada

- Implementar Content Security Policy (CSP) rigorosa
- Adicionar proteção contra CSRF e XSS
- Implementar verificação em duas etapas para ações sensíveis

### Monitoramento e análise

- Implementar telemetria de erros e desempenho
- Configurar alertas para problemas críticos
- Coletar métricas de uso para orientar melhorias futuras

## Riscos e mitigações

| Risco | Probabilidade | Impacto | Mitigação |
|-------|--------------|---------|-----------|
| Incompatibilidade de navegador | Alta | Médio | Detecção prévia de recursos e instruções claras para o usuário |
| Resistência dos usuários à mudança | Média | Alto | Comunicação eficaz, período de transição e suporte dedicado |
| Problemas de performance | Média | Alto | Otimização de código e testes rigorosos em diferentes dispositivos |
| Interrupções durante a migração | Baixa | Crítico | Implantação gradual e monitoramento contínuo |
| Falhas de segurança | Baixa | Crítico | Auditorias de segurança e testes de penetração antes do lançamento |

## Métricas de sucesso

- **Taxa de adoção**: Meta de 80% dos usuários migrando em 60 dias
- **Satisfação do usuário**: NPS igual ou superior ao dos aplicativos nativos
- **Performance**: Tempo de carregamento inicial inferior a 3 segundos
- **Estabilidade**: Taxa de erros inferior a 0,5%
- **Conversão**: Manter ou melhorar taxa de renovação de assinaturas

## Reorganização da estrutura de arquivos

Para manter o repositório organizado durante a migração e transição, será implementada uma reorganização da estrutura de arquivos conforme abaixo:

### Diretórios a serem mantidos na raiz:

```
sistemadozero/
├── backend/               # Backend Node.js (mantido)
├── latex-service/         # Serviço LaTeX (mantido)
├── web-app/               # Nova aplicação web (ativa)
├── ShareX/                # ShareX core (mantido até 31/12/2025)
├── ShareX.CartorioPlugin/ # Plugin ShareX (mantido até 31/12/2025)
├── Libs/                  # Bibliotecas necessárias (mantidas até 31/12/2025)
├── docker-compose.yml     # Configuração de serviços
├── README.md              # Documentação principal
└── MIGRATION.md           # Este documento
```

### Diretórios a serem movidos para a pasta "archived":

```
sistemadozero/archived/
├── mobile-app/           # Aplicativo Flutter (arquivado)
├── desktop-app/          # Aplicativo desktop standalone (arquivado)
└── docs/                 # Documentação antiga (arquivada)
```

### Diretórios a serem movidos para a pasta "deprecated":

```
sistemadozero/deprecated/
├── ShareX.HelpersLib/     # Bibliotecas não usadas pelo plugin (não necessárias)
├── ShareX.HistoryLib/     # (não necessária)
├── ShareX.ImageEffectsLib/# (não necessária)
├── ShareX.IndexerLib/     # (não necessária)
├── ShareX.MediaLib/       # (não necessária)
├── ShareX.ScreenCaptureLib/# (necessária - manter na raiz)
├── ShareX.UploadersLib/   # (necessária - manter na raiz)
└── outras pastas ShareX/  # Pastas não utilizadas diretamente pelo plugin
```

### Plano de execução da reorganização:

1. Criar os diretórios `archived` e `deprecated`
2. Mover os arquivos seguindo o esquema acima
3. Atualizar referências nos arquivos de projeto e soluções
4. Verificar se não houve quebra de dependências
5. Atualizar a documentação para refletir a nova estrutura

A reorganização será realizada em 05/07/2025, após backup completo do repositório.

> **Importante**: Para os componentes ShareX mantidos na raiz, será conduzida uma análise de dependências para garantir que apenas as bibliotecas estritamente necessárias para o funcionamento do plugin sejam mantidas na raiz.

import 'dart:io';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screen_recorder/screen_recorder.dart';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';
import 'package:flutter/foundation.dart';

class ScreenRecorderService {
  final _recorder = ScreenRecorder();
  String _currentRecordingPath = '';
  DateTime? _startTime;
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  /// Inicia a gravação da tela
  Future<void> startRecording({
    required String orientation,
  }) async {
    final tempDir = await getTemporaryDirectory();
    final uuid = Uuid().v4();
    _currentRecordingPath = '${tempDir.path}/recording_$uuid.mp4';
    
    await _recorder.start(
      fileName: _currentRecordingPath,
      recordAudio: true,
      androidUseMediaRecorder: true,
      androidEncoder: AndroidEncoder.H264,
      iosEncoder: IosEncoder.h264,
      width: orientation == 'horizontal' ? 1280 : 720,
      height: orientation == 'horizontal' ? 720 : 1280,
    );
    
    _startTime = DateTime.now();
  }
  
  /// Para a gravação e retorna o arquivo
  Future<File> stopRecording() async {
    await _recorder.stop();
    
    // Mover arquivo para diretório de documentos (permanente)
    final docsDir = await getApplicationDocumentsDirectory();
    final recordingsDir = Directory('${docsDir.path}/recordings');
    
    if (!await recordingsDir.exists()) {
      await recordingsDir.create(recursive: true);
    }
    
    final fileName = _currentRecordingPath.split('/').last;
    final newPath = '${recordingsDir.path}/$fileName';
    
    final tempFile = File(_currentRecordingPath);
    final newFile = await tempFile.copy(newPath);
    await tempFile.delete();
    
    _currentRecordingPath = newPath;
    return newFile;
  }
  
  /// Calcula o hash SHA-256 de um arquivo
  Future<String> calculateFileHash(File file) async {
    final bytes = await file.readAsBytes();
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  /// Obtém informações do dispositivo
  Future<String> getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return 'Android ${androidInfo.version.release} - ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return '${iosInfo.systemName} ${iosInfo.systemVersion} - ${iosInfo.model}';
      }
      return 'Unknown device';
    } catch (e) {
      debugPrint('Error getting device info: $e');
      return 'Error getting device info';
    }
  }
  
  /// Envia a gravação para o servidor
  Future<Map<String, dynamic>> uploadRecording({
    required File file,
    required String apiUrl,
  }) async {
    try {
      // Calcular hash do arquivo
      final hash = await calculateFileHash(file);
      
      // Obter informações do dispositivo
      final deviceInfo = await getDeviceInfo();
      
      // Preparar o request multipart
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$apiUrl/api/recordings'),
      );
      
      // Adicionar o arquivo
      request.files.add(
        await http.MultipartFile.fromPath(
          'video',
          file.path,
          filename: file.path.split('/').last,
        ),
      );
      
      // Adicionar metadados
      request.fields['consent_timestamp'] = DateTime.now().toIso8601String();
      request.fields['consent_ip'] = '127.0.0.1'; // Simulado para dispositivos móveis
      request.fields['device_info'] = deviceInfo;
      request.fields['orientation'] = file.path.contains('horizontal') ? 'horizontal' : 'vertical';
      request.fields['consent_user_agent'] = 'CartorioMobileApp/1.0.0';
      
      if (_startTime != null) {
        final duration = DateTime.now().difference(_startTime!).inSeconds;
        request.fields['duration'] = duration.toString();
      }
      
      // Enviar request
      final response = await request.send();
      
      if (response.statusCode == 201) {
        final responseBody = await response.stream.bytesToString();
        final result = jsonDecode(responseBody) as Map<String, dynamic>;
        return result;
      } else {
        final error = await response.stream.bytesToString();
        throw Exception('Failed to upload recording: ${response.statusCode} - $error');
      }
    } catch (e) {
      rethrow;
    }
  }
}

<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ShapeType_RegionFreehand" xml:space="preserve">
    <value>区域：自由</value>
  </data>
  <data name="ReplCodeMenuEntry_w_Current_week_name__Local_language_" xml:space="preserve">
    <value>周几（本地语言）</value>
  </data>
  <data name="ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_" xml:space="preserve">
    <value>设置已复制到剪贴板。</value>
  </data>
  <data name="ImgurThumbnailType_Big_Square" xml:space="preserve">
    <value>大正方形</value>
  </data>
  <data name="ReplCodeMenuEntry_s_Current_second" xml:space="preserve">
    <value>当前秒</value>
  </data>
  <data name="TextDestination_CustomTextUploader" xml:space="preserve">
    <value>自定义文本上传器</value>
  </data>
  <data name="ProxyMethod_None" xml:space="preserve">
    <value>无</value>
  </data>
  <data name="ReplCodeMenuEntry_mo_Current_month" xml:space="preserve">
    <value>当前月</value>
  </data>
  <data name="CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___" xml:space="preserve">
    <value>浏览 CSS...</value>
  </data>
  <data name="Extensions_AddContextMenu_Redo" xml:space="preserve">
    <value>重复</value>
  </data>
  <data name="HotkeyType_VideoThumbnailer" xml:space="preserve">
    <value>视频缩略图</value>
  </data>
  <data name="ShapeType_EffectBlur" xml:space="preserve">
    <value>效果：模糊 (B)</value>
  </data>
  <data name="AfterCaptureTasks_ShowQuickTaskMenu" xml:space="preserve">
    <value>显示快速任务菜单</value>
  </data>
  <data name="CustomUploaderDestinationType_URLShortener" xml:space="preserve">
    <value>URL 短链</value>
  </data>
  <data name="ReplCodeMenuEntry_uln_User_login_name" xml:space="preserve">
    <value>用户登录名</value>
  </data>
  <data name="HotkeyType_ImageEffects" xml:space="preserve">
    <value>图像效果</value>
  </data>
  <data name="ShapeType_DrawingImageScreen" xml:space="preserve">
    <value>绘图：屏幕画面</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available" xml:space="preserve">
    <value>新版本 {0} 可用</value>
  </data>
  <data name="AfterUploadTasks_ShowQRCode" xml:space="preserve">
    <value>显示二维码窗口</value>
  </data>
  <data name="ShapeType_DrawingSpeechBalloon" xml:space="preserve">
    <value>绘图：文本气泡 (S)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date" xml:space="preserve">
    <value>{0} 是最新</value>
  </data>
  <data name="HotkeyType_Category_ScreenRecord" xml:space="preserve">
    <value>屏幕录制</value>
  </data>
  <data name="PastebinExpiration_H1" xml:space="preserve">
    <value>1 小时</value>
  </data>
  <data name="HotkeyType_ScrollingCapture" xml:space="preserve">
    <value>滚动截图</value>
  </data>
  <data name="ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all" xml:space="preserve">
    <value>自动递增字母数字区分大小写（使用 {n} 向左填充 0）</value>
  </data>
  <data name="ReplCodeMenuEntry_t_Title_of_active_window" xml:space="preserve">
    <value>活动窗口标题</value>
  </data>
  <data name="AfterCaptureTasks_SendImageToPrinter" xml:space="preserve">
    <value>打印图像</value>
  </data>
  <data name="ShapeType_RegionRectangle" xml:space="preserve">
    <value>区域：矩形</value>
  </data>
  <data name="HotkeyType_ToggleActionsToolbar" xml:space="preserve">
    <value>切换工作栏</value>
  </data>
  <data name="AfterCaptureTasks_PerformActions" xml:space="preserve">
    <value>执行操作</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfCanvas" xml:space="preserve">
    <value>画布百分比</value>
  </data>
  <data name="ReplCodeMenuCategory_Date_and_Time" xml:space="preserve">
    <value>日期时间</value>
  </data>
  <data name="HotkeyType_ImageCombiner" xml:space="preserve">
    <value>图像合并</value>
  </data>
  <data name="HotkeyType_RectangleTransparent" xml:space="preserve">
    <value>截图矩形区域（透明度）</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_" xml:space="preserve">
    <value>下载完成。</value>
  </data>
  <data name="YouTubeVideoPrivacy_Private" xml:space="preserve">
    <value>私享</value>
  </data>
  <data name="AfterUploadTasks_ShareURL" xml:space="preserve">
    <value>分享 URL</value>
  </data>
  <data name="CustomUploaderDestinationType_FileUploader" xml:space="preserve">
    <value>文件上传</value>
  </data>
  <data name="ReplCodeMenuEntry_h_Current_hour" xml:space="preserve">
    <value>当前小时</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_Update_check_failed" xml:space="preserve">
    <value>检查更新失败</value>
  </data>
  <data name="ReplCodeMenuEntry_ms_Current_millisecond" xml:space="preserve">
    <value>当前毫秒</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Install" xml:space="preserve">
    <value>安装</value>
  </data>
  <data name="AfterCaptureTasks_UploadImageToHost" xml:space="preserve">
    <value>上传图片</value>
  </data>
  <data name="ReplCodeMenuEntry_ix_Auto_increment_hexadecimal" xml:space="preserve">
    <value>十六进制自动递增（使用 {n} 向左填充 0）</value>
  </data>
  <data name="CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__" xml:space="preserve">
    <value>青色: {0:0.0}%，品红: {1:0.0}%，黄色: {2:0.0}%, 键: {3:0.0}%</value>
  </data>
  <data name="HotkeyType_FolderUpload" xml:space="preserve">
    <value>上传文件夹</value>
  </data>
  <data name="ReplCodeMenuEntry_mi_Current_minute" xml:space="preserve">
    <value>当前分</value>
  </data>
  <data name="ShapeType_EffectPixelate" xml:space="preserve">
    <value>效果：马赛克 (P)</value>
  </data>
  <data name="ReplCodeMenuEntry_d_Current_day" xml:space="preserve">
    <value>当前日</value>
  </data>
  <data name="PastebinExpiration_D1" xml:space="preserve">
    <value>1 天</value>
  </data>
  <data name="ShapeType_DrawingArrow" xml:space="preserve">
    <value>绘图：箭头 (A)</value>
  </data>
  <data name="ShapeType_DrawingSmartEraser" xml:space="preserve">
    <value>智能擦除</value>
  </data>
  <data name="PastebinPrivacy_Unlisted" xml:space="preserve">
    <value>非公开</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_update_is_available" xml:space="preserve">
    <value>更新可用</value>
  </data>
  <data name="HotkeyType_Category_Upload" xml:space="preserve">
    <value>上传</value>
  </data>
  <data name="Extensions_AddContextMenu_Cut" xml:space="preserve">
    <value>剪切</value>
  </data>
  <data name="FileExistAction_Cancel" xml:space="preserve">
    <value>不保存</value>
  </data>
  <data name="AfterCaptureTasks_CopyImageToClipboard" xml:space="preserve">
    <value>图像复制到剪贴板</value>
  </data>
  <data name="PNGBitDepth_Bit32" xml:space="preserve">
    <value>32 位</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFActiveWindow" xml:space="preserve">
    <value>使用活动窗口开始屏幕录制（GIF）</value>
  </data>
  <data name="HotkeyType_PrintScreen" xml:space="preserve">
    <value>截图整个屏幕</value>
  </data>
  <data name="ImageEditorStartMode_Normal" xml:space="preserve">
    <value>普通</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFCustomRegion" xml:space="preserve">
    <value>使用自定义区域开始屏幕录制（GIF）</value>
  </data>
  <data name="HotkeyType_CustomRegion" xml:space="preserve">
    <value>截图自定义区域</value>
  </data>
  <data name="ReplCodeMenuCategory_Image" xml:space="preserve">
    <value>图像</value>
  </data>
  <data name="PastebinExpiration_M10" xml:space="preserve">
    <value>10 分钟</value>
  </data>
  <data name="RegionCaptureAction_SwapToolType" xml:space="preserve">
    <value>互换工具类型</value>
  </data>
  <data name="HotkeyType_RectangleRegion" xml:space="preserve">
    <value>截图矩形区域</value>
  </data>
  <data name="AfterCaptureTasks_DoOCR" xml:space="preserve">
    <value>文字识别（OCR）</value>
  </data>
  <data name="HotkeyType_ExitShareX" xml:space="preserve">
    <value>退出 ShareX</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_Portable" xml:space="preserve">
    <value>新版本 {0} 可用。
要下载吗？</value>
  </data>
  <data name="Helpers_DownloadString_Download_failed_" xml:space="preserve">
    <value>下载失败:</value>
  </data>
  <data name="ShapeType_DrawingTextOutline" xml:space="preserve">
    <value>绘图：文字（描边）（O)</value>
  </data>
  <data name="RegionCaptureAction_CaptureActiveMonitor" xml:space="preserve">
    <value>截图活动显示器</value>
  </data>
  <data name="ImgurThumbnailType_Small_Thumbnail" xml:space="preserve">
    <value>小缩略图</value>
  </data>
  <data name="PrintForm_LoadSettings_Print" xml:space="preserve">
    <value>打印</value>
  </data>
  <data name="GIFQuality_Bit4" xml:space="preserve">
    <value>16 色（八叉树色彩量化）</value>
  </data>
  <data name="AfterUploadTasks_ShowAfterUploadWindow" xml:space="preserve">
    <value>显示"上传后"窗口</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAdjective" xml:space="preserve">
    <value>随机形容词</value>
  </data>
  <data name="Extensions_AddContextMenu_SelectAll" xml:space="preserve">
    <value>全选</value>
  </data>
  <data name="FileDestination_CustomFileUploader" xml:space="preserve">
    <value>自定义文件上传</value>
  </data>
  <data name="LinearGradientMode_Vertical" xml:space="preserve">
    <value>垂直</value>
  </data>
  <data name="ReplCodeMenuCategory_Random" xml:space="preserve">
    <value>随机</value>
  </data>
  <data name="CustomUploaderDestinationType_ImageUploader" xml:space="preserve">
    <value>图像上传</value>
  </data>
  <data name="HotkeyType_HashCheck" xml:space="preserve">
    <value>哈希（Hash）检查</value>
  </data>
  <data name="HotkeyType_ScreenRecorderActiveWindow" xml:space="preserve">
    <value>使用活动窗口开始屏幕录制</value>
  </data>
  <data name="ReplCodeMenuEntry_rn_Random_number_0_to_9" xml:space="preserve">
    <value>随机数 0 至 9</value>
  </data>
  <data name="HotkeyType_ClipboardUploadWithContentViewer" xml:space="preserve">
    <value>从剪贴板内容查看器上传</value>
  </data>
  <data name="YouTubeVideoPrivacy_Public" xml:space="preserve">
    <value>公开</value>
  </data>
  <data name="HSB_ToString_" xml:space="preserve">
    <value>色调: {0:0.0}度，饱和度: {1:0.0}%，亮度: {2:0.0}%</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_OK" xml:space="preserve">
    <value>确定</value>
  </data>
  <data name="HotkeyType_DragDropUpload" xml:space="preserve">
    <value>拖放上传</value>
  </data>
  <data name="PastebinExpiration_N" xml:space="preserve">
    <value>从不</value>
  </data>
  <data name="HotkeyType_StartScreenRecorder" xml:space="preserve">
    <value>使用最后一个区域开始屏幕录制</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Yes" xml:space="preserve">
    <value>是</value>
  </data>
  <data name="HotkeyType_ImageThumbnailer" xml:space="preserve">
    <value>图像缩略图工具</value>
  </data>
  <data name="ReplCodeMenuEntry_mon_Current_month_name__Local_language_" xml:space="preserve">
    <value>当月名称（本地语言）</value>
  </data>
  <data name="GIFQuality_Bit8" xml:space="preserve">
    <value>256 色（编码慢，但质量更好）</value>
  </data>
  <data name="ShapeType_DrawingImage" xml:space="preserve">
    <value>绘图：图片文件</value>
  </data>
  <data name="ScreenRecordGIFEncoding_NET" xml:space="preserve">
    <value>.NET（低质量）</value>
  </data>
  <data name="ReplCodeMenuEntry_ia_Auto_increment_alphanumeric" xml:space="preserve">
    <value>自动递增字母数字不区分大小写（使用 {n} 向左填充 0）</value>
  </data>
  <data name="AfterCaptureTasks_AddImageEffects" xml:space="preserve">
    <value>添加图像特效</value>
  </data>
  <data name="AfterCaptureTasks_DeleteFile" xml:space="preserve">
    <value>删除本地文件</value>
  </data>
  <data name="ExportImportControl_Serialize_Export_failed_" xml:space="preserve">
    <value>导出失败。</value>
  </data>
  <data name="ReplCodeMenuCategory_Computer" xml:space="preserve">
    <value>计算机</value>
  </data>
  <data name="FileExistAction_UniqueName" xml:space="preserve">
    <value>附加数字到文件名</value>
  </data>
  <data name="ImgurThumbnailType_Large_Thumbnail" xml:space="preserve">
    <value>大缩略图</value>
  </data>
  <data name="ReplCodeMenuEntry_yy_Current_year__2_digits_" xml:space="preserve">
    <value>当前年（2 位数字）</value>
  </data>
  <data name="PNGBitDepth_Automatic" xml:space="preserve">
    <value>自动检测</value>
  </data>
  <data name="ImageEditorStartMode_PreviousState" xml:space="preserve">
    <value>先前状态</value>
  </data>
  <data name="ShapeType_RegionEllipse" xml:space="preserve">
    <value>区域：椭圆</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIF" xml:space="preserve">
    <value>开始/停止屏幕录制（GIF）</value>
  </data>
  <data name="YouTubeVideoPrivacy_Unlisted" xml:space="preserve">
    <value>不公开</value>
  </data>
  <data name="ObjectListView_ObjectListView_Name" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="ReplCodeMenuCategory_Window" xml:space="preserve">
    <value>窗口</value>
  </data>
  <data name="HotkeyType_Ruler" xml:space="preserve">
    <value>测量尺</value>
  </data>
  <data name="ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from" xml:space="preserve">
    <value>设置下载的URL</value>
  </data>
  <data name="ShapeType_DrawingFreehand" xml:space="preserve">
    <value>绘图：画笔（F）</value>
  </data>
  <data name="ReplCodeMenuEntry_pm_Gets_AM_PM" xml:space="preserve">
    <value>上午/下午</value>
  </data>
  <data name="DirectoryNameEditor_EditValue_Browse_for_a_folder___" xml:space="preserve">
    <value>浏览文件夹...</value>
  </data>
  <data name="LinearGradientMode_BackwardDiagonal" xml:space="preserve">
    <value>反对角线</value>
  </data>
  <data name="ShapeType_DrawingCursor" xml:space="preserve">
    <value>绘图：指针</value>
  </data>
  <data name="ImgurThumbnailType_Huge_Thumbnail" xml:space="preserve">
    <value>巨大缩略图</value>
  </data>
  <data name="LinearGradientMode_Horizontal" xml:space="preserve">
    <value>水平</value>
  </data>
  <data name="HotkeyType_AbortScreenRecording" xml:space="preserve">
    <value>中止屏幕录制</value>
  </data>
  <data name="ReplCodeMenuEntry_y_Current_year" xml:space="preserve">
    <value>当前年</value>
  </data>
  <data name="PastebinExpiration_W2" xml:space="preserve">
    <value>2 周</value>
  </data>
  <data name="ImageEditorStartMode_Fullscreen" xml:space="preserve">
    <value>全屏</value>
  </data>
  <data name="AfterCaptureTasks_CopyFilePathToClipboard" xml:space="preserve">
    <value>复制文件路径到剪贴板</value>
  </data>
  <data name="HotkeyType_ScreenRecorder" xml:space="preserve">
    <value>屏幕录制</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFile" xml:space="preserve">
    <value>保存图像文件</value>
  </data>
  <data name="ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_" xml:space="preserve">
    <value>无扩展名的路径 + "输出文件的扩展名"</value>
  </data>
  <data name="URLSharingServices_GoogleImageSearch" xml:space="preserve">
    <value>Google 图片搜索</value>
  </data>
  <data name="HotkeyType_IndexFolder" xml:space="preserve">
    <value>索引文件夹</value>
  </data>
  <data name="ReplCodeMenuEntry_unix_Unix_timestamp" xml:space="preserve">
    <value>Unix 时间戳</value>
  </data>
  <data name="ScreenRecordGIFEncoding_FFmpeg" xml:space="preserve">
    <value>FFmpeg（高质量）</value>
  </data>
  <data name="HotkeyType_TweetMessage" xml:space="preserve">
    <value>发推特消息</value>
  </data>
  <data name="DrawImageSizeMode_DontResize" xml:space="preserve">
    <value>不调整大小</value>
  </data>
  <data name="HotkeyType_StopUploads" xml:space="preserve">
    <value>停止所有活动上传</value>
  </data>
  <data name="AfterUploadTasks_OpenURL" xml:space="preserve">
    <value>打开网址</value>
  </data>
  <data name="AfterCaptureTasks_AnnotateImage" xml:space="preserve">
    <value>打开图像编辑器</value>
  </data>
  <data name="MyPictureBox_LoadImageAsync_Loading_image___" xml:space="preserve">
    <value>图片加载中...</value>
  </data>
  <data name="HotkeyType_LastRegion" xml:space="preserve">
    <value>截图上次区域</value>
  </data>
  <data name="Helpers_OpenFolder_Folder_not_exist_" xml:space="preserve">
    <value>文件夹不存在：</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_CurrentVersion" xml:space="preserve">
    <value>当前版本</value>
  </data>
  <data name="FileDestination_Email" xml:space="preserve">
    <value>电子邮箱</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_" xml:space="preserve">
    <value>有一个新版本 {0} 可用.
你要下载安装吗?</value>
  </data>
  <data name="WavFileNameEditor_EditValue_Browse_for_a_sound_file___" xml:space="preserve">
    <value>浏览音频文件...</value>
  </data>
  <data name="Helpers_OpenFile_File_not_exist_" xml:space="preserve">
    <value>文件不存在:</value>
  </data>
  <data name="Helpers_BrowseFolder_Choose_folder" xml:space="preserve">
    <value>选择文件夹</value>
  </data>
  <data name="ExportImportControl_Deserialize_Import_failed_" xml:space="preserve">
    <value>导入失败。</value>
  </data>
  <data name="Extensions_AddContextMenu_Delete" xml:space="preserve">
    <value>删除</value>
  </data>
  <data name="Extensions_AddContextMenu_Paste" xml:space="preserve">
    <value>粘贴</value>
  </data>
  <data name="HotkeyType_QRCodeDecodeFromScreen" xml:space="preserve">
    <value>QR 码 (解码自屏幕)</value>
  </data>
  <data name="LinearGradientMode_ForwardDiagonal" xml:space="preserve">
    <value>正对角线</value>
  </data>
  <data name="PNGBitDepth_Bit24" xml:space="preserve">
    <value>24 位</value>
  </data>
  <data name="ReplCodeMenuEntry_wy_Week_of_year" xml:space="preserve">
    <value>周数</value>
  </data>
  <data name="DrawImageSizeMode_AbsoluteSize" xml:space="preserve">
    <value>绝对大小</value>
  </data>
  <data name="HotkeyType_OpenImageHistory" xml:space="preserve">
    <value>打开历史图片</value>
  </data>
  <data name="ReplCodeMenuCategory_Incremental" xml:space="preserve">
    <value>增量</value>
  </data>
  <data name="RandomEmojiRepeatUsingN" xml:space="preserve">
    <value>随机 emoji 表情。用 {n} 表示重复次数</value>
  </data>
  <data name="AfterCaptureTasks_SaveThumbnailImageToFile" xml:space="preserve">
    <value>保存缩略图为文件</value>
  </data>
  <data name="DownloaderForm_StartDownload_Downloading_" xml:space="preserve">
    <value>正在下载。</value>
  </data>
  <data name="RegionCaptureAction_RemoveShapeCancelCapture" xml:space="preserve">
    <value>删除形状或取消截图</value>
  </data>
  <data name="ReplCodeMenuEntry_un_User_name" xml:space="preserve">
    <value>用户名</value>
  </data>
  <data name="ShapeType_DrawingMagnify" xml:space="preserve">
    <value>放大</value>
  </data>
  <data name="CodeMenu_Create_Close" xml:space="preserve">
    <value>关闭</value>
  </data>
  <data name="ShapeType_DrawingSticker" xml:space="preserve">
    <value>绘图：贴纸</value>
  </data>
  <data name="HotkeyType_QRCode" xml:space="preserve">
    <value>二维码</value>
  </data>
  <data name="PastebinExpiration_W1" xml:space="preserve">
    <value>1 周</value>
  </data>
  <data name="CustomUploaderDestinationType_URLSharingService" xml:space="preserve">
    <value>URL 分享服务</value>
  </data>
  <data name="ShapeType_EffectHighlight" xml:space="preserve">
    <value>效果：高亮 (H)</value>
  </data>
  <data name="GIFQuality_Grayscale" xml:space="preserve">
    <value>灰度 256 色（调色板色彩量化）</value>
  </data>
  <data name="GIFQuality_Default" xml:space="preserve">
    <value>默认 .NET 编码（编码快速，但质量一般）</value>
  </data>
  <data name="ReplCodeMenuEntry_rx_Random_hexadecimal" xml:space="preserve">
    <value>随机的十六进制字符。重复使用 {n}</value>
  </data>
  <data name="PastebinPrivacy_Private" xml:space="preserve">
    <value>私有（仅成员）</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>错误</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAnimal" xml:space="preserve">
    <value>随机动物</value>
  </data>
  <data name="URLSharingServices_CustomURLSharingService" xml:space="preserve">
    <value>自定义 URL 分享</value>
  </data>
  <data name="RegionCaptureAction_CaptureFullscreen" xml:space="preserve">
    <value>截图全屏</value>
  </data>
  <data name="ReplCodeMenuEntry_pn_Process_name_of_active_window" xml:space="preserve">
    <value>活动窗口的进程名</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Waiting_" xml:space="preserve">
    <value>等待中。</value>
  </data>
  <data name="HotkeyType_ImageEditor" xml:space="preserve">
    <value>图像编辑器</value>
  </data>
  <data name="URLSharingServices_Email" xml:space="preserve">
    <value>电子邮箱</value>
  </data>
  <data name="HotkeyType_OpenHistory" xml:space="preserve">
    <value>打开历史记录</value>
  </data>
  <data name="ShapeType_ToolSelect" xml:space="preserve">
    <value>选择并移动 (M)</value>
  </data>
  <data name="ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric" xml:space="preserve">
    <value>使用字母数字 (1 &lt; n &lt; 63) 按基数 {n} 自动递增</value>
  </data>
  <data name="HotkeyType_CaptureWebpage" xml:space="preserve">
    <value>网页截图</value>
  </data>
  <data name="RegionCaptureAction_CancelCapture" xml:space="preserve">
    <value>取消截图</value>
  </data>
  <data name="AfterCaptureTasks_ScanQRCode" xml:space="preserve">
    <value>扫描二维码</value>
  </data>
  <data name="HotkeyType_RectangleLight" xml:space="preserve">
    <value>截图矩形区域（高亮）</value>
  </data>
  <data name="ProxyMethod_Automatic" xml:space="preserve">
    <value>自动</value>
  </data>
  <data name="HotkeyType_FileUpload" xml:space="preserve">
    <value>上传文件</value>
  </data>
  <data name="ReplCodeMenuEntry_guid_Random_guid" xml:space="preserve">
    <value>随机 GUID</value>
  </data>
  <data name="ShapeType_DrawingLine" xml:space="preserve">
    <value>绘图：线条 (L)</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_value" xml:space="preserve">
    <value>复制值</value>
  </data>
  <data name="AfterCaptureTasks_ShowBeforeUploadWindow" xml:space="preserve">
    <value>显示"上传前"窗口</value>
  </data>
  <data name="AfterCaptureTasks_ShowInExplorer" xml:space="preserve">
    <value>在资源管理器中显示文件</value>
  </data>
  <data name="ImageDestination_CustomImageUploader" xml:space="preserve">
    <value>自定义图像上传</value>
  </data>
  <data name="HotkeyType_Category_ScreenCapture" xml:space="preserve">
    <value>屏幕截图</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_No" xml:space="preserve">
    <value>否</value>
  </data>
  <data name="HotkeyType_ActiveWindow" xml:space="preserve">
    <value>截图活动窗口</value>
  </data>
  <data name="ShapeType_DrawingStep" xml:space="preserve">
    <value>绘图：序号 (I)</value>
  </data>
  <data name="ReplCodeMenuEntry_i_Auto_increment_number" xml:space="preserve">
    <value>自动递增编号（使用 {n} 向左填充 0）</value>
  </data>
  <data name="HotkeyType_ClipboardUpload" xml:space="preserve">
    <value>从剪贴板上传</value>
  </data>
  <data name="ReplCodeMenuEntry_n_New_line" xml:space="preserve">
    <value>新行</value>
  </data>
  <data name="ReplCodeMenuEntry_mon2_Current_month_name__English_" xml:space="preserve">
    <value>当月名称（英语）</value>
  </data>
  <data name="HotkeyType_OpenScreenshotsFolder" xml:space="preserve">
    <value>打开截图文件夹</value>
  </data>
  <data name="ReplCodeMenuEntry_width_Gets_image_width" xml:space="preserve">
    <value>图像宽度</value>
  </data>
  <data name="ReplCodeMenuEntry_w2_Current_week_name__English_" xml:space="preserve">
    <value>周几（英语）</value>
  </data>
  <data name="ExeFileNameEditor_EditValue_Browse_for_executable___" xml:space="preserve">
    <value>浏览可执行文件...</value>
  </data>
  <data name="ImageDestination_FileUploader" xml:space="preserve">
    <value>文件上传</value>
  </data>
  <data name="ImageEditorStartMode_AutoSize" xml:space="preserve">
    <value>自动尺寸</value>
  </data>
  <data name="HotkeyType_None" xml:space="preserve">
    <value>无</value>
  </data>
  <data name="PNGBitDepth_Default" xml:space="preserve">
    <value>默认</value>
  </data>
  <data name="Helpers_CreateDirectoryIfNotExist_Create_failed_" xml:space="preserve">
    <value>未能创建目录。</value>
  </data>
  <data name="ProxyMethod_Manual" xml:space="preserve">
    <value>手动</value>
  </data>
  <data name="DownloaderForm_ChangeStatus_Status___0_" xml:space="preserve">
    <value>状态: {0}</value>
  </data>
  <data name="HotkeyType_StartScreenRecorderGIF" xml:space="preserve">
    <value>使用上一次的区域开始屏幕录制（GIF）</value>
  </data>
  <data name="ImgurThumbnailType_Small_Square" xml:space="preserve">
    <value>小正方形</value>
  </data>
  <data name="HotkeyType_MonitorTest" xml:space="preserve">
    <value>显示器测试</value>
  </data>
  <data name="Extensions_AddContextMenu_Copy" xml:space="preserve">
    <value>复制</value>
  </data>
  <data name="AfterUploadTasks_UseURLShortener" xml:space="preserve">
    <value>缩短 URL</value>
  </data>
  <data name="ReplCodeMenuEntry_rf_Random_line_from_file" xml:space="preserve">
    <value>从文件产生随机线条，使用 {filepath} 选取文件</value>
  </data>
  <data name="DownloaderForm_StartDownload_Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="HotkeyType_Category_Tools" xml:space="preserve">
    <value>工具</value>
  </data>
  <data name="FileDestination_SharedFolder" xml:space="preserve">
    <value>共享文件夹</value>
  </data>
  <data name="HotkeyType_ActiveMonitor" xml:space="preserve">
    <value>截图活动显示器</value>
  </data>
  <data name="DownloaderForm_StartDownload_Getting_file_size_" xml:space="preserve">
    <value>正在获取文件大小。</value>
  </data>
  <data name="HotkeyType_Category_Other" xml:space="preserve">
    <value>其它</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Filename___0_" xml:space="preserve">
    <value>文件名: {0}</value>
  </data>
  <data name="ShapeType_DrawingEllipse" xml:space="preserve">
    <value>绘图：椭圆 (E)</value>
  </data>
  <data name="HotkeyType_ColorPicker" xml:space="preserve">
    <value>拾色器</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="TextDestination_FileUploader" xml:space="preserve">
    <value>文件上传器</value>
  </data>
  <data name="MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__" xml:space="preserve">
    <value>加载图像：{0}%</value>
  </data>
  <data name="ReplCodeMenuEntry_ra_Random_alphanumeric_char" xml:space="preserve">
    <value>随机字符</value>
  </data>
  <data name="ObjectListView_ObjectListView_Value" xml:space="preserve">
    <value>值</value>
  </data>
  <data name="HotkeyType_DisableHotkeys" xml:space="preserve">
    <value>禁用/启用快捷键</value>
  </data>
  <data name="RegionCaptureAction_None" xml:space="preserve">
    <value>无操作</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFileWithDialog" xml:space="preserve">
    <value>保存图像文件为...</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_name" xml:space="preserve">
    <value>复制名称</value>
  </data>
  <data name="RegionCaptureAction_RemoveShape" xml:space="preserve">
    <value>删除形状</value>
  </data>
  <data name="ActionsCodeMenuEntry_FilePath_File_path" xml:space="preserve">
    <value>文件路径</value>
  </data>
  <data name="SupportedLanguage_Automatic" xml:space="preserve">
    <value>自动</value>
  </data>
  <data name="HotkeyType_VideoConverter" xml:space="preserve">
    <value>视频转换器</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Cancel" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="FolderSelectDialog_Title_Select_a_folder" xml:space="preserve">
    <value>选择一个文件夹</value>
  </data>
  <data name="HotkeyType_OpenMainWindow" xml:space="preserve">
    <value>打开主页面</value>
  </data>
  <data name="HotkeyType_ScreenColorPicker" xml:space="preserve">
    <value>屏幕拾色器</value>
  </data>
  <data name="PrintTextForm_LoadSettings_Name___0___Size___1_" xml:space="preserve">
    <value>名称: {0}，大小: {1}</value>
  </data>
  <data name="HotkeyType_AutoCapture" xml:space="preserve">
    <value>自动截图</value>
  </data>
  <data name="ShapeType_DrawingRectangle" xml:space="preserve">
    <value>绘图：矩形 (R)</value>
  </data>
  <data name="ImageEditorStartMode_Maximized" xml:space="preserve">
    <value>最大化</value>
  </data>
  <data name="HotkeyType_ScreenRecorderCustomRegion" xml:space="preserve">
    <value>使用自定义区域开始屏幕录制</value>
  </data>
  <data name="ScreenRecordGIFEncoding_OctreeQuantizer" xml:space="preserve">
    <value>八叉树色彩量化（中等质量）</value>
  </data>
  <data name="Helpers_BrowseFile_Choose_file" xml:space="preserve">
    <value>选择文件</value>
  </data>
  <data name="ReplCodeMenuEntry_height_Gets_image_height" xml:space="preserve">
    <value>图像高度</value>
  </data>
  <data name="PastebinExpiration_M1" xml:space="preserve">
    <value>1 月</value>
  </data>
  <data name="ShapeType_DrawingTextBackground" xml:space="preserve">
    <value>绘图：文字（背景填充）(T)</value>
  </data>
  <data name="RandomNonAmbiguousAlphanumericCharRepeatUsingN" xml:space="preserve">
    <value>随机的非易混淆字母与数字字符。使用 {n} 表示重复次数</value>
  </data>
  <data name="UrlShortenerType_CustomURLShortener" xml:space="preserve">
    <value>自定义网址缩短器</value>
  </data>
  <data name="PastebinPrivacy_Public" xml:space="preserve">
    <value>公开</value>
  </data>
  <data name="FileExistAction_Overwrite" xml:space="preserve">
    <value>覆盖文件</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfWatermark" xml:space="preserve">
    <value>图像百分比</value>
  </data>
  <data name="HotkeyType_ShortenURL" xml:space="preserve">
    <value>缩短 URL</value>
  </data>
  <data name="CustomUploaderDestinationType_TextUploader" xml:space="preserve">
    <value>文本上传</value>
  </data>
  <data name="FileExistAction_Ask" xml:space="preserve">
    <value>询问</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_LatestVersion" xml:space="preserve">
    <value>最新版本</value>
  </data>
  <data name="AfterCaptureTasks_ShowAfterCaptureWindow" xml:space="preserve">
    <value>显示"截图后"窗口</value>
  </data>
  <data name="HotkeyType_UploadText" xml:space="preserve">
    <value>上传文本</value>
  </data>
  <data name="ShapeType_ToolCrop" xml:space="preserve">
    <value>工具：图像裁剪 (C)</value>
  </data>
  <data name="HotkeyType_UploadURL" xml:space="preserve">
    <value>从 URL 上传</value>
  </data>
  <data name="HotkeyType_ImageSplitter" xml:space="preserve">
    <value>图像分割器</value>
  </data>
  <data name="AfterUploadTasks_CopyURLToClipboard" xml:space="preserve">
    <value>URL 复制到剪贴板</value>
  </data>
  <data name="ReplCodeMenuEntry_cn_Computer_name" xml:space="preserve">
    <value>计算机名称</value>
  </data>
  <data name="HotkeyType_StartAutoCapture" xml:space="preserve">
    <value>使用最后一个区域开始自动采集</value>
  </data>
  <data name="ImgurThumbnailType_Medium_Thumbnail" xml:space="preserve">
    <value>中缩略图</value>
  </data>
  <data name="Extensions_AddContextMenu_Undo" xml:space="preserve">
    <value>撤销</value>
  </data>
  <data name="AfterCaptureTasks_CopyFileToClipboard" xml:space="preserve">
    <value>将文件复制到剪贴板</value>
  </data>
  <data name="ResultOfFirstFile" xml:space="preserve">
    <value>第一个文件结果：</value>
  </data>
  <data name="ResultOfSecondFile" xml:space="preserve">
    <value>第二个文件结果：</value>
  </data>
  <data name="Result" xml:space="preserve">
    <value>结果：</value>
  </data>
  <data name="Target" xml:space="preserve">
    <value>目标：</value>
  </data>
  <data name="ArrowHeadDirection_End" xml:space="preserve">
    <value>终点</value>
  </data>
  <data name="ArrowHeadDirection_Start" xml:space="preserve">
    <value>起点</value>
  </data>
  <data name="ArrowHeadDirection_Both" xml:space="preserve">
    <value>两端</value>
  </data>
  <data name="StepType_LettersLowercase" xml:space="preserve">
    <value>字母（小写）</value>
  </data>
  <data name="StepType_LettersUppercase" xml:space="preserve">
    <value>字母（大写）</value>
  </data>
  <data name="StepType_Numbers" xml:space="preserve">
    <value>数字</value>
  </data>
  <data name="StepType_RomanNumeralsLowercase" xml:space="preserve">
    <value>随机数字（小写）</value>
  </data>
  <data name="StepType_RomanNumeralsUppercase" xml:space="preserve">
    <value>随机数字（大写）</value>
  </data>
  <data name="HotkeyType_ClipboardViewer" xml:space="preserve">
    <value>剪贴板查看器</value>
  </data>
  <data name="HotkeyType_InspectWindow" xml:space="preserve">
    <value>探查窗口</value>
  </data>
  <data name="BorderStyle_Solid" xml:space="preserve">
    <value>实线</value>
  </data>
  <data name="BorderStyle_Dash" xml:space="preserve">
    <value>虚线</value>
  </data>
  <data name="BorderStyle_Dot" xml:space="preserve">
    <value>点线</value>
  </data>
  <data name="BorderStyle_DashDot" xml:space="preserve">
    <value>点划线</value>
  </data>
  <data name="BorderStyle_DashDotDot" xml:space="preserve">
    <value>点点划线</value>
  </data>
  <data name="ToastClickAction_CloseNotification" xml:space="preserve">
    <value>关闭通知</value>
  </data>
  <data name="ToastClickAction_AnnotateImage" xml:space="preserve">
    <value>编辑图像</value>
  </data>
  <data name="ToastClickAction_CopyImageToClipboard" xml:space="preserve">
    <value>复制图像</value>
  </data>
  <data name="ToastClickAction_CopyFile" xml:space="preserve">
    <value>复制文件</value>
  </data>
  <data name="ToastClickAction_CopyFilePath" xml:space="preserve">
    <value>复制文件路径</value>
  </data>
  <data name="ToastClickAction_CopyUrl" xml:space="preserve">
    <value>复制链接</value>
  </data>
  <data name="ToastClickAction_OpenFile" xml:space="preserve">
    <value>打开文件</value>
  </data>
  <data name="ToastClickAction_OpenFolder" xml:space="preserve">
    <value>打开文件夹</value>
  </data>
  <data name="ToastClickAction_OpenUrl" xml:space="preserve">
    <value>打开链接</value>
  </data>
  <data name="ToastClickAction_Upload" xml:space="preserve">
    <value>上传文件</value>
  </data>
  <data name="ContentAlignment_TopLeft" xml:space="preserve">
    <value>左上</value>
  </data>
  <data name="ContentAlignment_TopCenter" xml:space="preserve">
    <value>正上</value>
  </data>
  <data name="ContentAlignment_TopRight" xml:space="preserve">
    <value>右下</value>
  </data>
  <data name="ContentAlignment_MiddleLeft" xml:space="preserve">
    <value>左中</value>
  </data>
  <data name="ContentAlignment_MiddleCenter" xml:space="preserve">
    <value>正中</value>
  </data>
  <data name="ContentAlignment_MiddleRight" xml:space="preserve">
    <value>右中</value>
  </data>
  <data name="ContentAlignment_BottomLeft" xml:space="preserve">
    <value>左下</value>
  </data>
  <data name="ContentAlignment_BottomCenter" xml:space="preserve">
    <value>正下</value>
  </data>
  <data name="ContentAlignment_BottomRight" xml:space="preserve">
    <value>右下</value>
  </data>
  <data name="URLSharingServices_BingVisualSearch" xml:space="preserve">
    <value>必应可视化搜索</value>
  </data>
  <data name="EDataType_Default" xml:space="preserve">
    <value>默认</value>
  </data>
  <data name="EDataType_File" xml:space="preserve">
    <value>文件</value>
  </data>
  <data name="EDataType_Image" xml:space="preserve">
    <value>图像</value>
  </data>
  <data name="EDataType_Text" xml:space="preserve">
    <value>文本</value>
  </data>
  <data name="EDataType_URL" xml:space="preserve">
    <value>网址</value>
  </data>
  <data name="RegionCaptureAction_CaptureLastRegion" xml:space="preserve">
    <value>捕捉上次区域</value>
  </data>
  <data name="HotkeyType_StopScreenRecording" xml:space="preserve">
    <value>停止屏幕录制</value>
  </data>
  <data name="HotkeyType_ToggleTrayMenu" xml:space="preserve">
    <value>切换托盘菜单</value>
  </data>
  <data name="ThumbnailViewClickAction_Default" xml:space="preserve">
    <value>默认</value>
  </data>
  <data name="ThumbnailViewClickAction_EditImage" xml:space="preserve">
    <value>编辑图像</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFile" xml:space="preserve">
    <value>打开文件</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFolder" xml:space="preserve">
    <value>打开文件夹</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenImageViewer" xml:space="preserve">
    <value>打开图像查看器</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenURL" xml:space="preserve">
    <value>打开网址</value>
  </data>
  <data name="ThumbnailViewClickAction_Select" xml:space="preserve">
    <value>选择</value>
  </data>
  <data name="ImagePreviewLocation_Bottom" xml:space="preserve">
    <value>底边</value>
  </data>
  <data name="ImagePreviewLocation_Side" xml:space="preserve">
    <value>侧边</value>
  </data>
  <data name="ImagePreviewVisibility_Automatic" xml:space="preserve">
    <value>自动</value>
  </data>
  <data name="ImagePreviewVisibility_Hide" xml:space="preserve">
    <value>隐藏</value>
  </data>
  <data name="ImagePreviewVisibility_Show" xml:space="preserve">
    <value>显示</value>
  </data>
  <data name="TaskViewMode_ListView" xml:space="preserve">
    <value>列表视图</value>
  </data>
  <data name="TaskViewMode_ThumbnailView" xml:space="preserve">
    <value>缩略图视图</value>
  </data>
  <data name="ThumbnailTitleLocation_Bottom" xml:space="preserve">
    <value>底部</value>
  </data>
  <data name="ThumbnailTitleLocation_Top" xml:space="preserve">
    <value>顶部</value>
  </data>
  <data name="HotkeyType_ImageViewer" xml:space="preserve">
    <value>图像查看器</value>
  </data>
  <data name="HotkeyType_OCR" xml:space="preserve">
    <value>OCR</value>
  </data>
  <data name="HotkeyType_BorderlessWindow" xml:space="preserve">
    <value>无边框窗口</value>
  </data>
  <data name="HotkeyType_Metadata" xml:space="preserve">
    <value />
  </data>
</root>
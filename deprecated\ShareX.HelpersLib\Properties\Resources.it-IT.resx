﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ShapeType_RegionFreehand" xml:space="preserve">
    <value>Regione: a Mano Libera</value>
  </data>
  <data name="ReplCodeMenuEntry_w_Current_week_name__Local_language_" xml:space="preserve">
    <value>Nome Giorno della Settimana Corrente (Lingua Locale)</value>
  </data>
  <data name="ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_" xml:space="preserve">
    <value>Impostazioni copiate negli appunti.</value>
  </data>
  <data name="ImgurThumbnailType_Big_Square" xml:space="preserve">
    <value>Quadrato Grande</value>
  </data>
  <data name="ReplCodeMenuEntry_s_Current_second" xml:space="preserve">
    <value>Secondo Corrente</value>
  </data>
  <data name="TextDestination_CustomTextUploader" xml:space="preserve">
    <value>Servizio Caricamento Testo Personalizzato</value>
  </data>
  <data name="ProxyMethod_None" xml:space="preserve">
    <value>Nessuno</value>
  </data>
  <data name="ReplCodeMenuEntry_mo_Current_month" xml:space="preserve">
    <value>Mese Corrente</value>
  </data>
  <data name="CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___" xml:space="preserve">
    <value>Cerca CSS...</value>
  </data>
  <data name="HotkeyType_VideoThumbnailer" xml:space="preserve">
    <value>Generatore Miniature Video</value>
  </data>
  <data name="ShapeType_EffectBlur" xml:space="preserve">
    <value>Effetto: Sfoca</value>
  </data>
  <data name="AfterCaptureTasks_ShowQuickTaskMenu" xml:space="preserve">
    <value>Mostra Menu Operazioni Veloci</value>
  </data>
  <data name="CustomUploaderDestinationType_URLShortener" xml:space="preserve">
    <value>Servizio Accorciamento URL</value>
  </data>
  <data name="ReplCodeMenuEntry_uln_User_login_name" xml:space="preserve">
    <value>Nome Utente per l'Accesso</value>
  </data>
  <data name="HotkeyType_ImageEffects" xml:space="preserve">
    <value>Effetti Immagine</value>
  </data>
  <data name="ShapeType_DrawingImageScreen" xml:space="preserve">
    <value>Disegna: Immagine (Schermo)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available" xml:space="preserve">
    <value>È disponibile una nuova versione di {0}</value>
  </data>
  <data name="AfterUploadTasks_ShowQRCode" xml:space="preserve">
    <value>Mostra Finestra Codice QR</value>
  </data>
  <data name="ShapeType_DrawingSpeechBalloon" xml:space="preserve">
    <value>Disegna: Fumetto</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date" xml:space="preserve">
    <value>{0} è aggiornato</value>
  </data>
  <data name="HotkeyType_Category_ScreenRecord" xml:space="preserve">
    <value>Registra Schermo</value>
  </data>
  <data name="PastebinExpiration_H1" xml:space="preserve">
    <value>1 Ora</value>
  </data>
  <data name="HotkeyType_ScrollingCapture" xml:space="preserve">
    <value>Cattura a Scorrimento</value>
  </data>
  <data name="ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all" xml:space="preserve">
    <value>Auto Incremento Alfanumerico (Case-Sensitive). Sarà aggiunto uno 0 davanti usando {n}</value>
  </data>
  <data name="ReplCodeMenuEntry_t_Title_of_active_window" xml:space="preserve">
    <value>Titolo della Finestra</value>
  </data>
  <data name="AfterCaptureTasks_SendImageToPrinter" xml:space="preserve">
    <value>Stampa Immagine</value>
  </data>
  <data name="ShapeType_RegionRectangle" xml:space="preserve">
    <value>Regione: Rettangolo</value>
  </data>
  <data name="HotkeyType_ToggleActionsToolbar" xml:space="preserve">
    <value>Abilita/Disabilita Barra delle Azioni</value>
  </data>
  <data name="AfterCaptureTasks_PerformActions" xml:space="preserve">
    <value>Esegui Azioni</value>
  </data>
  <data name="ReplCodeMenuCategory_Date_and_Time" xml:space="preserve">
    <value>Data e Ora</value>
  </data>
  <data name="HotkeyType_ImageCombiner" xml:space="preserve">
    <value>Combina Immagini</value>
  </data>
  <data name="HotkeyType_RectangleTransparent" xml:space="preserve">
    <value>Cattura Regione (Trasparente)</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_" xml:space="preserve">
    <value>Scaricamento completato.</value>
  </data>
  <data name="AfterUploadTasks_ShareURL" xml:space="preserve">
    <value>Condividi URL</value>
  </data>
  <data name="CustomUploaderDestinationType_FileUploader" xml:space="preserve">
    <value>Servizio Caricamento File</value>
  </data>
  <data name="ReplCodeMenuEntry_h_Current_hour" xml:space="preserve">
    <value>Ora Corrente</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_Update_check_failed" xml:space="preserve">
    <value>Controllo aggiornamenti fallito</value>
  </data>
  <data name="ReplCodeMenuEntry_ms_Current_millisecond" xml:space="preserve">
    <value>Millisecondo Corrente</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Install" xml:space="preserve">
    <value>Installa</value>
  </data>
  <data name="AfterCaptureTasks_UploadImageToHost" xml:space="preserve">
    <value>Carica Immagine su Host</value>
  </data>
  <data name="ReplCodeMenuEntry_ix_Auto_increment_hexadecimal" xml:space="preserve">
    <value>Auto Incremento Esadecimale. Sarà aggiunto uno 0 davanti usando {n}</value>
  </data>
  <data name="CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__" xml:space="preserve">
    <value>Ciano: {0:0.0}%, Magenta: {1:0.0}%, Giallo: {2:0.0}%, Chiave: {3:0.0}%</value>
  </data>
  <data name="HotkeyType_FolderUpload" xml:space="preserve">
    <value>Carica Cartella</value>
  </data>
  <data name="ReplCodeMenuEntry_mi_Current_minute" xml:space="preserve">
    <value>Minuto Corrente</value>
  </data>
  <data name="ShapeType_EffectPixelate" xml:space="preserve">
    <value>Effetto: Pixel</value>
  </data>
  <data name="ReplCodeMenuEntry_d_Current_day" xml:space="preserve">
    <value>Giorno Corrente</value>
  </data>
  <data name="PastebinExpiration_D1" xml:space="preserve">
    <value>1 Giorno</value>
  </data>
  <data name="ShapeType_DrawingArrow" xml:space="preserve">
    <value>Disegna: Freccia</value>
  </data>
  <data name="PastebinPrivacy_Unlisted" xml:space="preserve">
    <value>Non in Elenco</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_update_is_available" xml:space="preserve">
    <value>Aggiornamento disponibile</value>
  </data>
  <data name="HotkeyType_Category_Upload" xml:space="preserve">
    <value>Carica</value>
  </data>
  <data name="Extensions_AddContextMenu_Cut" xml:space="preserve">
    <value>Taglia</value>
  </data>
  <data name="FileExistAction_Cancel" xml:space="preserve">
    <value>Non Salvare</value>
  </data>
  <data name="AfterCaptureTasks_CopyImageToClipboard" xml:space="preserve">
    <value>Copia Immagine negli Appunti</value>
  </data>
  <data name="PNGBitDepth_Bit32" xml:space="preserve">
    <value>32 bit</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFActiveWindow" xml:space="preserve">
    <value>Avvia/Ferma Registrazione Schermo (GIF) usando la Finestra Attiva</value>
  </data>
  <data name="HotkeyType_PrintScreen" xml:space="preserve">
    <value>Cattura Schermo Intero</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFCustomRegion" xml:space="preserve">
    <value>Avvia/Ferma Registrazione Schermo (GIF) usando la Regione Pre Configurata</value>
  </data>
  <data name="HotkeyType_CustomRegion" xml:space="preserve">
    <value>Cattura Regione Personalizzata</value>
  </data>
  <data name="ReplCodeMenuCategory_Image" xml:space="preserve">
    <value>Immagine</value>
  </data>
  <data name="PastebinExpiration_M10" xml:space="preserve">
    <value>10 Minuti</value>
  </data>
  <data name="RegionCaptureAction_SwapToolType" xml:space="preserve">
    <value>Cambia Tipo di Strumento</value>
  </data>
  <data name="HotkeyType_RectangleRegion" xml:space="preserve">
    <value>Cattura Regione</value>
  </data>
  <data name="AfterCaptureTasks_DoOCR" xml:space="preserve">
    <value>Riconoscimento Testo (OCR)</value>
  </data>
  <data name="HotkeyType_ExitShareX" xml:space="preserve">
    <value>Esci da ShareX</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_Portable" xml:space="preserve">
    <value>È disponibile una nuova versione di {0}.
Vuoi scaricarla ora?</value>
  </data>
  <data name="Helpers_DownloadString_Download_failed_" xml:space="preserve">
    <value>Scaricamento fallito:</value>
  </data>
  <data name="ShapeType_DrawingTextOutline" xml:space="preserve">
    <value>Disegna: Testo (Contorno)</value>
  </data>
  <data name="RegionCaptureAction_CaptureActiveMonitor" xml:space="preserve">
    <value>Cattura Monitor Attivo</value>
  </data>
  <data name="ImgurThumbnailType_Small_Thumbnail" xml:space="preserve">
    <value>Anteprima Piccola</value>
  </data>
  <data name="PrintForm_LoadSettings_Print" xml:space="preserve">
    <value>Stampa</value>
  </data>
  <data name="GIFQuality_Bit4" xml:space="preserve">
    <value>Quantizzatore Octree a 16 colori</value>
  </data>
  <data name="AfterUploadTasks_ShowAfterUploadWindow" xml:space="preserve">
    <value>Mostra Finestra "Dopo il Caricamento"</value>
  </data>
  <data name="FileDestination_CustomFileUploader" xml:space="preserve">
    <value>Servizio Caricamento File Personalizzato</value>
  </data>
  <data name="LinearGradientMode_Vertical" xml:space="preserve">
    <value>Verticale</value>
  </data>
  <data name="ReplCodeMenuCategory_Random" xml:space="preserve">
    <value>Casuale</value>
  </data>
  <data name="CustomUploaderDestinationType_ImageUploader" xml:space="preserve">
    <value>Servizio Caricamento Immagine</value>
  </data>
  <data name="HotkeyType_HashCheck" xml:space="preserve">
    <value>Controlla Hash</value>
  </data>
  <data name="HotkeyType_ScreenRecorderActiveWindow" xml:space="preserve">
    <value>Avvia/Ferma Registrazione Schermo usando la Finestra Attiva</value>
  </data>
  <data name="ReplCodeMenuEntry_rn_Random_number_0_to_9" xml:space="preserve">
    <value>Numero Casuale da 0 a 9. Ripeti usando {n}</value>
  </data>
  <data name="HotkeyType_ClipboardUploadWithContentViewer" xml:space="preserve">
    <value>Carica dagli Appunti Mostrando il Contenuto</value>
  </data>
  <data name="HSB_ToString_" xml:space="preserve">
    <value>Tonalità: {0:0.0}°, Saturazione: {1:0.0}%, Luminosità: {2:0.0}%</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="HotkeyType_DragDropUpload" xml:space="preserve">
    <value>Carica con Drag &amp; Drop</value>
  </data>
  <data name="PastebinExpiration_N" xml:space="preserve">
    <value>Mai</value>
  </data>
  <data name="HotkeyType_StartScreenRecorder" xml:space="preserve">
    <value>Avvia/Ferma Registrazione Schermo usando l'Ultima Regione</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Yes" xml:space="preserve">
    <value>Sì</value>
  </data>
  <data name="ReplCodeMenuEntry_mon_Current_month_name__Local_language_" xml:space="preserve">
    <value>Nome Mese Corrente (Lingua Locale)</value>
  </data>
  <data name="GIFQuality_Bit8" xml:space="preserve">
    <value>Quantizzatore Octree a 256 colori (Lento - Qualità Migliore)</value>
  </data>
  <data name="ShapeType_DrawingImage" xml:space="preserve">
    <value>Disegna: Immagine (File)</value>
  </data>
  <data name="ScreenRecordGIFEncoding_NET" xml:space="preserve">
    <value>.NET (Qualità Cattiva)</value>
  </data>
  <data name="ReplCodeMenuEntry_ia_Auto_increment_alphanumeric" xml:space="preserve">
    <value>Auto Incremento Alfanumerico (Case-Insensitive). Sarà aggiunto uno 0 davanti usando {n}</value>
  </data>
  <data name="AfterCaptureTasks_AddImageEffects" xml:space="preserve">
    <value>Aggiungi Effetti Immagine / Watermark</value>
  </data>
  <data name="AfterCaptureTasks_DeleteFile" xml:space="preserve">
    <value>Elimina File Locale</value>
  </data>
  <data name="ExportImportControl_Serialize_Export_failed_" xml:space="preserve">
    <value>Esportazione fallita.</value>
  </data>
  <data name="ReplCodeMenuCategory_Computer" xml:space="preserve">
    <value>Computer</value>
  </data>
  <data name="FileExistAction_UniqueName" xml:space="preserve">
    <value>Aggiungi Numero al Nome File</value>
  </data>
  <data name="ImgurThumbnailType_Large_Thumbnail" xml:space="preserve">
    <value>Anteprima Grande</value>
  </data>
  <data name="ReplCodeMenuEntry_yy_Current_year__2_digits_" xml:space="preserve">
    <value>Anno Corrente (2 cifre)</value>
  </data>
  <data name="PNGBitDepth_Automatic" xml:space="preserve">
    <value>Rileva Automaticamente</value>
  </data>
  <data name="ShapeType_RegionEllipse" xml:space="preserve">
    <value>Regione: Ellisse</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIF" xml:space="preserve">
    <value>Avvia/Ferma Registrazione Schermo (GIF) usando una Regione Personalizzata</value>
  </data>
  <data name="HotkeyType_Ruler" xml:space="preserve">
    <value>Righello</value>
  </data>
  <data name="ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from" xml:space="preserve">
    <value>Scarica Impostazioni da</value>
  </data>
  <data name="ShapeType_DrawingFreehand" xml:space="preserve">
    <value>Disegna: a Mano Libera</value>
  </data>
  <data name="ReplCodeMenuEntry_pm_Gets_AM_PM" xml:space="preserve">
    <value>Ottieni AM/PM</value>
  </data>
  <data name="DirectoryNameEditor_EditValue_Browse_for_a_folder___" xml:space="preserve">
    <value>Cerca Cartella...</value>
  </data>
  <data name="LinearGradientMode_BackwardDiagonal" xml:space="preserve">
    <value>Diagonale Indietro</value>
  </data>
  <data name="ShapeType_DrawingCursor" xml:space="preserve">
    <value>Disegna: Cursore</value>
  </data>
  <data name="ImgurThumbnailType_Huge_Thumbnail" xml:space="preserve">
    <value>Anteprima Enorme</value>
  </data>
  <data name="LinearGradientMode_Horizontal" xml:space="preserve">
    <value>Orizzontale</value>
  </data>
  <data name="HotkeyType_AbortScreenRecording" xml:space="preserve">
    <value>Annulla Registrazione Schermo</value>
  </data>
  <data name="ReplCodeMenuEntry_y_Current_year" xml:space="preserve">
    <value>Anno Corrente</value>
  </data>
  <data name="PastebinExpiration_W2" xml:space="preserve">
    <value>2 Settimane</value>
  </data>
  <data name="AfterCaptureTasks_CopyFilePathToClipboard" xml:space="preserve">
    <value>Copia Percorso File negli Appunti</value>
  </data>
  <data name="HotkeyType_ScreenRecorder" xml:space="preserve">
    <value>Avvia/Ferma Registrazione Schermo usando una Regione Personalizzata</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFile" xml:space="preserve">
    <value>Salva Immagine...</value>
  </data>
  <data name="ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_" xml:space="preserve">
    <value>Percorso File senza Estensione</value>
  </data>
  <data name="URLSharingServices_GoogleImageSearch" xml:space="preserve">
    <value>Ricerca Immagini Google</value>
  </data>
  <data name="HotkeyType_IndexFolder" xml:space="preserve">
    <value>Indicizza Cartella</value>
  </data>
  <data name="ReplCodeMenuEntry_unix_Unix_timestamp" xml:space="preserve">
    <value>Orario Unix</value>
  </data>
  <data name="ScreenRecordGIFEncoding_FFmpeg" xml:space="preserve">
    <value>FFmpeg (Qualità Buona)</value>
  </data>
  <data name="HotkeyType_TweetMessage" xml:space="preserve">
    <value>Tweet</value>
  </data>
  <data name="HotkeyType_StopUploads" xml:space="preserve">
    <value>Ferma Tutti i Caricamenti Attivi</value>
  </data>
  <data name="AfterUploadTasks_OpenURL" xml:space="preserve">
    <value>Apri URL</value>
  </data>
  <data name="AfterCaptureTasks_AnnotateImage" xml:space="preserve">
    <value>Annota Immagine</value>
  </data>
  <data name="MyPictureBox_LoadImageAsync_Loading_image___" xml:space="preserve">
    <value>Caricamento pagina...</value>
  </data>
  <data name="HotkeyType_LastRegion" xml:space="preserve">
    <value>Cattura Ultima Regione</value>
  </data>
  <data name="Helpers_OpenFolder_Folder_not_exist_" xml:space="preserve">
    <value>Cartella inesistente:</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_CurrentVersion" xml:space="preserve">
    <value>Versione Attuale</value>
  </data>
  <data name="FileDestination_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_" xml:space="preserve">
    <value>È disponibile una nuova versione di {0}.
Vuoi scaricarla e installarla ora?</value>
  </data>
  <data name="Helpers_OpenFile_File_not_exist_" xml:space="preserve">
    <value>File inesistente:</value>
  </data>
  <data name="Helpers_BrowseFolder_Choose_folder" xml:space="preserve">
    <value>Scegli Cartella</value>
  </data>
  <data name="ExportImportControl_Deserialize_Import_failed_" xml:space="preserve">
    <value>Importazione fallita.</value>
  </data>
  <data name="Extensions_AddContextMenu_Paste" xml:space="preserve">
    <value>Incolla</value>
  </data>
  <data name="LinearGradientMode_ForwardDiagonal" xml:space="preserve">
    <value>Diagonale Avanti</value>
  </data>
  <data name="PNGBitDepth_Bit24" xml:space="preserve">
    <value>24 bit</value>
  </data>
  <data name="HotkeyType_OpenImageHistory" xml:space="preserve">
    <value>Apri Finestra Cronologia Immagini</value>
  </data>
  <data name="ReplCodeMenuCategory_Incremental" xml:space="preserve">
    <value>Incrementale</value>
  </data>
  <data name="AfterCaptureTasks_SaveThumbnailImageToFile" xml:space="preserve">
    <value>Salva Miniatura Immagine</value>
  </data>
  <data name="DownloaderForm_StartDownload_Downloading_" xml:space="preserve">
    <value>Scaricamento.</value>
  </data>
  <data name="RegionCaptureAction_RemoveShapeCancelCapture" xml:space="preserve">
    <value>Rimuovi Forma o Annulla Cattura</value>
  </data>
  <data name="ReplCodeMenuEntry_un_User_name" xml:space="preserve">
    <value>Nome Utente</value>
  </data>
  <data name="CodeMenu_Create_Close" xml:space="preserve">
    <value>Chiudi</value>
  </data>
  <data name="HotkeyType_QRCode" xml:space="preserve">
    <value>Codice QR</value>
  </data>
  <data name="PastebinExpiration_W1" xml:space="preserve">
    <value>1 Settimana</value>
  </data>
  <data name="CustomUploaderDestinationType_URLSharingService" xml:space="preserve">
    <value>Servizio Condivisione URL</value>
  </data>
  <data name="ShapeType_EffectHighlight" xml:space="preserve">
    <value>Effetto: Evidenzia</value>
  </data>
  <data name="GIFQuality_Grayscale" xml:space="preserve">
    <value>Paletta di Quantificazione in Scala di Grigi 256 Colori</value>
  </data>
  <data name="GIFQuality_Default" xml:space="preserve">
    <value>Codifica .NET Predefinita (Veloce - Qualità Media)</value>
  </data>
  <data name="ReplCodeMenuEntry_rx_Random_hexadecimal" xml:space="preserve">
    <value>Carattere Esadecimale Casuale. Ripeti usando {n}</value>
  </data>
  <data name="PastebinPrivacy_Private" xml:space="preserve">
    <value>Privato (solo membri)</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Errore</value>
  </data>
  <data name="URLSharingServices_CustomURLSharingService" xml:space="preserve">
    <value>Servizio Condivisione URL Personalizzato</value>
  </data>
  <data name="RegionCaptureAction_CaptureFullscreen" xml:space="preserve">
    <value>Cattura Schermo intero</value>
  </data>
  <data name="ReplCodeMenuEntry_pn_Process_name_of_active_window" xml:space="preserve">
    <value>Nome del Processo della Finestra</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Waiting_" xml:space="preserve">
    <value>Attendere.</value>
  </data>
  <data name="HotkeyType_ImageEditor" xml:space="preserve">
    <value>Editor Immagini</value>
  </data>
  <data name="URLSharingServices_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="HotkeyType_OpenHistory" xml:space="preserve">
    <value>Apri Finestra Cronologia</value>
  </data>
  <data name="ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric" xml:space="preserve">
    <value>Auto Incremento Alfanumerico in Base {n} (1 &lt; n &lt; 63)</value>
  </data>
  <data name="HotkeyType_CaptureWebpage" xml:space="preserve">
    <value>Cattura Pagina Web</value>
  </data>
  <data name="RegionCaptureAction_CancelCapture" xml:space="preserve">
    <value>Annulla Cattura</value>
  </data>
  <data name="HotkeyType_RectangleLight" xml:space="preserve">
    <value>Cattura Regione (Semplice)</value>
  </data>
  <data name="ProxyMethod_Automatic" xml:space="preserve">
    <value>Automatico</value>
  </data>
  <data name="HotkeyType_FileUpload" xml:space="preserve">
    <value>Carica File</value>
  </data>
  <data name="ReplCodeMenuEntry_guid_Random_guid" xml:space="preserve">
    <value>GUID Casuale</value>
  </data>
  <data name="ShapeType_DrawingLine" xml:space="preserve">
    <value>Disegna: Linea</value>
  </data>
  <data name="AfterCaptureTasks_ShowBeforeUploadWindow" xml:space="preserve">
    <value>Mostra Finestra "Prima del Caricamento"</value>
  </data>
  <data name="AfterCaptureTasks_ShowInExplorer" xml:space="preserve">
    <value>Mostra File in Explorer</value>
  </data>
  <data name="ImageDestination_CustomImageUploader" xml:space="preserve">
    <value>Servizio Caricamento Immagini Personalizzato</value>
  </data>
  <data name="HotkeyType_Category_ScreenCapture" xml:space="preserve">
    <value>Cattura Schermo</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="HotkeyType_ActiveWindow" xml:space="preserve">
    <value>Cattura Finestra Attiva</value>
  </data>
  <data name="ShapeType_DrawingStep" xml:space="preserve">
    <value>Disegna: Punti</value>
  </data>
  <data name="ReplCodeMenuEntry_i_Auto_increment_number" xml:space="preserve">
    <value>Auto Incremento Numerico. Sarà aggiunto uno 0 davanti usando {n}</value>
  </data>
  <data name="HotkeyType_ClipboardUpload" xml:space="preserve">
    <value>Carica dagli Appunti</value>
  </data>
  <data name="ReplCodeMenuEntry_n_New_line" xml:space="preserve">
    <value>Nuova Linea</value>
  </data>
  <data name="ReplCodeMenuEntry_mon2_Current_month_name__English_" xml:space="preserve">
    <value>Nome Mese Corrente (Inglese)</value>
  </data>
  <data name="HotkeyType_OpenScreenshotsFolder" xml:space="preserve">
    <value>Apri Cartella Screenshot</value>
  </data>
  <data name="ReplCodeMenuEntry_width_Gets_image_width" xml:space="preserve">
    <value>Larghezza Immagne</value>
  </data>
  <data name="ReplCodeMenuEntry_w2_Current_week_name__English_" xml:space="preserve">
    <value>Nome Giorno della Settimana Corrente (Inglese)</value>
  </data>
  <data name="ExeFileNameEditor_EditValue_Browse_for_executable___" xml:space="preserve">
    <value>Cerca Eseguibile...</value>
  </data>
  <data name="ImageDestination_FileUploader" xml:space="preserve">
    <value>Servizio Caricamento File</value>
  </data>
  <data name="HotkeyType_None" xml:space="preserve">
    <value>Nessuna</value>
  </data>
  <data name="PNGBitDepth_Default" xml:space="preserve">
    <value>Predefinita</value>
  </data>
  <data name="Helpers_CreateDirectoryIfNotExist_Create_failed_" xml:space="preserve">
    <value>Impossibile creare la cartella.</value>
  </data>
  <data name="ProxyMethod_Manual" xml:space="preserve">
    <value>Manuale</value>
  </data>
  <data name="DownloaderForm_ChangeStatus_Status___0_" xml:space="preserve">
    <value>Stato: {0}</value>
  </data>
  <data name="HotkeyType_StartScreenRecorderGIF" xml:space="preserve">
    <value>Avvia/Ferma Registrazione Schermo (GIF) usando l'Ultima Regione</value>
  </data>
  <data name="ImgurThumbnailType_Small_Square" xml:space="preserve">
    <value>Quadrato Piccolo</value>
  </data>
  <data name="HotkeyType_MonitorTest" xml:space="preserve">
    <value>Prova Monitor</value>
  </data>
  <data name="Extensions_AddContextMenu_Copy" xml:space="preserve">
    <value>Copia</value>
  </data>
  <data name="AfterUploadTasks_UseURLShortener" xml:space="preserve">
    <value>Accorcia URL</value>
  </data>
  <data name="DownloaderForm_StartDownload_Cancel" xml:space="preserve">
    <value>Annulla</value>
  </data>
  <data name="HotkeyType_Category_Tools" xml:space="preserve">
    <value>Strumenti</value>
  </data>
  <data name="FileDestination_SharedFolder" xml:space="preserve">
    <value>Cartella Condivisa</value>
  </data>
  <data name="HotkeyType_ActiveMonitor" xml:space="preserve">
    <value>Cattura Monitor Attivo</value>
  </data>
  <data name="DownloaderForm_StartDownload_Getting_file_size_" xml:space="preserve">
    <value>Ottenimento dimensione file.</value>
  </data>
  <data name="HotkeyType_Category_Other" xml:space="preserve">
    <value>Altro</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Filename___0_" xml:space="preserve">
    <value>Nome File: {0}</value>
  </data>
  <data name="ShapeType_DrawingEllipse" xml:space="preserve">
    <value>Disegna: Ellisse</value>
  </data>
  <data name="HotkeyType_ColorPicker" xml:space="preserve">
    <value>Selezione Colore</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>Ferma</value>
  </data>
  <data name="TextDestination_FileUploader" xml:space="preserve">
    <value>Servizio Caricamento File</value>
  </data>
  <data name="MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__" xml:space="preserve">
    <value>Caricamento immagine: {0}%</value>
  </data>
  <data name="ReplCodeMenuEntry_ra_Random_alphanumeric_char" xml:space="preserve">
    <value>Carattere Alfanumerico Casuale. Ripeti usando {n}</value>
  </data>
  <data name="HotkeyType_DisableHotkeys" xml:space="preserve">
    <value>Attiva/Disattiva Scorciatoie</value>
  </data>
  <data name="RegionCaptureAction_None" xml:space="preserve">
    <value>Niente</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFileWithDialog" xml:space="preserve">
    <value>Salva Immagine come...</value>
  </data>
  <data name="RegionCaptureAction_RemoveShape" xml:space="preserve">
    <value>Rimuovi Forma</value>
  </data>
  <data name="ActionsCodeMenuEntry_FilePath_File_path" xml:space="preserve">
    <value>Percorso File</value>
  </data>
  <data name="SupportedLanguage_Automatic" xml:space="preserve">
    <value>Automatica</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Cancel" xml:space="preserve">
    <value>Annulla</value>
  </data>
  <data name="FolderSelectDialog_Title_Select_a_folder" xml:space="preserve">
    <value>Seleziona una Cartella</value>
  </data>
  <data name="HotkeyType_OpenMainWindow" xml:space="preserve">
    <value>Apri Finestra Principale</value>
  </data>
  <data name="HotkeyType_ScreenColorPicker" xml:space="preserve">
    <value>Cattura Colore</value>
  </data>
  <data name="PrintTextForm_LoadSettings_Name___0___Size___1_" xml:space="preserve">
    <value>Nome: {0}, Dimensione: {1}</value>
  </data>
  <data name="HotkeyType_AutoCapture" xml:space="preserve">
    <value>Cattura Automatica</value>
  </data>
  <data name="ShapeType_DrawingRectangle" xml:space="preserve">
    <value>Disegna: Rettangolo</value>
  </data>
  <data name="HotkeyType_ScreenRecorderCustomRegion" xml:space="preserve">
    <value>Avvia/Ferma Registrazione Schermo usando la Regione Pre Configurata</value>
  </data>
  <data name="ScreenRecordGIFEncoding_OctreeQuantizer" xml:space="preserve">
    <value>Quantizzatore Octree (Qualità Media)</value>
  </data>
  <data name="Helpers_BrowseFile_Choose_file" xml:space="preserve">
    <value>Scegli File</value>
  </data>
  <data name="ReplCodeMenuEntry_height_Gets_image_height" xml:space="preserve">
    <value>Altezza Immagne</value>
  </data>
  <data name="PastebinExpiration_M1" xml:space="preserve">
    <value>1 Mese</value>
  </data>
  <data name="ShapeType_DrawingTextBackground" xml:space="preserve">
    <value>Disegna: Testo (Sfondo)</value>
  </data>
  <data name="UrlShortenerType_CustomURLShortener" xml:space="preserve">
    <value>Servizio Accorciamento URL Personalizzato</value>
  </data>
  <data name="PastebinPrivacy_Public" xml:space="preserve">
    <value>Pubblico</value>
  </data>
  <data name="FileExistAction_Overwrite" xml:space="preserve">
    <value>Sovrascrivi File</value>
  </data>
  <data name="HotkeyType_ShortenURL" xml:space="preserve">
    <value>Accorcia URL</value>
  </data>
  <data name="CustomUploaderDestinationType_TextUploader" xml:space="preserve">
    <value>Servizio Caricamento Testo</value>
  </data>
  <data name="FileExistAction_Ask" xml:space="preserve">
    <value>Chiedi cosa fare</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_LatestVersion" xml:space="preserve">
    <value>Ultima Versione</value>
  </data>
  <data name="AfterCaptureTasks_ShowAfterCaptureWindow" xml:space="preserve">
    <value>Mostra Finestra "Dopo la Cattura"</value>
  </data>
  <data name="HotkeyType_UploadText" xml:space="preserve">
    <value>Carica Testo</value>
  </data>
  <data name="HotkeyType_UploadURL" xml:space="preserve">
    <value>Carica da URL</value>
  </data>
  <data name="AfterUploadTasks_CopyURLToClipboard" xml:space="preserve">
    <value>Copia URL negli Appunti</value>
  </data>
  <data name="ReplCodeMenuEntry_cn_Computer_name" xml:space="preserve">
    <value>Nome Computer</value>
  </data>
  <data name="HotkeyType_StartAutoCapture" xml:space="preserve">
    <value>Avvia Cattura Automatica usando l'Ultima Regione</value>
  </data>
  <data name="ImgurThumbnailType_Medium_Thumbnail" xml:space="preserve">
    <value>Anteprima Media</value>
  </data>
  <data name="AfterCaptureTasks_CopyFileToClipboard" xml:space="preserve">
    <value>Copia File negli Appunti</value>
  </data>
  <data name="HotkeyType_Metadata" xml:space="preserve">
    <value />
  </data>
</root>
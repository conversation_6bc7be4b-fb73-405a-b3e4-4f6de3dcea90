﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="btnFilePathBrowse.Text" xml:space="preserve">
    <value>Parcourir...</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Vérifier un hash</value>
  </data>
  <data name="lblHashType.Text" xml:space="preserve">
    <value>Type de hash :</value>
  </data>
  <data name="btnStartHashCheck.Text" xml:space="preserve">
    <value>Vérifier</value>
  </data>
  <data name="lblResult.Text" xml:space="preserve">
    <value>Résultat :</value>
  </data>
  <data name="lblTarget.Text" xml:space="preserve">
    <value>Cible :</value>
  </data>
  <data name="btnHashCheckCopyAll.Text" xml:space="preserve">
    <value>Copier tout</value>
  </data>
  <data name="lblHashCheckBase64.Text" xml:space="preserve">
    <value>Codage base64 :</value>
  </data>
  <data name="lblHashCheckHex.Text" xml:space="preserve">
    <value>Codage hexadécimal :</value>
  </data>
  <data name="lblHashCheckBinary.Text" xml:space="preserve">
    <value>Codage binaire :</value>
  </data>
  <data name="lblHashCheckText.Text" xml:space="preserve">
    <value>Texte :</value>
  </data>
  <data name="lblHashCheckHash.Text" xml:space="preserve">
    <value>Hash :</value>
  </data>
  <data name="btnHashCheckDecodeBase64.Text" xml:space="preserve">
    <value>Décoder</value>
  </data>
  <data name="btnHashCheckDecodeASCII.Text" xml:space="preserve">
    <value>Décoder</value>
  </data>
  <data name="btnHashCheckDecodeHex.Text" xml:space="preserve">
    <value>Décoder</value>
  </data>
  <data name="btnHashCheckDecodeBinary.Text" xml:space="preserve">
    <value>Décoder</value>
  </data>
  <data name="btnHashCheckEncodeText.Text" xml:space="preserve">
    <value>Encoder</value>
  </data>
  <data name="tpTextConversions.Text" xml:space="preserve">
    <value>Conversions de textes</value>
  </data>
  <data name="tpFileHashCheck.Text" xml:space="preserve">
    <value>Vérifier le hash d'un fichier</value>
  </data>
  <data name="lblFilePath.Text" xml:space="preserve">
    <value>Chemin du fichier :</value>
  </data>
  <data name="lblFilePath2.Text" xml:space="preserve">
    <value>Chemin du fichier :</value>
  </data>
  <data name="btnFilePathBrowse2.Text" xml:space="preserve">
    <value>Parcourir...</value>
  </data>
  <data name="lblHashCheckASCII.Text" xml:space="preserve">
    <value>ASCII :</value>
  </data>
  <data name="lblProgressPercentage.Text" xml:space="preserve">
    <value>0%</value>
  </data>
  <data name="cbCompareTwoFiles.Text" xml:space="preserve">
    <value>Comparer deux fichiers</value>
  </data>
</root>
﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtSelectedClipboardContent.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="txtSelectedClipboardContent.Location" type="System.Drawing.Point, System.Drawing">
    <value>264, 8</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtSelectedClipboardContent.Multiline" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSelectedClipboardContent.Size" type="System.Drawing.Size, System.Drawing">
    <value>512, 544</value>
  </data>
  <data name="txtSelectedClipboardContent.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;txtSelectedClipboardContent.Name" xml:space="preserve">
    <value>txtSelectedClipboardContent</value>
  </data>
  <data name="&gt;&gt;txtSelectedClipboardContent.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txtSelectedClipboardContent.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtSelectedClipboardContent.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="btnRefresh.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnRefresh.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 8</value>
  </data>
  <data name="btnRefresh.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 32</value>
  </data>
  <data name="btnRefresh.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnRefresh.Text" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="&gt;&gt;btnRefresh.Name" xml:space="preserve">
    <value>btnRefresh</value>
  </data>
  <data name="&gt;&gt;btnRefresh.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnRefresh.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnRefresh.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnClearClipboard.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnClearClipboard.Location" type="System.Drawing.Point, System.Drawing">
    <value>136, 8</value>
  </data>
  <data name="btnClearClipboard.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 32</value>
  </data>
  <data name="btnClearClipboard.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnClearClipboard.Text" xml:space="preserve">
    <value>Clear clipboard</value>
  </data>
  <data name="&gt;&gt;btnClearClipboard.Name" xml:space="preserve">
    <value>btnClearClipboard</value>
  </data>
  <data name="&gt;&gt;btnClearClipboard.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnClearClipboard.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnClearClipboard.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lvClipboardContentList.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left</value>
  </data>
  <data name="chFormat.Text" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="chFormat.Width" type="System.Int32, mscorlib">
    <value>244</value>
  </data>
  <data name="lvClipboardContentList.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 48</value>
  </data>
  <data name="lvClipboardContentList.Size" type="System.Drawing.Size, System.Drawing">
    <value>248, 504</value>
  </data>
  <data name="lvClipboardContentList.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;lvClipboardContentList.Name" xml:space="preserve">
    <value>lvClipboardContentList</value>
  </data>
  <data name="&gt;&gt;lvClipboardContentList.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lvClipboardContentList.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lvClipboardContentList.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pbSelectedClipboardContent.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="pbSelectedClipboardContent.Location" type="System.Drawing.Point, System.Drawing">
    <value>264, 8</value>
  </data>
  <data name="pbSelectedClipboardContent.Size" type="System.Drawing.Size, System.Drawing">
    <value>512, 544</value>
  </data>
  <data name="pbSelectedClipboardContent.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;pbSelectedClipboardContent.Name" xml:space="preserve">
    <value>pbSelectedClipboardContent</value>
  </data>
  <data name="&gt;&gt;pbSelectedClipboardContent.Type" xml:space="preserve">
    <value>ShareX.HelpersLib.MyPictureBox, ShareX.HelpersLib, Version=********, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;pbSelectedClipboardContent.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;pbSelectedClipboardContent.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>784, 561</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Clipboard viewer</value>
  </data>
  <data name="&gt;&gt;chFormat.Name" xml:space="preserve">
    <value>chFormat</value>
  </data>
  <data name="&gt;&gt;chFormat.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>ClipboardViewerForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
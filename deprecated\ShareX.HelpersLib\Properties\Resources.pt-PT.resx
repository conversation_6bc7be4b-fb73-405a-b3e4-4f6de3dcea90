﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ShapeType_RegionFreehand" xml:space="preserve">
    <value>Região: Escolha livre</value>
  </data>
  <data name="ReplCodeMenuEntry_w_Current_week_name__Local_language_" xml:space="preserve">
    <value>Dia da semana (linguagem local)</value>
  </data>
  <data name="ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_" xml:space="preserve">
    <value>Definições copiadas para a sua área de transferência.</value>
  </data>
  <data name="ImgurThumbnailType_Big_Square" xml:space="preserve">
    <value>Rectângulo grande</value>
  </data>
  <data name="ReplCodeMenuEntry_s_Current_second" xml:space="preserve">
    <value>Segundo actual</value>
  </data>
  <data name="TextDestination_CustomTextUploader" xml:space="preserve">
    <value>Serviço de upload de texto personalizado</value>
  </data>
  <data name="ProxyMethod_None" xml:space="preserve">
    <value>Nenhum</value>
  </data>
  <data name="ReplCodeMenuEntry_mo_Current_month" xml:space="preserve">
    <value>Mês actual</value>
  </data>
  <data name="CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___" xml:space="preserve">
    <value>Procurar um ficheiro CSS...</value>
  </data>
  <data name="Extensions_AddContextMenu_Redo" xml:space="preserve">
    <value>Refazer</value>
  </data>
  <data name="HotkeyType_VideoThumbnailer" xml:space="preserve">
    <value>Conversor de vídeo para miniatura</value>
  </data>
  <data name="ShapeType_EffectBlur" xml:space="preserve">
    <value>Efeito: nevoado</value>
  </data>
  <data name="AfterCaptureTasks_ShowQuickTaskMenu" xml:space="preserve">
    <value>Mostrar menu de tarefas rápidas</value>
  </data>
  <data name="CustomUploaderDestinationType_URLShortener" xml:space="preserve">
    <value>Encurtar hiperligação</value>
  </data>
  <data name="ReplCodeMenuEntry_uln_User_login_name" xml:space="preserve">
    <value>Utilizador</value>
  </data>
  <data name="HotkeyType_ImageEffects" xml:space="preserve">
    <value>Efeitos de imagem</value>
  </data>
  <data name="ShapeType_DrawingImageScreen" xml:space="preserve">
    <value>Imagem (Ecrã)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available" xml:space="preserve">
    <value>Uma nova versão do {0} está disponivel</value>
  </data>
  <data name="AfterUploadTasks_ShowQRCode" xml:space="preserve">
    <value>Mostrar janela de código QR</value>
  </data>
  <data name="ShapeType_DrawingSpeechBalloon" xml:space="preserve">
    <value>Desenho: Balão de voz</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date" xml:space="preserve">
    <value>{0} está actualizado</value>
  </data>
  <data name="HotkeyType_Category_ScreenRecord" xml:space="preserve">
    <value>Gravar ecrã</value>
  </data>
  <data name="HotkeyType_ScrollingCapture" xml:space="preserve">
    <value>Captura com botão do meio</value>
  </data>
  <data name="ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all" xml:space="preserve">
    <value>Incrementar automáticamente caracteres alfanuméricos maiúsculos e minúsculos. Acrescentar 0 à esquerda utilizando {n}</value>
  </data>
  <data name="ReplCodeMenuEntry_t_Title_of_active_window" xml:space="preserve">
    <value>Título da janela</value>
  </data>
  <data name="AfterCaptureTasks_SendImageToPrinter" xml:space="preserve">
    <value>Imprimir imagem</value>
  </data>
  <data name="ShapeType_RegionRectangle" xml:space="preserve">
    <value>Região: Retângulo</value>
  </data>
  <data name="HotkeyType_ToggleActionsToolbar" xml:space="preserve">
    <value>Alterna entre a barra de ferramentas de tarefas</value>
  </data>
  <data name="AfterCaptureTasks_PerformActions" xml:space="preserve">
    <value>Realizar acções</value>
  </data>
  <data name="ReplCodeMenuCategory_Date_and_Time" xml:space="preserve">
    <value>Data e hora</value>
  </data>
  <data name="HotkeyType_ImageCombiner" xml:space="preserve">
    <value>Juntar imagens</value>
  </data>
  <data name="HotkeyType_RectangleTransparent" xml:space="preserve">
    <value>Capturar região (transparente)</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_" xml:space="preserve">
    <value>Transferência concluída.</value>
  </data>
  <data name="YouTubeVideoPrivacy_Private" xml:space="preserve">
    <value>Privado</value>
  </data>
  <data name="AfterUploadTasks_ShareURL" xml:space="preserve">
    <value>Partilhar hiperligação</value>
  </data>
  <data name="CustomUploaderDestinationType_FileUploader" xml:space="preserve">
    <value>Envio de ficheiros</value>
  </data>
  <data name="ReplCodeMenuEntry_h_Current_hour" xml:space="preserve">
    <value>Hora actual</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_Update_check_failed" xml:space="preserve">
    <value>Falha ao verificar actualizações</value>
  </data>
  <data name="ReplCodeMenuEntry_ms_Current_millisecond" xml:space="preserve">
    <value>Milisegundo atual</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Install" xml:space="preserve">
    <value>Instalar</value>
  </data>
  <data name="AfterCaptureTasks_UploadImageToHost" xml:space="preserve">
    <value>Enviar imagem para hospedagem</value>
  </data>
  <data name="ReplCodeMenuEntry_ix_Auto_increment_hexadecimal" xml:space="preserve">
    <value>Incrementar automáticamente utilizando caractéres hexadecimais. Acrescentar 0 à esquerda usando {n}</value>
  </data>
  <data name="CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__" xml:space="preserve">
    <value>Ciano: {0:0.0}%, Magenta: {1:0.0}%, Amarelo: {2:0.0}%, Chave: {3:0.0}%</value>
  </data>
  <data name="HotkeyType_FolderUpload" xml:space="preserve">
    <value>Envio de pasta</value>
  </data>
  <data name="ReplCodeMenuEntry_mi_Current_minute" xml:space="preserve">
    <value>Minuto atual</value>
  </data>
  <data name="ShapeType_EffectPixelate" xml:space="preserve">
    <value>Efeito: Pixelizar</value>
  </data>
  <data name="ReplCodeMenuEntry_d_Current_day" xml:space="preserve">
    <value>Dia atual</value>
  </data>
  <data name="ShapeType_DrawingArrow" xml:space="preserve">
    <value>Desenho: Seta</value>
  </data>
  <data name="ShapeType_DrawingSmartEraser" xml:space="preserve">
    <value>Borracha inteligente</value>
  </data>
  <data name="PastebinPrivacy_Unlisted" xml:space="preserve">
    <value>Não listado</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_update_is_available" xml:space="preserve">
    <value>Actualização está disponível</value>
  </data>
  <data name="HotkeyType_Category_Upload" xml:space="preserve">
    <value>Envio</value>
  </data>
  <data name="Extensions_AddContextMenu_Cut" xml:space="preserve">
    <value>Recortar</value>
  </data>
  <data name="FileExistAction_Cancel" xml:space="preserve">
    <value>Nâo guardar</value>
  </data>
  <data name="AfterCaptureTasks_CopyImageToClipboard" xml:space="preserve">
    <value>Copiar imagem para a área de transferência</value>
  </data>
  <data name="PNGBitDepth_Bit32" xml:space="preserve">
    <value>32 bit</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFActiveWindow" xml:space="preserve">
    <value>Iniciar/Parar gravação do ecrã (GIF) utilizando a região de janela activa</value>
  </data>
  <data name="HotkeyType_PrintScreen" xml:space="preserve">
    <value>Capturar o ecrã inteiro</value>
  </data>
  <data name="ImageEditorStartMode_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFCustomRegion" xml:space="preserve">
    <value>Iniciar/Parar gravação do ecrã (GIF) utilizando uma região personalizada</value>
  </data>
  <data name="HotkeyType_CustomRegion" xml:space="preserve">
    <value>Capturar região pré-configurada</value>
  </data>
  <data name="ReplCodeMenuCategory_Image" xml:space="preserve">
    <value>Imagem</value>
  </data>
  <data name="RegionCaptureAction_SwapToolType" xml:space="preserve">
    <value>Trocar tipo de ferramenta</value>
  </data>
  <data name="HotkeyType_RectangleRegion" xml:space="preserve">
    <value>Capturar região</value>
  </data>
  <data name="AfterCaptureTasks_DoOCR" xml:space="preserve">
    <value>Reconhecer texto (OCR)</value>
  </data>
  <data name="HotkeyType_ExitShareX" xml:space="preserve">
    <value>Fechar ShareX</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_Portable" xml:space="preserve">
    <value>Uma nova versão de {0} está disponível.
Gostaria de transferí-la?</value>
  </data>
  <data name="Helpers_DownloadString_Download_failed_" xml:space="preserve">
    <value>Falha na transferência:</value>
  </data>
  <data name="ShapeType_DrawingTextOutline" xml:space="preserve">
    <value>Texto (Outline) (O)</value>
  </data>
  <data name="RegionCaptureAction_CaptureActiveMonitor" xml:space="preserve">
    <value>Capturar monitor ativo</value>
  </data>
  <data name="ImgurThumbnailType_Small_Thumbnail" xml:space="preserve">
    <value>Miniatura pequena</value>
  </data>
  <data name="PrintForm_LoadSettings_Print" xml:space="preserve">
    <value>Imprimir</value>
  </data>
  <data name="GIFQuality_Bit4" xml:space="preserve">
    <value>Octree quantizer 16 cores</value>
  </data>
  <data name="AfterUploadTasks_ShowAfterUploadWindow" xml:space="preserve">
    <value>Mostrar janela "após envio"</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAdjective" xml:space="preserve">
    <value>Adjétivo aleatório</value>
  </data>
  <data name="Extensions_AddContextMenu_SelectAll" xml:space="preserve">
    <value>Seleccionar tudo</value>
  </data>
  <data name="FileDestination_CustomFileUploader" xml:space="preserve">
    <value>Serviço de upload de ficheiro personalizado</value>
  </data>
  <data name="LinearGradientMode_Vertical" xml:space="preserve">
    <value>Vertical</value>
  </data>
  <data name="ReplCodeMenuCategory_Random" xml:space="preserve">
    <value>Aleatório</value>
  </data>
  <data name="CustomUploaderDestinationType_ImageUploader" xml:space="preserve">
    <value>Envio de imagem</value>
  </data>
  <data name="HotkeyType_HashCheck" xml:space="preserve">
    <value>Verificação de hash</value>
  </data>
  <data name="HotkeyType_ScreenRecorderActiveWindow" xml:space="preserve">
    <value>Iniciar/Parar gravação do ecrã utilizando a região de janela activa</value>
  </data>
  <data name="ReplCodeMenuEntry_rn_Random_number_0_to_9" xml:space="preserve">
    <value>Número aleatório de 0 a 9. Repetir utilizando {n}</value>
  </data>
  <data name="HotkeyType_ClipboardUploadWithContentViewer" xml:space="preserve">
    <value>Enviar da área de transferências com o visualizador de conteúdo</value>
  </data>
  <data name="YouTubeVideoPrivacy_Public" xml:space="preserve">
    <value>Público</value>
  </data>
  <data name="HSB_ToString_" xml:space="preserve">
    <value>Matiz: {0:0.0}°, Saturação: {1:0.0}%, Brilho: {2:0.0}%</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="HotkeyType_DragDropUpload" xml:space="preserve">
    <value>Envio de arrastar e largar</value>
  </data>
  <data name="PastebinExpiration_N" xml:space="preserve">
    <value>Nunca</value>
  </data>
  <data name="HotkeyType_StartScreenRecorder" xml:space="preserve">
    <value>Iniciar/Parar gravação de ecrã utilizando a última região definida</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Yes" xml:space="preserve">
    <value>Sim</value>
  </data>
  <data name="HotkeyType_ImageThumbnailer" xml:space="preserve">
    <value>Conversor de imagem para miniatura</value>
  </data>
  <data name="ReplCodeMenuEntry_mon_Current_month_name__Local_language_" xml:space="preserve">
    <value>Mês atual (linguagem local)</value>
  </data>
  <data name="GIFQuality_Bit8" xml:space="preserve">
    <value>Octree quantizer 256 cores (mais lento mas com qualidade melhor)</value>
  </data>
  <data name="ShapeType_DrawingImage" xml:space="preserve">
    <value>Desenho: Imagem</value>
  </data>
  <data name="ScreenRecordGIFEncoding_NET" xml:space="preserve">
    <value>.NET (qualidade má)</value>
  </data>
  <data name="ReplCodeMenuEntry_ia_Auto_increment_alphanumeric" xml:space="preserve">
    <value>Auto incrementar caracteres alfanuméricos maiúsculos e minúsculos. Acrescentar 0 à esquerda usando {n}</value>
  </data>
  <data name="AfterCaptureTasks_AddImageEffects" xml:space="preserve">
    <value>Adicionar efeitos de imagem / marca d'água</value>
  </data>
  <data name="AfterCaptureTasks_DeleteFile" xml:space="preserve">
    <value>Apagar ficheiro local</value>
  </data>
  <data name="ExportImportControl_Serialize_Export_failed_" xml:space="preserve">
    <value>Erro na exportação.</value>
  </data>
  <data name="ReplCodeMenuCategory_Computer" xml:space="preserve">
    <value>Computador</value>
  </data>
  <data name="FileExistAction_UniqueName" xml:space="preserve">
    <value>Acrescentar número ao nome do ficheiro</value>
  </data>
  <data name="ImgurThumbnailType_Large_Thumbnail" xml:space="preserve">
    <value>Miniatura grande</value>
  </data>
  <data name="ReplCodeMenuEntry_yy_Current_year__2_digits_" xml:space="preserve">
    <value>Ano atual (2 digitos)</value>
  </data>
  <data name="PNGBitDepth_Automatic" xml:space="preserve">
    <value>Detectar automáticamente</value>
  </data>
  <data name="ImageEditorStartMode_PreviousState" xml:space="preserve">
    <value>Estado anterior</value>
  </data>
  <data name="ShapeType_RegionEllipse" xml:space="preserve">
    <value>Região: Elipse</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIF" xml:space="preserve">
    <value>Iniciar/Parar gravação do ecrã (GIF) utilizando uma região personalizada</value>
  </data>
  <data name="YouTubeVideoPrivacy_Unlisted" xml:space="preserve">
    <value>Não listado</value>
  </data>
  <data name="ObjectListView_ObjectListView_Name" xml:space="preserve">
    <value>Nome</value>
  </data>
  <data name="HotkeyType_Ruler" xml:space="preserve">
    <value>Régua</value>
  </data>
  <data name="ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from" xml:space="preserve">
    <value>Hiperligação para transferir definições de</value>
  </data>
  <data name="ShapeType_DrawingFreehand" xml:space="preserve">
    <value>Desenho: Escolha livre</value>
  </data>
  <data name="ReplCodeMenuEntry_pm_Gets_AM_PM" xml:space="preserve">
    <value>Recebe AM/PM</value>
  </data>
  <data name="DirectoryNameEditor_EditValue_Browse_for_a_folder___" xml:space="preserve">
    <value>Procurar uma pasta...</value>
  </data>
  <data name="LinearGradientMode_BackwardDiagonal" xml:space="preserve">
    <value>Diagonal para trás</value>
  </data>
  <data name="ShapeType_DrawingCursor" xml:space="preserve">
    <value>Cursor</value>
  </data>
  <data name="ImgurThumbnailType_Huge_Thumbnail" xml:space="preserve">
    <value>Miniatura enorme</value>
  </data>
  <data name="LinearGradientMode_Horizontal" xml:space="preserve">
    <value>Horizontal</value>
  </data>
  <data name="HotkeyType_AbortScreenRecording" xml:space="preserve">
    <value>Cancelar captura de ecrã</value>
  </data>
  <data name="ReplCodeMenuEntry_y_Current_year" xml:space="preserve">
    <value>Ano atual</value>
  </data>
  <data name="ImageEditorStartMode_Fullscreen" xml:space="preserve">
    <value>Ecrã inteiro</value>
  </data>
  <data name="AfterCaptureTasks_CopyFilePathToClipboard" xml:space="preserve">
    <value>Copiar caminho do ficheiro para área de transferência</value>
  </data>
  <data name="HotkeyType_ScreenRecorder" xml:space="preserve">
    <value>Iniciar/Parar gravação do ecrã (GIF) utilizando uma região personalizada</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFile" xml:space="preserve">
    <value>Guardar imagem para ficheiro</value>
  </data>
  <data name="ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_" xml:space="preserve">
    <value>Caminho do ficheiro sem extensão + "Output file name extension"</value>
  </data>
  <data name="URLSharingServices_GoogleImageSearch" xml:space="preserve">
    <value>Pesquiza de imagem do Google</value>
  </data>
  <data name="HotkeyType_IndexFolder" xml:space="preserve">
    <value>Indexador de pastas</value>
  </data>
  <data name="ReplCodeMenuEntry_unix_Unix_timestamp" xml:space="preserve">
    <value>Horário UNIX</value>
  </data>
  <data name="ScreenRecordGIFEncoding_FFmpeg" xml:space="preserve">
    <value>FFmpeg (qualidade boa)</value>
  </data>
  <data name="HotkeyType_TweetMessage" xml:space="preserve">
    <value>Enviar mensagem no Tweet</value>
  </data>
  <data name="HotkeyType_StopUploads" xml:space="preserve">
    <value>Parar todos os envios ativos</value>
  </data>
  <data name="AfterUploadTasks_OpenURL" xml:space="preserve">
    <value>Abrir hiperligação</value>
  </data>
  <data name="AfterCaptureTasks_AnnotateImage" xml:space="preserve">
    <value>Abrir imagem no editor</value>
  </data>
  <data name="MyPictureBox_LoadImageAsync_Loading_image___" xml:space="preserve">
    <value>Carregando imagem...</value>
  </data>
  <data name="HotkeyType_LastRegion" xml:space="preserve">
    <value>Capturar última região</value>
  </data>
  <data name="Helpers_OpenFolder_Folder_not_exist_" xml:space="preserve">
    <value>Pasta não existe:</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_CurrentVersion" xml:space="preserve">
    <value>Versão actual</value>
  </data>
  <data name="FileDestination_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_" xml:space="preserve">
    <value>Uma nova versão do {0} está disponível.
Você gostaria de transferi-la e instalá-la?</value>
  </data>
  <data name="WavFileNameEditor_EditValue_Browse_for_a_sound_file___" xml:space="preserve">
    <value>Procure por um ficheiro de som...</value>
  </data>
  <data name="Helpers_OpenFile_File_not_exist_" xml:space="preserve">
    <value>O ficheiro não existe:</value>
  </data>
  <data name="Helpers_BrowseFolder_Choose_folder" xml:space="preserve">
    <value>Escolher caminho</value>
  </data>
  <data name="ExportImportControl_Deserialize_Import_failed_" xml:space="preserve">
    <value>Erro na importação.</value>
  </data>
  <data name="Extensions_AddContextMenu_Delete" xml:space="preserve">
    <value>Apagar</value>
  </data>
  <data name="Extensions_AddContextMenu_Paste" xml:space="preserve">
    <value>Colar</value>
  </data>
  <data name="LinearGradientMode_ForwardDiagonal" xml:space="preserve">
    <value>Diagonal para a frente</value>
  </data>
  <data name="PNGBitDepth_Bit24" xml:space="preserve">
    <value>24 bit</value>
  </data>
  <data name="ReplCodeMenuEntry_wy_Week_of_year" xml:space="preserve">
    <value>Semana atual</value>
  </data>
  <data name="HotkeyType_OpenImageHistory" xml:space="preserve">
    <value>Abrir janela de histórico de imagens</value>
  </data>
  <data name="ReplCodeMenuCategory_Incremental" xml:space="preserve">
    <value>Incremental</value>
  </data>
  <data name="RandomEmojiRepeatUsingN" xml:space="preserve">
    <value>emoji aleatório. Repetir utilizando {n}</value>
  </data>
  <data name="AfterCaptureTasks_SaveThumbnailImageToFile" xml:space="preserve">
    <value>Guardar miniatura para ficheiro</value>
  </data>
  <data name="DownloaderForm_StartDownload_Downloading_" xml:space="preserve">
    <value>Transferindo.</value>
  </data>
  <data name="RegionCaptureAction_RemoveShapeCancelCapture" xml:space="preserve">
    <value>Remover forma ou cancelar captura</value>
  </data>
  <data name="ReplCodeMenuEntry_un_User_name" xml:space="preserve">
    <value>Nome de utilizador</value>
  </data>
  <data name="ShapeType_DrawingMagnify" xml:space="preserve">
    <value>Magnificar</value>
  </data>
  <data name="CodeMenu_Create_Close" xml:space="preserve">
    <value>Fechar</value>
  </data>
  <data name="ShapeType_DrawingSticker" xml:space="preserve">
    <value>Rótulo</value>
  </data>
  <data name="HotkeyType_QRCode" xml:space="preserve">
    <value>Código QR</value>
  </data>
  <data name="CustomUploaderDestinationType_URLSharingService" xml:space="preserve">
    <value>Serviço de partilha de hiperligações</value>
  </data>
  <data name="ShapeType_EffectHighlight" xml:space="preserve">
    <value>Efeito: Realçar</value>
  </data>
  <data name="GIFQuality_Grayscale" xml:space="preserve">
    <value>Palete de tons de cinza quantizer 256 cores</value>
  </data>
  <data name="GIFQuality_Default" xml:space="preserve">
    <value>Codificação padrão .NET (rápida mas com qualidade média)</value>
  </data>
  <data name="ReplCodeMenuEntry_rx_Random_hexadecimal" xml:space="preserve">
    <value>Caractére hexadecimal aleatório. Repetir usando {n}</value>
  </data>
  <data name="PastebinPrivacy_Private" xml:space="preserve">
    <value>Privado (apenas membros)</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Erro</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAnimal" xml:space="preserve">
    <value>Animal aleatório</value>
  </data>
  <data name="URLSharingServices_CustomURLSharingService" xml:space="preserve">
    <value>Serviço de partilha de hiperligações personalizado</value>
  </data>
  <data name="RegionCaptureAction_CaptureFullscreen" xml:space="preserve">
    <value>Capturar ecrã inteiro</value>
  </data>
  <data name="ReplCodeMenuEntry_pn_Process_name_of_active_window" xml:space="preserve">
    <value>Nome do processo da janela ativa</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Waiting_" xml:space="preserve">
    <value>Em espera.</value>
  </data>
  <data name="HotkeyType_ImageEditor" xml:space="preserve">
    <value>Editor de imagem</value>
  </data>
  <data name="URLSharingServices_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="HotkeyType_OpenHistory" xml:space="preserve">
    <value>Abrir janela de histórico</value>
  </data>
  <data name="ShapeType_ToolSelect" xml:space="preserve">
    <value>Seleccione e mova (M)</value>
  </data>
  <data name="ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric" xml:space="preserve">
    <value>Incrementar automáticamente por base {n} utilizando caracteres alfanuméricos (1 &lt; n &lt; 63)</value>
  </data>
  <data name="HotkeyType_CaptureWebpage" xml:space="preserve">
    <value>Capturar página da web</value>
  </data>
  <data name="RegionCaptureAction_CancelCapture" xml:space="preserve">
    <value>Cancelar captura</value>
  </data>
  <data name="AfterCaptureTasks_ScanQRCode" xml:space="preserve">
    <value>Analisar código QR</value>
  </data>
  <data name="HotkeyType_RectangleLight" xml:space="preserve">
    <value>Capturar região (leve)</value>
  </data>
  <data name="ProxyMethod_Automatic" xml:space="preserve">
    <value>Automático</value>
  </data>
  <data name="HotkeyType_FileUpload" xml:space="preserve">
    <value>Envio de ficheiro</value>
  </data>
  <data name="ReplCodeMenuEntry_guid_Random_guid" xml:space="preserve">
    <value>GUID Aleatório</value>
  </data>
  <data name="ShapeType_DrawingLine" xml:space="preserve">
    <value>Desenho: Linha</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_value" xml:space="preserve">
    <value>Copiar valor</value>
  </data>
  <data name="AfterCaptureTasks_ShowBeforeUploadWindow" xml:space="preserve">
    <value>Mostrar janela "antes do envio"</value>
  </data>
  <data name="AfterCaptureTasks_ShowInExplorer" xml:space="preserve">
    <value>Mostrar ficheiro no explorador do Windows</value>
  </data>
  <data name="ImageDestination_CustomImageUploader" xml:space="preserve">
    <value>Serviço de envio de imagem personalizado</value>
  </data>
  <data name="HotkeyType_Category_ScreenCapture" xml:space="preserve">
    <value>Capturar ecrã</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_No" xml:space="preserve">
    <value>Não</value>
  </data>
  <data name="HotkeyType_ActiveWindow" xml:space="preserve">
    <value>Capturar janela ativa</value>
  </data>
  <data name="ShapeType_DrawingStep" xml:space="preserve">
    <value>Desenho: Passo</value>
  </data>
  <data name="ReplCodeMenuEntry_i_Auto_increment_number" xml:space="preserve">
    <value>Incrementar automáticamente número. Acrescentar 0 à esquerda usando {n}</value>
  </data>
  <data name="HotkeyType_ClipboardUpload" xml:space="preserve">
    <value>Envio através da área de transferência</value>
  </data>
  <data name="ReplCodeMenuEntry_n_New_line" xml:space="preserve">
    <value>Novo parágrafo</value>
  </data>
  <data name="ReplCodeMenuEntry_mon2_Current_month_name__English_" xml:space="preserve">
    <value>Mês atual (inglês)</value>
  </data>
  <data name="HotkeyType_OpenScreenshotsFolder" xml:space="preserve">
    <value>Abrir pasta de imagens</value>
  </data>
  <data name="ReplCodeMenuEntry_width_Gets_image_width" xml:space="preserve">
    <value>Largura da imagem</value>
  </data>
  <data name="ReplCodeMenuEntry_w2_Current_week_name__English_" xml:space="preserve">
    <value>Dia da semana (inglês)</value>
  </data>
  <data name="ExeFileNameEditor_EditValue_Browse_for_executable___" xml:space="preserve">
    <value>Procurar por ficheiro executável...</value>
  </data>
  <data name="ImageDestination_FileUploader" xml:space="preserve">
    <value>Envio de ficheiros</value>
  </data>
  <data name="ImageEditorStartMode_AutoSize" xml:space="preserve">
    <value>Redimensionamento automático</value>
  </data>
  <data name="HotkeyType_None" xml:space="preserve">
    <value>Nenhum</value>
  </data>
  <data name="PNGBitDepth_Default" xml:space="preserve">
    <value>Padrão</value>
  </data>
  <data name="Helpers_CreateDirectoryIfNotExist_Create_failed_" xml:space="preserve">
    <value>Não foi possível criar o caminho da pasta.</value>
  </data>
  <data name="ProxyMethod_Manual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="DownloaderForm_ChangeStatus_Status___0_" xml:space="preserve">
    <value>Estado: {0}</value>
  </data>
  <data name="HotkeyType_StartScreenRecorderGIF" xml:space="preserve">
    <value>Iniciar/Parar gravação de ecrã (GIF) utilizando última região</value>
  </data>
  <data name="ImgurThumbnailType_Small_Square" xml:space="preserve">
    <value>Quadrado pequeno</value>
  </data>
  <data name="HotkeyType_MonitorTest" xml:space="preserve">
    <value>Teste de monitor</value>
  </data>
  <data name="Extensions_AddContextMenu_Copy" xml:space="preserve">
    <value>Copiar</value>
  </data>
  <data name="AfterUploadTasks_UseURLShortener" xml:space="preserve">
    <value>Encurtar URL</value>
  </data>
  <data name="ReplCodeMenuEntry_rf_Random_line_from_file" xml:space="preserve">
    <value>Linha aleatória de um ficheiro. Utilizar {filepath} para obter o ficheiro</value>
  </data>
  <data name="DownloaderForm_StartDownload_Cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="HotkeyType_Category_Tools" xml:space="preserve">
    <value>Ferramentas</value>
  </data>
  <data name="FileDestination_SharedFolder" xml:space="preserve">
    <value>Pasta partilhada</value>
  </data>
  <data name="HotkeyType_ActiveMonitor" xml:space="preserve">
    <value>Capturar monitor ativo</value>
  </data>
  <data name="DownloaderForm_StartDownload_Getting_file_size_" xml:space="preserve">
    <value>A obter tamanho do ficheiro.</value>
  </data>
  <data name="HotkeyType_Category_Other" xml:space="preserve">
    <value>Outro</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Filename___0_" xml:space="preserve">
    <value>Nome do ficheiro: {0}</value>
  </data>
  <data name="ShapeType_DrawingEllipse" xml:space="preserve">
    <value>Desenho: Elipse</value>
  </data>
  <data name="HotkeyType_ColorPicker" xml:space="preserve">
    <value>Selecção de cores</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>Parar</value>
  </data>
  <data name="TextDestination_FileUploader" xml:space="preserve">
    <value>Envio de ficheiros</value>
  </data>
  <data name="MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__" xml:space="preserve">
    <value>Carregando imagem: {0}%</value>
  </data>
  <data name="ReplCodeMenuEntry_ra_Random_alphanumeric_char" xml:space="preserve">
    <value>Caractére alfanumérico aleatório. Repetir utilizando {n}</value>
  </data>
  <data name="ObjectListView_ObjectListView_Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="HotkeyType_DisableHotkeys" xml:space="preserve">
    <value>Desactivar/Activar teclas de atalho</value>
  </data>
  <data name="RegionCaptureAction_None" xml:space="preserve">
    <value>Não fazer nada</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFileWithDialog" xml:space="preserve">
    <value>Guardar imagem como...</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_name" xml:space="preserve">
    <value>Copiar nome</value>
  </data>
  <data name="RegionCaptureAction_RemoveShape" xml:space="preserve">
    <value>Remover forma</value>
  </data>
  <data name="ActionsCodeMenuEntry_FilePath_File_path" xml:space="preserve">
    <value>Caminho do ficheiro</value>
  </data>
  <data name="SupportedLanguage_Automatic" xml:space="preserve">
    <value>Automático</value>
  </data>
  <data name="HotkeyType_VideoConverter" xml:space="preserve">
    <value>Conversor de vídeo</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="FolderSelectDialog_Title_Select_a_folder" xml:space="preserve">
    <value>Seleccionar um caminho</value>
  </data>
  <data name="HotkeyType_OpenMainWindow" xml:space="preserve">
    <value>Abrir janela principal</value>
  </data>
  <data name="HotkeyType_ScreenColorPicker" xml:space="preserve">
    <value>Seleção de cores da tela</value>
  </data>
  <data name="PrintTextForm_LoadSettings_Name___0___Size___1_" xml:space="preserve">
    <value>Nome: {0}, Tamanho: {1}</value>
  </data>
  <data name="HotkeyType_AutoCapture" xml:space="preserve">
    <value>Capturar automáticamente</value>
  </data>
  <data name="ShapeType_DrawingRectangle" xml:space="preserve">
    <value>Desenho: Rectângulo</value>
  </data>
  <data name="ImageEditorStartMode_Maximized" xml:space="preserve">
    <value>Maximizado</value>
  </data>
  <data name="HotkeyType_ScreenRecorderCustomRegion" xml:space="preserve">
    <value>Iniciar/Parar gravação de ecrã utilizando uma região personalizada</value>
  </data>
  <data name="ScreenRecordGIFEncoding_OctreeQuantizer" xml:space="preserve">
    <value>Octree quantizer (qualidade média)</value>
  </data>
  <data name="Helpers_BrowseFile_Choose_file" xml:space="preserve">
    <value>Escolher ficheiro</value>
  </data>
  <data name="ReplCodeMenuEntry_height_Gets_image_height" xml:space="preserve">
    <value>Altura da imagem</value>
  </data>
  <data name="ShapeType_DrawingTextBackground" xml:space="preserve">
    <value>Texto (Fundo) (T)</value>
  </data>
  <data name="RandomNonAmbiguousAlphanumericCharRepeatUsingN" xml:space="preserve">
    <value>Aleatório alfanumérico não ambíguo. Repetir utilizando {n}</value>
  </data>
  <data name="UrlShortenerType_CustomURLShortener" xml:space="preserve">
    <value>Encurtar hiperligações personalizadas</value>
  </data>
  <data name="PastebinPrivacy_Public" xml:space="preserve">
    <value>Público</value>
  </data>
  <data name="FileExistAction_Overwrite" xml:space="preserve">
    <value>Substituir ficheiro</value>
  </data>
  <data name="HotkeyType_ShortenURL" xml:space="preserve">
    <value>Encurtar hiperligação</value>
  </data>
  <data name="CustomUploaderDestinationType_TextUploader" xml:space="preserve">
    <value>Hospedagem de Texto</value>
  </data>
  <data name="FileExistAction_Ask" xml:space="preserve">
    <value>Perguntar o que fazer</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_LatestVersion" xml:space="preserve">
    <value>Última versão</value>
  </data>
  <data name="AfterCaptureTasks_ShowAfterCaptureWindow" xml:space="preserve">
    <value>Mostrar janela "após captura"</value>
  </data>
  <data name="HotkeyType_UploadText" xml:space="preserve">
    <value>Enviar texto</value>
  </data>
  <data name="ShapeType_ToolCrop" xml:space="preserve">
    <value>Recortar imagem (C)</value>
  </data>
  <data name="HotkeyType_UploadURL" xml:space="preserve">
    <value>Enviar através de hiperligação</value>
  </data>
  <data name="HotkeyType_ImageSplitter" xml:space="preserve">
    <value>Divisor de imagem</value>
  </data>
  <data name="AfterUploadTasks_CopyURLToClipboard" xml:space="preserve">
    <value>Copiar hiperligação para área de transferência</value>
  </data>
  <data name="ReplCodeMenuEntry_cn_Computer_name" xml:space="preserve">
    <value>Nome do computador</value>
  </data>
  <data name="HotkeyType_StartAutoCapture" xml:space="preserve">
    <value>Iniciar captura automática na última região utilizada</value>
  </data>
  <data name="ImgurThumbnailType_Medium_Thumbnail" xml:space="preserve">
    <value>Miniatura média</value>
  </data>
  <data name="Extensions_AddContextMenu_Undo" xml:space="preserve">
    <value>Refazer</value>
  </data>
  <data name="AfterCaptureTasks_CopyFileToClipboard" xml:space="preserve">
    <value>Copiar ficheiro para área de transferência</value>
  </data>
  <data name="HotkeyType_Metadata" xml:space="preserve">
    <value />
  </data>
</root>
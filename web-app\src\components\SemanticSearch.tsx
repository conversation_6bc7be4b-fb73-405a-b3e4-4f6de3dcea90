import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Card,
  CardContent,
  Typography,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondary,
  CircularProgress,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Slider,
  FormControlLabel,
  Switch,
  Grid,
  Paper,
  Divider
} from '@mui/material';
import {
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  Tune as TuneIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  ContentCopy as ContentCopyIcon
} from '@mui/icons-material';
import { debounce } from 'lodash';

interface SearchResult {
  recordingId: string;
  filename: string;
  similarity: number;
  relevanceScore: number;
  snippet: string;
  keywords: Array<{ word: string; count: number }>;
  metadata: {
    createdAt: string;
    userName: string;
    purpose: string;
    duration: number;
    wordCount: number;
  };
  highlights: string;
}

interface SearchOptions {
  minSimilarity: number;
  maxResults: number;
  dateFrom?: string;
  dateTo?: string;
  userFilter?: string;
  typeFilter?: string;
}

const SemanticSearch: React.FC = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [searchOptions, setSearchOptions] = useState<SearchOptions>({
    minSimilarity: 0.7,
    maxResults: 20
  });
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [favorites, setFavorites] = useState<Set<string>>(new Set());

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string, options: SearchOptions) => {
      if (!searchQuery.trim()) {
        setResults([]);
        return;
      }

      setIsSearching(true);
      setError(null);

      try {
        const response = await fetch('/api/ai/semantic-search/search', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: searchQuery,
            options
          })
        });

        const data = await response.json();

        if (data.success) {
          setResults(data.data.results);
          
          // Adicionar ao histórico
          setSearchHistory(prev => {
            const newHistory = [searchQuery, ...prev.filter(q => q !== searchQuery)];
            return newHistory.slice(0, 10); // Manter apenas os 10 mais recentes
          });
        } else {
          setError(data.message || 'Erro na busca');
        }
      } catch (err) {
        setError('Erro de conexão com o serviço de busca');
        console.error('Erro na busca semântica:', err);
      } finally {
        setIsSearching(false);
      }
    }, 500),
    []
  );

  // Effect para busca automática
  useEffect(() => {
    if (query.length > 2) {
      debouncedSearch(query, searchOptions);
    } else {
      setResults([]);
    }
  }, [query, searchOptions, debouncedSearch]);

  const handleSearch = () => {
    if (query.trim()) {
      debouncedSearch(query, searchOptions);
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const toggleFavorite = (recordingId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(recordingId)) {
        newFavorites.delete(recordingId);
      } else {
        newFavorites.add(recordingId);
      }
      return newFavorites;
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getSimilarityColor = (similarity: number) => {
    if (similarity >= 0.9) return 'success';
    if (similarity >= 0.8) return 'info';
    if (similarity >= 0.7) return 'warning';
    return 'default';
  };

  return (
    <Box sx={{ maxWidth: 1200, margin: '0 auto', padding: 2 }}>
      <Typography variant="h4" gutterBottom>
        🔍 Busca Semântica Inteligente
      </Typography>
      
      <Typography variant="body1" color="text.secondary" paragraph>
        Encontre gravações usando linguagem natural. A IA entende o contexto e significado da sua busca.
      </Typography>

      {/* Campo de busca principal */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            fullWidth
            variant="outlined"
            placeholder="Ex: 'contratos de compra e venda de imóveis', 'certidões de nascimento', 'procurações para veículos'..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
          />
          <Button
            variant="contained"
            onClick={handleSearch}
            disabled={isSearching || !query.trim()}
            sx={{ minWidth: 120 }}
          >
            {isSearching ? <CircularProgress size={20} /> : 'Buscar'}
          </Button>
          <Button
            variant="outlined"
            startIcon={<TuneIcon />}
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            Filtros
          </Button>
        </Box>

        {/* Histórico de buscas */}
        {searchHistory.length > 0 && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="caption" color="text.secondary">
              Buscas recentes:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mt: 1 }}>
              {searchHistory.slice(0, 5).map((historyQuery, index) => (
                <Chip
                  key={index}
                  label={historyQuery}
                  size="small"
                  onClick={() => setQuery(historyQuery)}
                  sx={{ cursor: 'pointer' }}
                />
              ))}
            </Box>
          </Box>
        )}
      </Paper>

      {/* Filtros avançados */}
      <Accordion expanded={showAdvanced} onChange={() => setShowAdvanced(!showAdvanced)}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Filtros Avançados</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography gutterBottom>
                Similaridade Mínima: {Math.round(searchOptions.minSimilarity * 100)}%
              </Typography>
              <Slider
                value={searchOptions.minSimilarity}
                onChange={(_, value) => setSearchOptions(prev => ({ ...prev, minSimilarity: value as number }))}
                min={0.5}
                max={1}
                step={0.05}
                marks={[
                  { value: 0.5, label: '50%' },
                  { value: 0.7, label: '70%' },
                  { value: 0.9, label: '90%' }
                ]}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography gutterBottom>
                Máximo de Resultados: {searchOptions.maxResults}
              </Typography>
              <Slider
                value={searchOptions.maxResults}
                onChange={(_, value) => setSearchOptions(prev => ({ ...prev, maxResults: value as number }))}
                min={5}
                max={50}
                step={5}
                marks={[
                  { value: 10, label: '10' },
                  { value: 20, label: '20' },
                  { value: 50, label: '50' }
                ]}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="date"
                label="Data Inicial"
                value={searchOptions.dateFrom || ''}
                onChange={(e) => setSearchOptions(prev => ({ ...prev, dateFrom: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="date"
                label="Data Final"
                value={searchOptions.dateTo || ''}
                onChange={(e) => setSearchOptions(prev => ({ ...prev, dateTo: e.target.value }))}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        </AccordionDetails>
      </Accordion>

      {/* Mensagem de erro */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Resultados */}
      {results.length > 0 && (
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" gutterBottom>
            {results.length} resultado(s) encontrado(s)
          </Typography>

          <List>
            {results.map((result, index) => (
              <ListItem key={result.recordingId} sx={{ mb: 2, p: 0 }}>
                <Card sx={{ width: '100%' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="h6" component="div">
                          {result.filename}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {result.metadata.userName} • {formatDate(result.metadata.createdAt)}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={`${Math.round(result.similarity * 100)}%`}
                          color={getSimilarityColor(result.similarity)}
                          size="small"
                        />
                        <Button
                          size="small"
                          onClick={() => toggleFavorite(result.recordingId)}
                        >
                          {favorites.has(result.recordingId) ? <StarIcon /> : <StarBorderIcon />}
                        </Button>
                        <Button
                          size="small"
                          onClick={() => copyToClipboard(result.snippet)}
                        >
                          <ContentCopyIcon />
                        </Button>
                      </Box>
                    </Box>

                    <Typography variant="body1" paragraph>
                      <span dangerouslySetInnerHTML={{ __html: result.highlights }} />
                    </Typography>

                    <Divider sx={{ my: 2 }} />

                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6}>
                        <Typography variant="caption" color="text.secondary">
                          Palavras-chave:
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', mt: 0.5 }}>
                          {result.keywords.slice(0, 5).map((keyword, idx) => (
                            <Chip
                              key={idx}
                              label={`${keyword.word} (${keyword.count})`}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={6}>
                        <Typography variant="caption" color="text.secondary">
                          Detalhes:
                        </Typography>
                        <Typography variant="body2">
                          Duração: {formatDuration(result.metadata.duration)} • 
                          Palavras: {result.metadata.wordCount} • 
                          Finalidade: {result.metadata.purpose}
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </ListItem>
            ))}
          </List>
        </Box>
      )}

      {/* Estado vazio */}
      {!isSearching && query.length > 2 && results.length === 0 && !error && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            Nenhum resultado encontrado
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Tente usar termos diferentes ou ajustar os filtros de busca
          </Typography>
        </Box>
      )}

      {/* Loading state */}
      {isSearching && (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};

export default SemanticSearch;

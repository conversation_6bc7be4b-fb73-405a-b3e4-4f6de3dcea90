﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ShapeType_RegionFreehand" xml:space="preserve">
    <value>Serbest bölge</value>
  </data>
  <data name="ReplCodeMenuEntry_w_Current_week_name__Local_language_" xml:space="preserve">
    <value>Güncel hafta adı (Yerel dil)</value>
  </data>
  <data name="ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_" xml:space="preserve">
    <value>Ayarlar panoya kopyalandı.</value>
  </data>
  <data name="ImgurThumbnailType_Big_Square" xml:space="preserve">
    <value>Büyük kare</value>
  </data>
  <data name="ReplCodeMenuEntry_s_Current_second" xml:space="preserve">
    <value>Güncel saniye</value>
  </data>
  <data name="TextDestination_CustomTextUploader" xml:space="preserve">
    <value>Özel yazı yükleyici</value>
  </data>
  <data name="ProxyMethod_None" xml:space="preserve">
    <value>Hiçbiri</value>
  </data>
  <data name="ReplCodeMenuEntry_mo_Current_month" xml:space="preserve">
    <value>Güncel ay</value>
  </data>
  <data name="CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___" xml:space="preserve">
    <value>CSS için gözat...</value>
  </data>
  <data name="Extensions_AddContextMenu_Redo" xml:space="preserve">
    <value>Yinele</value>
  </data>
  <data name="HotkeyType_VideoThumbnailer" xml:space="preserve">
    <value>Video küçük resim yapıcı</value>
  </data>
  <data name="ShapeType_EffectBlur" xml:space="preserve">
    <value>Bulanıklaştır (B)</value>
  </data>
  <data name="AfterCaptureTasks_ShowQuickTaskMenu" xml:space="preserve">
    <value>Hızlı görev menüsünü göster</value>
  </data>
  <data name="CustomUploaderDestinationType_URLShortener" xml:space="preserve">
    <value>Adres kısaltıcı</value>
  </data>
  <data name="ReplCodeMenuEntry_uln_User_login_name" xml:space="preserve">
    <value>Kullanıcı giriş adı</value>
  </data>
  <data name="HotkeyType_ImageEffects" xml:space="preserve">
    <value>Resim efektleri</value>
  </data>
  <data name="ShapeType_DrawingImageScreen" xml:space="preserve">
    <value>Resim (Ekrandan)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available" xml:space="preserve">
    <value>Yeni bir {0} sürümü mevcut</value>
  </data>
  <data name="AfterUploadTasks_ShowQRCode" xml:space="preserve">
    <value>QR kod penceresini göster</value>
  </data>
  <data name="ShapeType_DrawingSpeechBalloon" xml:space="preserve">
    <value>Konuşma balonu (S)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date" xml:space="preserve">
    <value>{0} güncel</value>
  </data>
  <data name="HotkeyType_Category_ScreenRecord" xml:space="preserve">
    <value>Ekran kaydetme</value>
  </data>
  <data name="PastebinExpiration_H1" xml:space="preserve">
    <value>1 saat</value>
  </data>
  <data name="HotkeyType_ScrollingCapture" xml:space="preserve">
    <value>Kaydırarak yakalamayı başlat/durdur</value>
  </data>
  <data name="ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all" xml:space="preserve">
    <value>Otomatik artan alfanumerik büyük küçük harfe duyarlı ({n} başa n kadar 0 ekler)</value>
  </data>
  <data name="ReplCodeMenuEntry_t_Title_of_active_window" xml:space="preserve">
    <value>Pencerenin başlığı</value>
  </data>
  <data name="AfterCaptureTasks_SendImageToPrinter" xml:space="preserve">
    <value>Resimi yazdır</value>
  </data>
  <data name="ShapeType_RegionRectangle" xml:space="preserve">
    <value>Dikdörtgen bölge</value>
  </data>
  <data name="HotkeyType_ToggleActionsToolbar" xml:space="preserve">
    <value>Aksiyonlar araç çubuğunu göster/sakla</value>
  </data>
  <data name="AfterCaptureTasks_PerformActions" xml:space="preserve">
    <value>Aksiyonları gerçekleştir</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfCanvas" xml:space="preserve">
    <value>Tuval yüzdesi</value>
  </data>
  <data name="ReplCodeMenuCategory_Date_and_Time" xml:space="preserve">
    <value>Tarih ve zaman</value>
  </data>
  <data name="HotkeyType_ImageCombiner" xml:space="preserve">
    <value>Resim birleştirici</value>
  </data>
  <data name="HotkeyType_RectangleTransparent" xml:space="preserve">
    <value>Bölge yakala (Saydam)</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_" xml:space="preserve">
    <value>İndirme tamamlandı.</value>
  </data>
  <data name="YouTubeVideoPrivacy_Private" xml:space="preserve">
    <value>Kişisel</value>
  </data>
  <data name="AfterUploadTasks_ShareURL" xml:space="preserve">
    <value>Adresi paylaş</value>
  </data>
  <data name="CustomUploaderDestinationType_FileUploader" xml:space="preserve">
    <value>Dosya yükleyici</value>
  </data>
  <data name="ReplCodeMenuEntry_h_Current_hour" xml:space="preserve">
    <value>Güncel saat</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_Update_check_failed" xml:space="preserve">
    <value>Güncelleme kontrolü yapılamadı</value>
  </data>
  <data name="ReplCodeMenuEntry_ms_Current_millisecond" xml:space="preserve">
    <value>Güncel milisaniye</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Install" xml:space="preserve">
    <value>Yükle</value>
  </data>
  <data name="AfterCaptureTasks_UploadImageToHost" xml:space="preserve">
    <value>Resimi yükle</value>
  </data>
  <data name="ReplCodeMenuEntry_ix_Auto_increment_hexadecimal" xml:space="preserve">
    <value>Otomatik artan heksadesimal ({n} başa n kadar 0 ekler)</value>
  </data>
  <data name="CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__" xml:space="preserve">
    <value>Camgöbeği: {0:0.0}%, Eflatun: {1:0.0}%, Sarı: {2:0.0}%, Siyah: {3:0.0}%</value>
  </data>
  <data name="HotkeyType_FolderUpload" xml:space="preserve">
    <value>Dizin yükle</value>
  </data>
  <data name="ReplCodeMenuEntry_mi_Current_minute" xml:space="preserve">
    <value>Güncel dakika</value>
  </data>
  <data name="ShapeType_EffectPixelate" xml:space="preserve">
    <value>Mozaikle (P)</value>
  </data>
  <data name="ReplCodeMenuEntry_d_Current_day" xml:space="preserve">
    <value>Güncel gün</value>
  </data>
  <data name="PastebinExpiration_D1" xml:space="preserve">
    <value>1 gün</value>
  </data>
  <data name="ShapeType_DrawingArrow" xml:space="preserve">
    <value>Ok (A)</value>
  </data>
  <data name="ShapeType_DrawingSmartEraser" xml:space="preserve">
    <value>Akıllı silgi</value>
  </data>
  <data name="PastebinPrivacy_Unlisted" xml:space="preserve">
    <value>Listelenmemiş</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_update_is_available" xml:space="preserve">
    <value>Güncelleştirme mevcut</value>
  </data>
  <data name="HotkeyType_Category_Upload" xml:space="preserve">
    <value>Yükle</value>
  </data>
  <data name="Extensions_AddContextMenu_Cut" xml:space="preserve">
    <value>Kes</value>
  </data>
  <data name="FileExistAction_Cancel" xml:space="preserve">
    <value>Kaydetme</value>
  </data>
  <data name="AfterCaptureTasks_CopyImageToClipboard" xml:space="preserve">
    <value>Resimi panoya kopyala</value>
  </data>
  <data name="PNGBitDepth_Bit32" xml:space="preserve">
    <value>32 bit</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFActiveWindow" xml:space="preserve">
    <value>Aktif pencere alanıyla ekran kaydetme (GIF) başlat</value>
  </data>
  <data name="HotkeyType_PrintScreen" xml:space="preserve">
    <value>Tüm ekranı yakala</value>
  </data>
  <data name="ImageEditorStartMode_Normal" xml:space="preserve">
    <value>Standart</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFCustomRegion" xml:space="preserve">
    <value>Özel alan ile ekran kaydetme (GIF) başlat</value>
  </data>
  <data name="HotkeyType_CustomRegion" xml:space="preserve">
    <value>Özel bölge yakala</value>
  </data>
  <data name="ReplCodeMenuCategory_Image" xml:space="preserve">
    <value>Resim</value>
  </data>
  <data name="PastebinExpiration_M10" xml:space="preserve">
    <value>10 dakika</value>
  </data>
  <data name="RegionCaptureAction_SwapToolType" xml:space="preserve">
    <value>Araç tipini değiştir</value>
  </data>
  <data name="HotkeyType_RectangleRegion" xml:space="preserve">
    <value>Bölge yakala</value>
  </data>
  <data name="AfterCaptureTasks_DoOCR" xml:space="preserve">
    <value>Yazı tanı (OCR)</value>
  </data>
  <data name="HotkeyType_ExitShareX" xml:space="preserve">
    <value>ShareX kapat</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_Portable" xml:space="preserve">
    <value>{0} yeni sürümü mevcut.
İndirmek ister misiniz?</value>
  </data>
  <data name="Helpers_DownloadString_Download_failed_" xml:space="preserve">
    <value>İndirme başarısız:</value>
  </data>
  <data name="ShapeType_DrawingTextOutline" xml:space="preserve">
    <value>Yazı (Dış çizgili) (O)</value>
  </data>
  <data name="RegionCaptureAction_CaptureActiveMonitor" xml:space="preserve">
    <value>Aktif ekranı yakala</value>
  </data>
  <data name="ImgurThumbnailType_Small_Thumbnail" xml:space="preserve">
    <value>Küçük küçük resim</value>
  </data>
  <data name="PrintForm_LoadSettings_Print" xml:space="preserve">
    <value>Yazdır</value>
  </data>
  <data name="GIFQuality_Bit4" xml:space="preserve">
    <value>16 renk (Daha düşük boyut fakat kötü kalite)</value>
  </data>
  <data name="AfterUploadTasks_ShowAfterUploadWindow" xml:space="preserve">
    <value>"Yükleme sonrası" penceresini göster</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAdjective" xml:space="preserve">
    <value>Rastgele sıfat</value>
  </data>
  <data name="Extensions_AddContextMenu_SelectAll" xml:space="preserve">
    <value>Tümünü seç</value>
  </data>
  <data name="FileDestination_CustomFileUploader" xml:space="preserve">
    <value>Özel dosya yükleyici</value>
  </data>
  <data name="LinearGradientMode_Vertical" xml:space="preserve">
    <value>Dikey</value>
  </data>
  <data name="ReplCodeMenuCategory_Random" xml:space="preserve">
    <value>Rastgele</value>
  </data>
  <data name="CustomUploaderDestinationType_ImageUploader" xml:space="preserve">
    <value>Resim yükleyici</value>
  </data>
  <data name="HotkeyType_HashCheck" xml:space="preserve">
    <value>Hash kontrol</value>
  </data>
  <data name="HotkeyType_ScreenRecorderActiveWindow" xml:space="preserve">
    <value>Aktif pencere alanıyla ekran kaydetme başlat</value>
  </data>
  <data name="ReplCodeMenuEntry_rn_Random_number_0_to_9" xml:space="preserve">
    <value>Rastgele 0 ile 9 arasında sayı ({n} haneli)</value>
  </data>
  <data name="HotkeyType_ClipboardUploadWithContentViewer" xml:space="preserve">
    <value>İçerik gösterici ile panodan yükle</value>
  </data>
  <data name="YouTubeVideoPrivacy_Public" xml:space="preserve">
    <value>Halka açık</value>
  </data>
  <data name="HSB_ToString_" xml:space="preserve">
    <value>Renk tonu: {0:0.0}°, Doygunluk: {1:0.0}%, Parlaklık: {2:0.0}%</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_OK" xml:space="preserve">
    <value>Tamam</value>
  </data>
  <data name="HotkeyType_DragDropUpload" xml:space="preserve">
    <value>Sürükle bırak ile yükle</value>
  </data>
  <data name="PastebinExpiration_N" xml:space="preserve">
    <value>Hiçbir zaman</value>
  </data>
  <data name="HotkeyType_StartScreenRecorder" xml:space="preserve">
    <value>Son bölgeyi kullanarak ekran kaydet</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Yes" xml:space="preserve">
    <value>Evet</value>
  </data>
  <data name="HotkeyType_ImageThumbnailer" xml:space="preserve">
    <value>Küçük resim yapıcı</value>
  </data>
  <data name="ReplCodeMenuEntry_mon_Current_month_name__Local_language_" xml:space="preserve">
    <value>Güncel ay adı (Yerel dil)</value>
  </data>
  <data name="GIFQuality_Bit8" xml:space="preserve">
    <value>256 renk (Yavaş işleme fakat daha iyi kalite)</value>
  </data>
  <data name="ShapeType_DrawingImage" xml:space="preserve">
    <value>Resim (Dosya)</value>
  </data>
  <data name="ScreenRecordGIFEncoding_NET" xml:space="preserve">
    <value>.NET (Kötü kalite)</value>
  </data>
  <data name="ReplCodeMenuEntry_ia_Auto_increment_alphanumeric" xml:space="preserve">
    <value>Otomatik artan alfanumerik büyük küçük harfe duyarsız ({n} başa n kadar 0 ekler)</value>
  </data>
  <data name="AfterCaptureTasks_AddImageEffects" xml:space="preserve">
    <value>Resim efekti ekle</value>
  </data>
  <data name="AfterCaptureTasks_DeleteFile" xml:space="preserve">
    <value>Dosyayı sil</value>
  </data>
  <data name="ExportImportControl_Serialize_Export_failed_" xml:space="preserve">
    <value>Dışarı aktarma başarısız.</value>
  </data>
  <data name="ReplCodeMenuCategory_Computer" xml:space="preserve">
    <value>Bilgisayar</value>
  </data>
  <data name="FileExistAction_UniqueName" xml:space="preserve">
    <value>Dosya adına numara ekle</value>
  </data>
  <data name="ImgurThumbnailType_Large_Thumbnail" xml:space="preserve">
    <value>Büyük küçük resim</value>
  </data>
  <data name="ReplCodeMenuEntry_yy_Current_year__2_digits_" xml:space="preserve">
    <value>Güncel yıl (2 hane)</value>
  </data>
  <data name="PNGBitDepth_Automatic" xml:space="preserve">
    <value>Otomatik olarak tespit et</value>
  </data>
  <data name="ImageEditorStartMode_PreviousState" xml:space="preserve">
    <value>Önceki durum</value>
  </data>
  <data name="ShapeType_RegionEllipse" xml:space="preserve">
    <value>Elips bölge</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIF" xml:space="preserve">
    <value>Ekran kaydetme (GIF) başlat/durdur</value>
  </data>
  <data name="YouTubeVideoPrivacy_Unlisted" xml:space="preserve">
    <value>Listelenmemiş</value>
  </data>
  <data name="ObjectListView_ObjectListView_Name" xml:space="preserve">
    <value>İsim</value>
  </data>
  <data name="ReplCodeMenuCategory_Window" xml:space="preserve">
    <value>Pencere</value>
  </data>
  <data name="HotkeyType_Ruler" xml:space="preserve">
    <value>Cetvel</value>
  </data>
  <data name="ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from" xml:space="preserve">
    <value>Ayarların indirileceği adres</value>
  </data>
  <data name="ShapeType_DrawingFreehand" xml:space="preserve">
    <value>Serbest çizim (F)</value>
  </data>
  <data name="ReplCodeMenuEntry_pm_Gets_AM_PM" xml:space="preserve">
    <value>AM/PM al</value>
  </data>
  <data name="DirectoryNameEditor_EditValue_Browse_for_a_folder___" xml:space="preserve">
    <value>Dizin için gözat...</value>
  </data>
  <data name="LinearGradientMode_BackwardDiagonal" xml:space="preserve">
    <value>Geriye çapraz</value>
  </data>
  <data name="ShapeType_DrawingCursor" xml:space="preserve">
    <value>İmleç</value>
  </data>
  <data name="ImgurThumbnailType_Huge_Thumbnail" xml:space="preserve">
    <value>Devasa küçük resim</value>
  </data>
  <data name="LinearGradientMode_Horizontal" xml:space="preserve">
    <value>Yatay</value>
  </data>
  <data name="HotkeyType_AbortScreenRecording" xml:space="preserve">
    <value>Ekran kaydetme iptal et</value>
  </data>
  <data name="ReplCodeMenuEntry_y_Current_year" xml:space="preserve">
    <value>Güncel yıl</value>
  </data>
  <data name="PastebinExpiration_W2" xml:space="preserve">
    <value>2 hafta</value>
  </data>
  <data name="ImageEditorStartMode_Fullscreen" xml:space="preserve">
    <value>Tam ekran</value>
  </data>
  <data name="AfterCaptureTasks_CopyFilePathToClipboard" xml:space="preserve">
    <value>Dosya yolunu panoya kopyala</value>
  </data>
  <data name="HotkeyType_ScreenRecorder" xml:space="preserve">
    <value>Ekran kaydetme başlat/durdur</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFile" xml:space="preserve">
    <value>Resimi dosya olarak kaydet</value>
  </data>
  <data name="ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_" xml:space="preserve">
    <value>Uzantısız dosya yolu + "Çıkış dosya uzantısı"</value>
  </data>
  <data name="URLSharingServices_GoogleImageSearch" xml:space="preserve">
    <value>Google resim arama</value>
  </data>
  <data name="HotkeyType_IndexFolder" xml:space="preserve">
    <value>Dizini indeksle</value>
  </data>
  <data name="ReplCodeMenuEntry_unix_Unix_timestamp" xml:space="preserve">
    <value>Unix zaman imzası</value>
  </data>
  <data name="ScreenRecordGIFEncoding_FFmpeg" xml:space="preserve">
    <value>FFmpeg (İyi kalite)</value>
  </data>
  <data name="HotkeyType_TweetMessage" xml:space="preserve">
    <value>Tweet mesajı</value>
  </data>
  <data name="DrawImageSizeMode_DontResize" xml:space="preserve">
    <value>Yeniden boyutlandırma</value>
  </data>
  <data name="HotkeyType_StopUploads" xml:space="preserve">
    <value>Tüm aktif yüklemeleri durdur</value>
  </data>
  <data name="AfterUploadTasks_OpenURL" xml:space="preserve">
    <value>Adresi aç</value>
  </data>
  <data name="AfterCaptureTasks_AnnotateImage" xml:space="preserve">
    <value>Resim düzenleyicide aç</value>
  </data>
  <data name="MyPictureBox_LoadImageAsync_Loading_image___" xml:space="preserve">
    <value>Resim yükleniyor...</value>
  </data>
  <data name="HotkeyType_LastRegion" xml:space="preserve">
    <value>Son bölgeyi yakala</value>
  </data>
  <data name="Helpers_OpenFolder_Folder_not_exist_" xml:space="preserve">
    <value>Klasör bulunamadı:</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_CurrentVersion" xml:space="preserve">
    <value>Şu anki sürüm</value>
  </data>
  <data name="FileDestination_Email" xml:space="preserve">
    <value>Eposta</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_" xml:space="preserve">
    <value>{0} yeni sürümü mevcut.
İndirmek ve yüklemek ister misiniz?</value>
  </data>
  <data name="WavFileNameEditor_EditValue_Browse_for_a_sound_file___" xml:space="preserve">
    <value>Ses dosyası için gözat...</value>
  </data>
  <data name="Helpers_OpenFile_File_not_exist_" xml:space="preserve">
    <value>Dosya bulunamadı:</value>
  </data>
  <data name="Helpers_BrowseFolder_Choose_folder" xml:space="preserve">
    <value>Klasör seçiniz</value>
  </data>
  <data name="ExportImportControl_Deserialize_Import_failed_" xml:space="preserve">
    <value>İçeri alma başarısız.</value>
  </data>
  <data name="Extensions_AddContextMenu_Delete" xml:space="preserve">
    <value>Sil</value>
  </data>
  <data name="Extensions_AddContextMenu_Paste" xml:space="preserve">
    <value>Yapıştır</value>
  </data>
  <data name="HotkeyType_QRCodeDecodeFromScreen" xml:space="preserve">
    <value>QR kod (Ekrandan çöz)</value>
  </data>
  <data name="LinearGradientMode_ForwardDiagonal" xml:space="preserve">
    <value>İleri çapraz</value>
  </data>
  <data name="PNGBitDepth_Bit24" xml:space="preserve">
    <value>24 bit</value>
  </data>
  <data name="ReplCodeMenuEntry_wy_Week_of_year" xml:space="preserve">
    <value>Yılın haftası</value>
  </data>
  <data name="DrawImageSizeMode_AbsoluteSize" xml:space="preserve">
    <value>Mutlak boyut</value>
  </data>
  <data name="HotkeyType_OpenImageHistory" xml:space="preserve">
    <value>Resim geçmişi penceresini aç</value>
  </data>
  <data name="ReplCodeMenuCategory_Incremental" xml:space="preserve">
    <value>Artan</value>
  </data>
  <data name="RandomEmojiRepeatUsingN" xml:space="preserve">
    <value>Rastgele emoji ({n} kullanarak tekrarla)</value>
  </data>
  <data name="AfterCaptureTasks_SaveThumbnailImageToFile" xml:space="preserve">
    <value>Küçük resimi dosya olarak kaydet</value>
  </data>
  <data name="DownloaderForm_StartDownload_Downloading_" xml:space="preserve">
    <value>İndiriliyor.</value>
  </data>
  <data name="RegionCaptureAction_RemoveShapeCancelCapture" xml:space="preserve">
    <value>Nesneyi sil veya yakalamayı iptal et</value>
  </data>
  <data name="ReplCodeMenuEntry_un_User_name" xml:space="preserve">
    <value>Kullanıcı adı</value>
  </data>
  <data name="ShapeType_DrawingMagnify" xml:space="preserve">
    <value>Büyüt</value>
  </data>
  <data name="CodeMenu_Create_Close" xml:space="preserve">
    <value>Kapat</value>
  </data>
  <data name="ShapeType_DrawingSticker" xml:space="preserve">
    <value>Çıkartma</value>
  </data>
  <data name="HotkeyType_QRCode" xml:space="preserve">
    <value>QR kod</value>
  </data>
  <data name="PastebinExpiration_W1" xml:space="preserve">
    <value>1 hafta</value>
  </data>
  <data name="CustomUploaderDestinationType_URLSharingService" xml:space="preserve">
    <value>Adres paylaşma servisi</value>
  </data>
  <data name="ShapeType_EffectHighlight" xml:space="preserve">
    <value>Vurgula (H)</value>
  </data>
  <data name="GIFQuality_Grayscale" xml:space="preserve">
    <value>256 gri tonlarda renk</value>
  </data>
  <data name="GIFQuality_Default" xml:space="preserve">
    <value>Varsayılan .NET işlemesi (Hızlı işleme fakat ortalama kalite)</value>
  </data>
  <data name="ReplCodeMenuEntry_rx_Random_hexadecimal" xml:space="preserve">
    <value>Rastgele heksadesimal karakter ({n} haneli)</value>
  </data>
  <data name="PastebinPrivacy_Private" xml:space="preserve">
    <value>Kişisel (üyeler sadece)</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Hata</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAnimal" xml:space="preserve">
    <value>Rastgele hayvan</value>
  </data>
  <data name="URLSharingServices_CustomURLSharingService" xml:space="preserve">
    <value>Özel adres paylaşma servisi</value>
  </data>
  <data name="RegionCaptureAction_CaptureFullscreen" xml:space="preserve">
    <value>Tam ekran yakala</value>
  </data>
  <data name="ReplCodeMenuEntry_pn_Process_name_of_active_window" xml:space="preserve">
    <value>Pencerenin işlem ismi</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Waiting_" xml:space="preserve">
    <value>Bekliyor.</value>
  </data>
  <data name="HotkeyType_ImageEditor" xml:space="preserve">
    <value>Resim düzenleyici</value>
  </data>
  <data name="URLSharingServices_Email" xml:space="preserve">
    <value>Eposta</value>
  </data>
  <data name="HotkeyType_OpenHistory" xml:space="preserve">
    <value>Geçmiş penceresini aç</value>
  </data>
  <data name="ShapeType_ToolSelect" xml:space="preserve">
    <value>Seç ve taşı (M)</value>
  </data>
  <data name="ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric" xml:space="preserve">
    <value>Otomatik artan {n} sayı tabanında alfanumerik (1 &lt; n &lt; 63)</value>
  </data>
  <data name="HotkeyType_CaptureWebpage" xml:space="preserve">
    <value>Web sayfası yakala</value>
  </data>
  <data name="RegionCaptureAction_CancelCapture" xml:space="preserve">
    <value>Yakalamayı iptal et</value>
  </data>
  <data name="AfterCaptureTasks_ScanQRCode" xml:space="preserve">
    <value>QR kodunu tara</value>
  </data>
  <data name="HotkeyType_RectangleLight" xml:space="preserve">
    <value>Bölge yakala (Basit)</value>
  </data>
  <data name="ProxyMethod_Automatic" xml:space="preserve">
    <value>Otomatik</value>
  </data>
  <data name="HotkeyType_FileUpload" xml:space="preserve">
    <value>Dosya yükle</value>
  </data>
  <data name="ReplCodeMenuEntry_guid_Random_guid" xml:space="preserve">
    <value>Rastgele GUID</value>
  </data>
  <data name="ShapeType_DrawingLine" xml:space="preserve">
    <value>Çizgi (L)</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_value" xml:space="preserve">
    <value>Değeri kopyala</value>
  </data>
  <data name="AfterCaptureTasks_ShowBeforeUploadWindow" xml:space="preserve">
    <value>"Yükleme öncesi" penceresini göster</value>
  </data>
  <data name="AfterCaptureTasks_ShowInExplorer" xml:space="preserve">
    <value>Dosyayı klasörde göster</value>
  </data>
  <data name="ImageDestination_CustomImageUploader" xml:space="preserve">
    <value>Özel resim yükleyici</value>
  </data>
  <data name="HotkeyType_Category_ScreenCapture" xml:space="preserve">
    <value>Ekran yakalama</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_No" xml:space="preserve">
    <value>Hayır</value>
  </data>
  <data name="HotkeyType_ActiveWindow" xml:space="preserve">
    <value>Aktif pencereyi yakala</value>
  </data>
  <data name="ShapeType_DrawingStep" xml:space="preserve">
    <value>Kademe (I)</value>
  </data>
  <data name="ReplCodeMenuEntry_i_Auto_increment_number" xml:space="preserve">
    <value>Otomatik artan sayı ({n} başa n kadar 0 ekler)</value>
  </data>
  <data name="HotkeyType_ClipboardUpload" xml:space="preserve">
    <value>Panodan yükle</value>
  </data>
  <data name="ReplCodeMenuEntry_n_New_line" xml:space="preserve">
    <value>Yeni satır</value>
  </data>
  <data name="ReplCodeMenuEntry_mon2_Current_month_name__English_" xml:space="preserve">
    <value>Güncel ay adı (İngilizce)</value>
  </data>
  <data name="HotkeyType_OpenScreenshotsFolder" xml:space="preserve">
    <value>Ekran görüntüleri dizinini aç</value>
  </data>
  <data name="ReplCodeMenuEntry_width_Gets_image_width" xml:space="preserve">
    <value>Resim genişliği</value>
  </data>
  <data name="ReplCodeMenuEntry_w2_Current_week_name__English_" xml:space="preserve">
    <value>Güncel hafta adı (İngilizce)</value>
  </data>
  <data name="ExeFileNameEditor_EditValue_Browse_for_executable___" xml:space="preserve">
    <value>Çalıştırılabilir dosya için gözat...</value>
  </data>
  <data name="ImageDestination_FileUploader" xml:space="preserve">
    <value>Dosya yükleyici</value>
  </data>
  <data name="ImageEditorStartMode_AutoSize" xml:space="preserve">
    <value>Otomatik boyut</value>
  </data>
  <data name="HotkeyType_None" xml:space="preserve">
    <value>Hiçbiri</value>
  </data>
  <data name="PNGBitDepth_Default" xml:space="preserve">
    <value>Varsayılan</value>
  </data>
  <data name="Helpers_CreateDirectoryIfNotExist_Create_failed_" xml:space="preserve">
    <value>Klasör yaratılamıyor, yol ayarlarını kontrol ediniz.</value>
  </data>
  <data name="ProxyMethod_Manual" xml:space="preserve">
    <value>Elle</value>
  </data>
  <data name="DownloaderForm_ChangeStatus_Status___0_" xml:space="preserve">
    <value>Durum: {0}</value>
  </data>
  <data name="HotkeyType_StartScreenRecorderGIF" xml:space="preserve">
    <value>Son bölgeyi kullanarak ekran kaydet (GIF)</value>
  </data>
  <data name="ImgurThumbnailType_Small_Square" xml:space="preserve">
    <value>Küçük kare</value>
  </data>
  <data name="HotkeyType_MonitorTest" xml:space="preserve">
    <value>Monitör testi</value>
  </data>
  <data name="Extensions_AddContextMenu_Copy" xml:space="preserve">
    <value>Kopyala</value>
  </data>
  <data name="AfterUploadTasks_UseURLShortener" xml:space="preserve">
    <value>Adresi kısalt</value>
  </data>
  <data name="ReplCodeMenuEntry_rf_Random_line_from_file" xml:space="preserve">
    <value>Rastgele satır dosyadan (Dosya adresi için {filepath} kullan)</value>
  </data>
  <data name="DownloaderForm_StartDownload_Cancel" xml:space="preserve">
    <value>İptal</value>
  </data>
  <data name="HotkeyType_Category_Tools" xml:space="preserve">
    <value>Araçlar</value>
  </data>
  <data name="FileDestination_SharedFolder" xml:space="preserve">
    <value>Paylaşılmış dizin</value>
  </data>
  <data name="HotkeyType_ActiveMonitor" xml:space="preserve">
    <value>Aktif ekranı yakala</value>
  </data>
  <data name="DownloaderForm_StartDownload_Getting_file_size_" xml:space="preserve">
    <value>Dosya boyutu alınıyor.</value>
  </data>
  <data name="HotkeyType_Category_Other" xml:space="preserve">
    <value>Diğer</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Filename___0_" xml:space="preserve">
    <value>Dosya adı: {0}</value>
  </data>
  <data name="ShapeType_DrawingEllipse" xml:space="preserve">
    <value>Elips (E)</value>
  </data>
  <data name="HotkeyType_ColorPicker" xml:space="preserve">
    <value>Renk seçici</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>Durdur</value>
  </data>
  <data name="TextDestination_FileUploader" xml:space="preserve">
    <value>Dosya yükleyici</value>
  </data>
  <data name="MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__" xml:space="preserve">
    <value>Resim yükleniyor: {0}%</value>
  </data>
  <data name="ReplCodeMenuEntry_ra_Random_alphanumeric_char" xml:space="preserve">
    <value>Rastgele alfanumerik karakter ({n} haneli)</value>
  </data>
  <data name="ObjectListView_ObjectListView_Value" xml:space="preserve">
    <value>Değer</value>
  </data>
  <data name="HotkeyType_DisableHotkeys" xml:space="preserve">
    <value>Devre dışı bırak/Aktif et kısayolları</value>
  </data>
  <data name="RegionCaptureAction_None" xml:space="preserve">
    <value>Hiçbir şey yapma</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFileWithDialog" xml:space="preserve">
    <value>Resimi dosya olarak farklı kaydet...</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_name" xml:space="preserve">
    <value>İsmi kopyala</value>
  </data>
  <data name="RegionCaptureAction_RemoveShape" xml:space="preserve">
    <value>Nesneyi sil</value>
  </data>
  <data name="ActionsCodeMenuEntry_FilePath_File_path" xml:space="preserve">
    <value>Dosya yolu</value>
  </data>
  <data name="SupportedLanguage_Automatic" xml:space="preserve">
    <value>Otomatik</value>
  </data>
  <data name="HotkeyType_VideoConverter" xml:space="preserve">
    <value>Video çevirici</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Cancel" xml:space="preserve">
    <value>İptal</value>
  </data>
  <data name="FolderSelectDialog_Title_Select_a_folder" xml:space="preserve">
    <value>Dizin seç</value>
  </data>
  <data name="HotkeyType_OpenMainWindow" xml:space="preserve">
    <value>Ana pencereyi aç</value>
  </data>
  <data name="HotkeyType_ScreenColorPicker" xml:space="preserve">
    <value>Renk seçici ekranı</value>
  </data>
  <data name="PrintTextForm_LoadSettings_Name___0___Size___1_" xml:space="preserve">
    <value>İsim: {0}, Boyut: {1}</value>
  </data>
  <data name="HotkeyType_AutoCapture" xml:space="preserve">
    <value>Otomatik yakala</value>
  </data>
  <data name="ShapeType_DrawingRectangle" xml:space="preserve">
    <value>Dikdörtgen (R)</value>
  </data>
  <data name="ImageEditorStartMode_Maximized" xml:space="preserve">
    <value>Pencere olarak ekranı kapla</value>
  </data>
  <data name="HotkeyType_ScreenRecorderCustomRegion" xml:space="preserve">
    <value>Özel alan ile ekran kaydetme başlat</value>
  </data>
  <data name="ScreenRecordGIFEncoding_OctreeQuantizer" xml:space="preserve">
    <value>Octree nicemleyici (Orta kalite)</value>
  </data>
  <data name="Helpers_BrowseFile_Choose_file" xml:space="preserve">
    <value>Dosya seçiniz</value>
  </data>
  <data name="ReplCodeMenuEntry_height_Gets_image_height" xml:space="preserve">
    <value>Resim yüksekliği</value>
  </data>
  <data name="PastebinExpiration_M1" xml:space="preserve">
    <value>1 ay</value>
  </data>
  <data name="ShapeType_DrawingTextBackground" xml:space="preserve">
    <value>Yazı (Arkaplanlı) (T)</value>
  </data>
  <data name="RandomNonAmbiguousAlphanumericCharRepeatUsingN" xml:space="preserve">
    <value>Rastgele belirsiz olmayan alfanumerik karakter ({n} haneli)</value>
  </data>
  <data name="UrlShortenerType_CustomURLShortener" xml:space="preserve">
    <value>Özel adres kısaltıcı</value>
  </data>
  <data name="PastebinPrivacy_Public" xml:space="preserve">
    <value>Halka açık</value>
  </data>
  <data name="FileExistAction_Overwrite" xml:space="preserve">
    <value>Dosya üzerine yaz</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfWatermark" xml:space="preserve">
    <value>Resim yüzdesi</value>
  </data>
  <data name="HotkeyType_ShortenURL" xml:space="preserve">
    <value>Adresi kısalt</value>
  </data>
  <data name="CustomUploaderDestinationType_TextUploader" xml:space="preserve">
    <value>Yazı yükleyici</value>
  </data>
  <data name="FileExistAction_Ask" xml:space="preserve">
    <value>Ne yapılacağını sor</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_LatestVersion" xml:space="preserve">
    <value>Son sürüm</value>
  </data>
  <data name="AfterCaptureTasks_ShowAfterCaptureWindow" xml:space="preserve">
    <value>"Yakalama sonrası" penceresini göster</value>
  </data>
  <data name="HotkeyType_UploadText" xml:space="preserve">
    <value>Yazı yükle</value>
  </data>
  <data name="ShapeType_ToolCrop" xml:space="preserve">
    <value>Resmi kırp (C)</value>
  </data>
  <data name="HotkeyType_UploadURL" xml:space="preserve">
    <value>Adresten yükle</value>
  </data>
  <data name="HotkeyType_ImageSplitter" xml:space="preserve">
    <value>Resim ayırıcı</value>
  </data>
  <data name="AfterUploadTasks_CopyURLToClipboard" xml:space="preserve">
    <value>Adresi panoya kopyala</value>
  </data>
  <data name="ReplCodeMenuEntry_cn_Computer_name" xml:space="preserve">
    <value>Bilgisayar adı</value>
  </data>
  <data name="HotkeyType_StartAutoCapture" xml:space="preserve">
    <value>Son bölgeyi kullanarak otomatik yakala</value>
  </data>
  <data name="ImgurThumbnailType_Medium_Thumbnail" xml:space="preserve">
    <value>Orta küçük resim</value>
  </data>
  <data name="Extensions_AddContextMenu_Undo" xml:space="preserve">
    <value>Geri al</value>
  </data>
  <data name="AfterCaptureTasks_CopyFileToClipboard" xml:space="preserve">
    <value>Dosyayı panoya kopyala</value>
  </data>
  <data name="ResultOfFirstFile" xml:space="preserve">
    <value>İlk dosya sonucu:</value>
  </data>
  <data name="ResultOfSecondFile" xml:space="preserve">
    <value>İkinci dosya sonucu:</value>
  </data>
  <data name="Result" xml:space="preserve">
    <value>Sonuç:</value>
  </data>
  <data name="Target" xml:space="preserve">
    <value>Hedef:</value>
  </data>
  <data name="ArrowHeadDirection_End" xml:space="preserve">
    <value>Son</value>
  </data>
  <data name="ArrowHeadDirection_Start" xml:space="preserve">
    <value>Baş</value>
  </data>
  <data name="ArrowHeadDirection_Both" xml:space="preserve">
    <value>İki taraf</value>
  </data>
  <data name="StepType_LettersLowercase" xml:space="preserve">
    <value>Harfler (Küçük harf)</value>
  </data>
  <data name="StepType_LettersUppercase" xml:space="preserve">
    <value>Harfler (Büyük harf)</value>
  </data>
  <data name="StepType_Numbers" xml:space="preserve">
    <value>Sayılar</value>
  </data>
  <data name="StepType_RomanNumeralsLowercase" xml:space="preserve">
    <value>Roma rakamları (Küçük harf)</value>
  </data>
  <data name="StepType_RomanNumeralsUppercase" xml:space="preserve">
    <value>Roma rakamları (Büyük harf)</value>
  </data>
  <data name="HotkeyType_ClipboardViewer" xml:space="preserve">
    <value>Pano görüntüleyici</value>
  </data>
  <data name="HotkeyType_InspectWindow" xml:space="preserve">
    <value>Pencere incele</value>
  </data>
  <data name="BorderStyle_Solid" xml:space="preserve">
    <value>Düz</value>
  </data>
  <data name="BorderStyle_Dash" xml:space="preserve">
    <value>Tire</value>
  </data>
  <data name="BorderStyle_Dot" xml:space="preserve">
    <value>Nokta</value>
  </data>
  <data name="BorderStyle_DashDot" xml:space="preserve">
    <value>Tire nokta</value>
  </data>
  <data name="BorderStyle_DashDotDot" xml:space="preserve">
    <value>Tire nokta nokta</value>
  </data>
  <data name="ToastClickAction_CloseNotification" xml:space="preserve">
    <value>Uyarı penceresini kapat</value>
  </data>
  <data name="ToastClickAction_AnnotateImage" xml:space="preserve">
    <value>Resimi düzenle</value>
  </data>
  <data name="ToastClickAction_CopyImageToClipboard" xml:space="preserve">
    <value>Resim kopyala</value>
  </data>
  <data name="ToastClickAction_CopyFile" xml:space="preserve">
    <value>Dosya kopyala</value>
  </data>
  <data name="ToastClickAction_CopyFilePath" xml:space="preserve">
    <value>Dosya yolunu kopyala</value>
  </data>
  <data name="ToastClickAction_CopyUrl" xml:space="preserve">
    <value>Bağlantıyı kopyala</value>
  </data>
  <data name="ToastClickAction_OpenFile" xml:space="preserve">
    <value>Dosya aç</value>
  </data>
  <data name="ToastClickAction_OpenFolder" xml:space="preserve">
    <value>Klasör aç</value>
  </data>
  <data name="ToastClickAction_OpenUrl" xml:space="preserve">
    <value>Bağlantı aç</value>
  </data>
  <data name="ToastClickAction_Upload" xml:space="preserve">
    <value>Karşıya dosya yükle</value>
  </data>
  <data name="ContentAlignment_TopLeft" xml:space="preserve">
    <value>Sol üst</value>
  </data>
  <data name="ContentAlignment_TopCenter" xml:space="preserve">
    <value>Orta üst</value>
  </data>
  <data name="ContentAlignment_TopRight" xml:space="preserve">
    <value>Sağ üst</value>
  </data>
  <data name="ContentAlignment_MiddleLeft" xml:space="preserve">
    <value>Sol orta</value>
  </data>
  <data name="ContentAlignment_MiddleCenter" xml:space="preserve">
    <value>Orta merkez</value>
  </data>
  <data name="ContentAlignment_MiddleRight" xml:space="preserve">
    <value>Sağ orta</value>
  </data>
  <data name="ContentAlignment_BottomLeft" xml:space="preserve">
    <value>Sol alt</value>
  </data>
  <data name="ContentAlignment_BottomCenter" xml:space="preserve">
    <value>Orta alt</value>
  </data>
  <data name="ContentAlignment_BottomRight" xml:space="preserve">
    <value>Sağ alt</value>
  </data>
  <data name="URLSharingServices_BingVisualSearch" xml:space="preserve">
    <value>Bing görsel arama</value>
  </data>
  <data name="EDataType_Default" xml:space="preserve">
    <value>Varsayılan</value>
  </data>
  <data name="EDataType_File" xml:space="preserve">
    <value>Dosya</value>
  </data>
  <data name="EDataType_Image" xml:space="preserve">
    <value>Resim</value>
  </data>
  <data name="EDataType_Text" xml:space="preserve">
    <value>Yazı</value>
  </data>
  <data name="EDataType_URL" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="RegionCaptureAction_CaptureLastRegion" xml:space="preserve">
    <value>Son bölgeyi yakala</value>
  </data>
  <data name="HotkeyType_StopScreenRecording" xml:space="preserve">
    <value>Ekran kaydını durdur</value>
  </data>
  <data name="HotkeyType_ToggleTrayMenu" xml:space="preserve">
    <value>Tepsi menüsünü aç/kapat</value>
  </data>
  <data name="ThumbnailViewClickAction_Default" xml:space="preserve">
    <value>Varsayılan</value>
  </data>
  <data name="ThumbnailViewClickAction_EditImage" xml:space="preserve">
    <value>Resimi düzenle</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFile" xml:space="preserve">
    <value>Dosya aç</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFolder" xml:space="preserve">
    <value>Klasör aç</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenImageViewer" xml:space="preserve">
    <value>Resim görüntüleyiciyi aç</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenURL" xml:space="preserve">
    <value>Adresi aç</value>
  </data>
  <data name="ThumbnailViewClickAction_Select" xml:space="preserve">
    <value>Seç</value>
  </data>
  <data name="ImagePreviewLocation_Bottom" xml:space="preserve">
    <value>Alt</value>
  </data>
  <data name="ImagePreviewLocation_Side" xml:space="preserve">
    <value>Kenar</value>
  </data>
  <data name="ImagePreviewVisibility_Automatic" xml:space="preserve">
    <value>Otomatik</value>
  </data>
  <data name="ImagePreviewVisibility_Hide" xml:space="preserve">
    <value>Gizle</value>
  </data>
  <data name="ImagePreviewVisibility_Show" xml:space="preserve">
    <value>Göster</value>
  </data>
  <data name="TaskViewMode_ListView" xml:space="preserve">
    <value>Liste görünümü</value>
  </data>
  <data name="TaskViewMode_ThumbnailView" xml:space="preserve">
    <value>Küçük resim görünümü</value>
  </data>
  <data name="ThumbnailTitleLocation_Bottom" xml:space="preserve">
    <value>Alt</value>
  </data>
  <data name="ThumbnailTitleLocation_Top" xml:space="preserve">
    <value>Üst</value>
  </data>
  <data name="HotkeyType_ImageViewer" xml:space="preserve">
    <value>Resim görüntüleyici</value>
  </data>
  <data name="HotkeyType_OCR" xml:space="preserve">
    <value>OCR</value>
  </data>
  <data name="HotkeyType_BorderlessWindow" xml:space="preserve">
    <value>Kenarsız pencere</value>
  </data>
  <data name="AfterCaptureTasks_PinToScreen" xml:space="preserve">
    <value>Ekrana sabitle</value>
  </data>
  <data name="ToastClickAction_PinToScreen" xml:space="preserve">
    <value>Ekrana sabitle</value>
  </data>
  <data name="ShareXImageViewer" xml:space="preserve">
    <value>ShareX - Resim görüntüleyici</value>
  </data>
  <data name="HotkeyType_PinToScreen" xml:space="preserve">
    <value>Ekrana sabitle</value>
  </data>
  <data name="CutOutEffectType_None" xml:space="preserve">
    <value>Efekt yok</value>
  </data>
  <data name="CutOutEffectType_TornEdge" xml:space="preserve">
    <value>Yırtık kenarlar</value>
  </data>
  <data name="CutOutEffectType_Wave" xml:space="preserve">
    <value>Dalga</value>
  </data>
  <data name="CutOutEffectType_ZigZag" xml:space="preserve">
    <value>Testere dişi</value>
  </data>
  <data name="ShapeType_ToolCutOut" xml:space="preserve">
    <value>Kesip çıkar (X)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromClipboard" xml:space="preserve">
    <value>Ekrana sabitle (Panodan)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromFile" xml:space="preserve">
    <value>Ekrana sabitle (Dosyadan)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromScreen" xml:space="preserve">
    <value>Ekrana sabitle (Ekrandan)</value>
  </data>
  <data name="HotkeyType_Metadata" xml:space="preserve">
    <value />
  </data>
</root>
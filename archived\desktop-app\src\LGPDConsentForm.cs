using System;
using System.Drawing;
using System.Windows.Forms;

namespace CartorioDesktopApp
{
    public class LGPDConsentForm : Form
    {
        private readonly TextBox _txtName;
        private readonly TextBox _txtDocument;
        private readonly CheckBox _chkConsent;
        private readonly Button _btnAccept;
        private readonly Button _btnCancel;
        
        public string UserName { get; private set; } = "";
        public string UserDocument { get; private set; } = "";
        public DateTime ConsentTimestamp { get; private set; }
        
        public LGPDConsentForm()
        {
            this.Text = "Consentimento LGPD - Lei 13.709/2018";
            this.Size = new Size(500, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            // Título
            var lblTitle = new Label();
            lblTitle.Text = "Termo de Consentimento para Gravação de Tela";
            lblTitle.Font = new Font(lblTitle.Font.FontFamily, 12, FontStyle.Bold);
            lblTitle.AutoSize = true;
            lblTitle.Location = new Point(20, 20);
            this.Controls.Add(lblTitle);
            
            // Texto explicativo
            var lblExplanation = new Label();
            lblExplanation.Text = 
                "De acordo com o Art. 7º da Lei Geral de Proteção de Dados (Lei 13.709/2018), " +
                "precisamos do seu consentimento explícito para iniciar a gravação de tela. " +
                "Esta gravação pode conter informações pessoais e será armazenada de forma " +
                "segura com criptografia AES-256. Os dados coletados serão utilizados " +
                "exclusivamente para fins de documentação e prova processual conforme o " +
                "Art. 369 do Código de Processo Civil.\n\n" +
                "Ao aceitar este termo, você concorda com:\n" +
                "• A gravação da sua tela e possível áudio;\n" +
                "• O armazenamento seguro dessa gravação em banco de dados criptografado;\n" +
                "• A geração de um hash SHA-256 para garantir a integridade do arquivo;\n" +
                "• A possibilidade de uso dessa gravação como prova processual;\n" +
                "• O direito de solicitar a exclusão dos seus dados a qualquer momento.";
            lblExplanation.Size = new Size(440, 200);
            lblExplanation.Location = new Point(20, 50);
            this.Controls.Add(lblExplanation);
            
            // Nome
            var lblName = new Label();
            lblName.Text = "Nome completo:";
            lblName.AutoSize = true;
            lblName.Location = new Point(20, 260);
            this.Controls.Add(lblName);
            
            _txtName = new TextBox();
            _txtName.Size = new Size(440, 25);
            _txtName.Location = new Point(20, 280);
            this.Controls.Add(_txtName);
            
            // Documento
            var lblDocument = new Label();
            lblDocument.Text = "CPF:";
            lblDocument.AutoSize = true;
            lblDocument.Location = new Point(20, 310);
            this.Controls.Add(lblDocument);
            
            _txtDocument = new TextBox();
            _txtDocument.Size = new Size(200, 25);
            _txtDocument.Location = new Point(20, 330);
            this.Controls.Add(_txtDocument);
            
            // Checkbox de consentimento
            _chkConsent = new CheckBox();
            _chkConsent.Text = "Eu li e concordo com os termos acima.";
            _chkConsent.Size = new Size(300, 20);
            _chkConsent.Location = new Point(20, 370);
            _chkConsent.CheckedChanged += (s, e) => UpdateButtonState();
            this.Controls.Add(_chkConsent);
            
            // Botões
            _btnAccept = new Button();
            _btnAccept.Text = "Aceitar e Prosseguir";
            _btnAccept.Size = new Size(150, 30);
            _btnAccept.Location = new Point(310, 410);
            _btnAccept.Enabled = false;
            _btnAccept.Click += (s, e) => 
            {
                // Salvar informações de consentimento
                UserName = _txtName.Text;
                UserDocument = _txtDocument.Text;
                ConsentTimestamp = DateTime.Now;
                
                DialogResult = DialogResult.OK;
                Close();
            };
            this.Controls.Add(_btnAccept);
            
            _btnCancel = new Button();
            _btnCancel.Text = "Cancelar";
            _btnCancel.Size = new Size(100, 30);
            _btnCancel.Location = new Point(200, 410);
            _btnCancel.Click += (s, e) => 
            {
                DialogResult = DialogResult.Cancel;
                Close();
            };
            this.Controls.Add(_btnCancel);
        }
        
        private void UpdateButtonState()
        {
            bool hasName = !string.IsNullOrWhiteSpace(_txtName.Text);
            bool hasDocument = !string.IsNullOrWhiteSpace(_txtDocument.Text);
            bool hasConsent = _chkConsent.Checked;
            
            _btnAccept.Enabled = hasName && hasDocument && hasConsent;
        }
    }
}

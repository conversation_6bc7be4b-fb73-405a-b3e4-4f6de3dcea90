import { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Checkbox,
  FormControlLabel,
  Box,
  Alert
} from '@mui/material';

interface ConsentDialogProps {
  open: boolean;
  onClose: (consent: boolean) => void;
}

const LGPDConsentDialog = ({ open, onClose }: ConsentDialogProps) => {
  const [checked, setChecked] = useState(false);
  const [showError, setShowError] = useState(false);
  
  const handleConfirm = () => {
    if (!checked) {
      setShowError(true);
      return;
    }
    
    onClose(true);
    setChecked(false);
    setShowError(false);
  };
  
  const handleCancel = () => {
    onClose(false);
    setChecked(false);
    setShowError(false);
  };
  
  return (
    <Dialog 
      open={open} 
      onClose={() => onClose(false)}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>
        Termo de Consentimento - LGPD
      </DialogTitle>
      
      <DialogContent>
        <Typography variant="body1" paragraph>
          Em conformidade com a Lei Geral de Proteção de Dados (LGPD - Lei nº 13.709/2018),
          solicitamos seu consentimento para a gravação e processamento de sua interação com este sistema.
        </Typography>
        
        <Typography variant="body1" paragraph>
          <strong>Finalidade do tratamento:</strong> Esta gravação tem como objetivo
          registrar o procedimento realizado para fins de documentação oficial, segurança jurídica
          e cumprimento de requisitos legais estabelecidos para a validade de atos cartoriais.
        </Typography>
        
        <Typography variant="body1" paragraph>
          <strong>Dados coletados:</strong> A gravação capturará sua imagem, voz, dados inseridos
          durante o procedimento e metadados técnicos como data, hora e localização aproximada.
        </Typography>
        
        <Typography variant="body1" paragraph>
          <strong>Armazenamento:</strong> Os dados serão armazenados em ambiente seguro com 
          criptografia AES-256 e hash SHA-256 para garantir sua integridade e autenticidade pelo
          prazo legal aplicável ao procedimento em questão.
        </Typography>
        
        <Typography variant="body1" paragraph>
          <strong>Compartilhamento:</strong> As gravações poderão ser compartilhadas com autoridades
          judiciais, órgãos reguladores ou outras partes envolvidas no procedimento, sempre mediante
          requisição formal e dentro dos limites legais.
        </Typography>
        
        <Typography variant="body1" paragraph>
          <strong>Seus direitos:</strong> Você tem o direito de solicitar acesso, correção, anonimização, 
          portabilidade ou exclusão de seus dados pessoais, ressalvadas as hipóteses legais que exijam
          sua preservação.
        </Typography>
        
        <Box mt={2}>
          <FormControlLabel
            control={
              <Checkbox 
                checked={checked} 
                onChange={(e) => setChecked(e.target.checked)} 
                color="primary"
              />
            }
            label="Declaro que li e concordo com os termos acima, e autorizo a gravação e processamento dos meus dados conforme descrito"
          />
        </Box>
        
        {showError && (
          <Alert severity="error" sx={{ mt: 2 }}>
            Você precisa concordar com os termos para continuar.
          </Alert>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleCancel} color="primary">
          Cancelar
        </Button>
        <Button onClick={handleConfirm} color="primary" variant="contained">
          Confirmar
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default LGPDConsentDialog;

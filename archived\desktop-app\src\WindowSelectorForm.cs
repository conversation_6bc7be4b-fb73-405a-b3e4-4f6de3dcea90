using System;
using System.Drawing;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.Collections.Generic;

namespace CartorioDesktopApp
{
    public class WindowSelectorForm : Form
    {
        private ListBox _lstWindows;
        private Button _btnSelect;
        private Button _btnCancel;
        private List<WindowInfo> _windowList = new List<WindowInfo>();
        
        public IntPtr SelectedWindow { get; private set; } = IntPtr.Zero;
        
        public WindowSelectorForm()
        {
            this.Text = "Selecionar Janela para Gravação";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            
            // Lista de janelas
            _lstWindows = new ListBox();
            _lstWindows.Size = new Size(460, 300);
            _lstWindows.Location = new Point(20, 20);
            this.Controls.Add(_lstWindows);
            
            // Botões
            _btnSelect = new Button();
            _btnSelect.Text = "Selecionar";
            _btnSelect.Size = new Size(100, 30);
            _btnSelect.Location = new Point(380, 330);
            _btnSelect.Click += (s, e) => 
            {
                if (_lstWindows.SelectedIndex >= 0)
                {
                    SelectedWindow = _windowList[_lstWindows.SelectedIndex].Handle;
                    DialogResult = DialogResult.OK;
                    Close();
                }
                else
                {
                    MessageBox.Show("Por favor, selecione uma janela.", "Seleção de Janela", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            };
            this.Controls.Add(_btnSelect);
            
            _btnCancel = new Button();
            _btnCancel.Text = "Cancelar";
            _btnCancel.Size = new Size(100, 30);
            _btnCancel.Location = new Point(270, 330);
            _btnCancel.Click += (s, e) => 
            {
                DialogResult = DialogResult.Cancel;
                Close();
            };
            this.Controls.Add(_btnCancel);
            
            // Botão para atualizar a lista
            var btnRefresh = new Button();
            btnRefresh.Text = "Atualizar Lista";
            btnRefresh.Size = new Size(120, 30);
            btnRefresh.Location = new Point(20, 330);
            btnRefresh.Click += (s, e) => PopulateWindowList();
            this.Controls.Add(btnRefresh);
            
            // Preencher a lista de janelas
            this.Load += (s, e) => PopulateWindowList();
        }
        
        private void PopulateWindowList()
        {
            _lstWindows.Items.Clear();
            _windowList.Clear();
            
            EnumWindows(EnumWindowsProc, IntPtr.Zero);
        }
        
        private bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam)
        {
            // Ignorar janelas invisíveis ou sem título
            if (!IsWindowVisible(hWnd))
                return true;
            
            int length = GetWindowTextLength(hWnd);
            if (length == 0)
                return true;
            
            // Obter título da janela
            StringBuilder title = new StringBuilder(length + 1);
            GetWindowText(hWnd, title, title.Capacity);
            
            // Adicionar à lista
            var windowInfo = new WindowInfo
            {
                Handle = hWnd,
                Title = title.ToString()
            };
            
            _windowList.Add(windowInfo);
            _lstWindows.Items.Add(windowInfo.Title);
            
            return true;
        }
        
        #region Windows API
        
        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);
        
        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);
        
        [DllImport("user32.dll")]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);
        
        [DllImport("user32.dll")]
        private static extern int GetWindowTextLength(IntPtr hWnd);
        
        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);
        
        #endregion
        
        private class WindowInfo
        {
            public IntPtr Handle { get; set; }
            public string Title { get; set; } = "";
        }
    }
    
    public class RegionSelectorForm : Form
    {
        private Point _startPoint;
        private Rectangle _selectedRegion;
        private bool _isSelecting;
        
        public Rectangle SelectedRegion => _selectedRegion;
        
        public RegionSelectorForm()
        {
            this.Text = "Selecionar Região para Gravação";
            this.FormBorderStyle = FormBorderStyle.None;
            this.WindowState = FormWindowState.Maximized;
            this.TopMost = true;
            this.Cursor = Cursors.Cross;
            this.BackColor = Color.Black;
            this.Opacity = 0.5;
            this.ShowInTaskbar = false;
            
            this.MouseDown += (s, e) => 
            {
                if (e.Button == MouseButtons.Left)
                {
                    _startPoint = e.Location;
                    _isSelecting = true;
                }
            };
            
            this.MouseMove += (s, e) => 
            {
                if (_isSelecting)
                {
                    int x = Math.Min(_startPoint.X, e.X);
                    int y = Math.Min(_startPoint.Y, e.Y);
                    int width = Math.Abs(e.X - _startPoint.X);
                    int height = Math.Abs(e.Y - _startPoint.Y);
                    
                    _selectedRegion = new Rectangle(x, y, width, height);
                    this.Invalidate();
                }
            };
            
            this.MouseUp += (s, e) => 
            {
                if (e.Button == MouseButtons.Left)
                {
                    _isSelecting = false;
                    
                    // Ajustar para coordenadas da tela
                    _selectedRegion = new Rectangle(
                        _selectedRegion.X + this.Left,
                        _selectedRegion.Y + this.Top,
                        _selectedRegion.Width,
                        _selectedRegion.Height);
                    
                    // Verificar tamanho mínimo
                    if (_selectedRegion.Width < 50 || _selectedRegion.Height < 50)
                    {
                        MessageBox.Show("A região selecionada é muito pequena. Por favor, selecione uma região maior.",
                                       "Região Inválida", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                    else
                    {
                        DialogResult = DialogResult.OK;
                        Close();
                    }
                }
            };
            
            this.KeyDown += (s, e) => 
            {
                if (e.KeyCode == Keys.Escape)
                {
                    DialogResult = DialogResult.Cancel;
                    Close();
                }
            };
            
            this.Paint += (s, e) => 
            {
                if (_isSelecting)
                {
                    using (Pen pen = new Pen(Color.Red, 2))
                    {
                        e.Graphics.DrawRectangle(pen, _selectedRegion);
                    }
                    
                    using (SolidBrush brush = new SolidBrush(Color.FromArgb(50, Color.White)))
                    {
                        e.Graphics.FillRectangle(brush, _selectedRegion);
                    }
                }
            };
        }
    }
}

﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ShareX.HistoryLib.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ShareX.HistoryLib.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap application_block {
            get {
                object obj = ResourceManager.GetObject("application_block", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap chart {
            get {
                object obj = ResourceManager.GetObject("chart", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error occurred while reading history file:.
        /// </summary>
        internal static string ErrorOccuredWhileReadingHistoryFile {
            get {
                return ResourceManager.GetString("ErrorOccuredWhileReadingHistoryFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filtered.
        /// </summary>
        internal static string Filtered {
            get {
                return ResourceManager.GetString("Filtered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap gear {
            get {
                object obj = ResourceManager.GetObject("gear", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap globe {
            get {
                object obj = ResourceManager.GetObject("globe", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File name, window title, process name, etc..
        /// </summary>
        internal static string HistoryForm_Search_Watermark {
            get {
                return ResourceManager.GetString("HistoryForm_Search_Watermark", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filtered: {0}.
        /// </summary>
        internal static string HistoryForm_UpdateItemCount___Filtered___0_ {
            get {
                return ResourceManager.GetString("HistoryForm_UpdateItemCount___Filtered___0_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total: {0}.
        /// </summary>
        internal static string HistoryForm_UpdateItemCount_Total___0_ {
            get {
                return ResourceManager.GetString("HistoryForm_UpdateItemCount_Total___0_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to History item counts:.
        /// </summary>
        internal static string HistoryItemCounts {
            get {
                return ResourceManager.GetString("HistoryItemCounts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Copy {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Copy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deletion URL.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Deletion_URL {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Deletion_URL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit image....
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_EditImage {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_EditImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_File {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_File", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File name.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_File_name {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_File_name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File name with extension.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_File_name_with_extension {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_File_name_with_extension", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File path.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_File_path {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_File_path", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Folder.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Folder {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Folder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forum (BBCode) image.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Forum__BBCode__image {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Forum__BBCode__image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forum (BBCode) link.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Forum__BBCode__link {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Forum__BBCode__link", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forum (BBCode) linked image.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Forum__BBCode__linked_image {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Forum__BBCode__linked_image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTML image.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_HTML_image {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_HTML_image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTML link.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_HTML_link {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_HTML_link", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTML linked image.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_HTML_linked_image {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_HTML_linked_image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Image {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image preview....
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Image_preview {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Image_preview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Markdown image.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Markdown__image {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Markdown__image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Markdown link.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Markdown__link {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Markdown__link", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Markdown linked image.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Markdown__linked_image {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Markdown__linked_image", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More info....
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_More_info {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_More_info", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Open {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shortened URL.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Shortened_URL {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Shortened_URL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Text {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thumbnail URL.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_Thumbnail_URL {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_Thumbnail_URL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload file.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_UploadFile {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_UploadFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URL.
        /// </summary>
        internal static string HistoryItemManager_InitializeComponent_URL {
            get {
                return ResourceManager.GetString("HistoryItemManager_InitializeComponent_URL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        internal static string HistoryManager_GetHistoryItems_Error {
            get {
                return ResourceManager.GetString("HistoryManager_GetHistoryItems_Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to History stats.
        /// </summary>
        internal static string HistoryStats {
            get {
                return ResourceManager.GetString("HistoryStats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File extensions:.
        /// </summary>
        internal static string HistoryStats_FileExtensions {
            get {
                return ResourceManager.GetString("HistoryStats_FileExtensions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hosts:.
        /// </summary>
        internal static string HistoryStats_Hosts {
            get {
                return ResourceManager.GetString("HistoryStats_Hosts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total:.
        /// </summary>
        internal static string HistoryStats_Total {
            get {
                return ResourceManager.GetString("HistoryStats_Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yearly usages:.
        /// </summary>
        internal static string HistoryStats_YearlyUsages {
            get {
                return ResourceManager.GetString("HistoryStats_YearlyUsages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap image {
            get {
                object obj = ResourceManager.GetObject("image", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap layout_header_3_mix {
            get {
                object obj = ResourceManager.GetObject("layout-header-3-mix", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap magnifier {
            get {
                object obj = ResourceManager.GetObject("magnifier", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap magnifier__plus {
            get {
                object obj = ResourceManager.GetObject("magnifier--plus", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type System.Drawing.Bitmap.
        /// </summary>
        internal static System.Drawing.Bitmap notebook {
            get {
                object obj = ResourceManager.GetObject("notebook", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pin to screen.
        /// </summary>
        internal static string PinToScreen {
            get {
                return ResourceManager.GetString("PinToScreen", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Process names:.
        /// </summary>
        internal static string ProcessNames {
            get {
                return ResourceManager.GetString("ProcessNames", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total.
        /// </summary>
        internal static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
    }
}

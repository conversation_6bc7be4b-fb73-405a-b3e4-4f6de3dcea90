import 'package:flutter/material.dart';

class ConsentDialog extends StatefulWidget {
  const ConsentDialog({super.key});

  @override
  State<ConsentDialog> createState() => _ConsentDialogState();
}

class _ConsentDialogState extends State<ConsentDialog> {
  final _nameController = TextEditingController();
  final _documentController = TextEditingController();
  bool _consentGiven = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Consentimento LGPD - Lei 13.709/2018',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Termo de Consentimento para Gravação de Tela',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            SizedBox(height: 16),
            Text(
              'De acordo com o Art. 7º da Lei Geral de Proteção de Dados (Lei 13.709/2018), '
              'precisamos do seu consentimento explícito para iniciar a gravação de tela. '
              'Esta gravação pode conter informações pessoais e será armazenada de forma '
              'segura com criptografia AES-256. Os dados coletados serão utilizados '
              'exclusivamente para fins de documentação e prova processual conforme o '
              'Art. 369 do Código de Processo Civil.',
              style: TextStyle(fontSize: 14),
            ),
            SizedBox(height: 16),
            Text(
              'Ao aceitar este termo, você concorda com:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            _bulletPoint('A gravação da sua tela;'),
            _bulletPoint('O armazenamento seguro dessa gravação em banco de dados criptografado;'),
            _bulletPoint('A geração de um hash SHA-256 para garantir a integridade do arquivo;'),
            _bulletPoint('A possibilidade de uso dessa gravação como prova processual;'),
            _bulletPoint('O direito de solicitar a exclusão dos seus dados a qualquer momento.'),
            SizedBox(height: 16),
            TextField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'Nome completo',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 12),
            TextField(
              controller: _documentController,
              decoration: InputDecoration(
                labelText: 'CPF',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Checkbox(
                  value: _consentGiven,
                  onChanged: (value) {
                    setState(() {
                      _consentGiven = value ?? false;
                    });
                  },
                ),
                Expanded(
                  child: Text(
                    'Eu li e concordo com os termos acima.',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text('Cancelar'),
        ),
        ElevatedButton(
          onPressed: _isFormValid()
              ? () => Navigator.of(context).pop(true)
              : null,
          child: Text('Aceitar e Prosseguir'),
        ),
      ],
    );
  }

  Widget _bulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }

  bool _isFormValid() {
    return _consentGiven &&
           _nameController.text.isNotEmpty &&
           _documentController.text.isNotEmpty;
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _documentController.dispose();
    super.dispose();
  }
}

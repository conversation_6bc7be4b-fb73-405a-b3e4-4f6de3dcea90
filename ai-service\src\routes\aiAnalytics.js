const express = require('express');
const router = express.Router();

/**
 * GET /api/ai/analytics/dashboard
 * Dashboard geral de IA com todas as métricas
 */
router.get('/dashboard', async (req, res) => {
  try {
    const aiDatabase = require('../database/aiDatabase');
    const queueManager = require('../services/queueManager');
    
    const [
      generalStats,
      queueStats,
      recentTranscriptions,
      recentCompliance,
      recentQuality,
      recentSearches
    ] = await Promise.all([
      aiDatabase.getGeneralStats(),
      queueManager.getQueueStats(),
      aiDatabase.getRecentTranscriptions?.(7) || [],
      aiDatabase.getRecentComplianceAnalyses(7),
      aiDatabase.getRecentQualityAnalyses(7),
      aiDatabase.getRecentSearches(7)
    ]);

    const dashboard = {
      overview: {
        totalProcessedRecordings: generalStats.totalTranscriptions,
        totalEmbeddings: generalStats.totalEmbeddings,
        totalComplianceAnalyses: generalStats.totalComplianceAnalyses,
        totalQualityAnalyses: generalStats.totalQualityAnalyses,
        recentSearches: generalStats.recentSearches
      },
      queues: queueStats,
      activity: {
        transcriptions: {
          recent: recentTranscriptions.length,
          avgProcessingTime: recentTranscriptions.reduce((sum, t) => sum + (t.processing_time || 0), 0) / Math.max(recentTranscriptions.length, 1),
          successRate: recentTranscriptions.filter(t => t.text && t.text.length > 0).length / Math.max(recentTranscriptions.length, 1)
        },
        compliance: {
          recent: recentCompliance.length,
          avgScore: recentCompliance.reduce((sum, c) => sum + (c.compliance_score || 0), 0) / Math.max(recentCompliance.length, 1),
          riskDistribution: this.calculateRiskDistribution(recentCompliance)
        },
        quality: {
          recent: recentQuality.length,
          avgScore: recentQuality.reduce((sum, q) => sum + (q.overall_score || 0), 0) / Math.max(recentQuality.length, 1),
          qualityDistribution: this.calculateQualityDistribution(recentQuality)
        },
        search: {
          recent: recentSearches.length,
          avgResultsPerSearch: recentSearches.reduce((sum, s) => sum + (s.results_count || 0), 0) / Math.max(recentSearches.length, 1),
          avgProcessingTime: recentSearches.reduce((sum, s) => sum + (s.processing_time || 0), 0) / Math.max(recentSearches.length, 1)
        }
      },
      trends: {
        daily: this.calculateDailyTrends(recentTranscriptions, recentCompliance, recentQuality, recentSearches)
      }
    };

    res.json({
      success: true,
      data: dashboard
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao carregar dashboard de IA',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/analytics/performance
 * Métricas de performance dos serviços de IA
 */
router.get('/performance', async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    const days = parseInt(period.replace('d', ''));
    
    const aiDatabase = require('../database/aiDatabase');
    
    const [
      transcriptions,
      complianceAnalyses,
      qualityAnalyses,
      searches
    ] = await Promise.all([
      aiDatabase.getAllQuery(`
        SELECT processing_time, created_at, confidence, word_count 
        FROM transcriptions 
        WHERE created_at >= datetime('now', '-${days} days')
        ORDER BY created_at DESC
      `),
      aiDatabase.getAllQuery(`
        SELECT processing_time, created_at, compliance_score, risk_level 
        FROM compliance_analyses 
        WHERE created_at >= datetime('now', '-${days} days')
        ORDER BY created_at DESC
      `),
      aiDatabase.getAllQuery(`
        SELECT processing_time, created_at, overall_score, quality_level 
        FROM quality_analyses 
        WHERE created_at >= datetime('now', '-${days} days')
        ORDER BY created_at DESC
      `),
      aiDatabase.getAllQuery(`
        SELECT processing_time, created_at, results_count 
        FROM semantic_searches 
        WHERE created_at >= datetime('now', '-${days} days')
        ORDER BY created_at DESC
      `)
    ]);

    const performance = {
      transcription: {
        totalProcessed: transcriptions.length,
        avgProcessingTime: this.calculateAverage(transcriptions, 'processing_time'),
        avgConfidence: this.calculateAverage(transcriptions, 'confidence'),
        avgWordCount: this.calculateAverage(transcriptions, 'word_count'),
        throughput: transcriptions.length / days // por dia
      },
      compliance: {
        totalProcessed: complianceAnalyses.length,
        avgProcessingTime: this.calculateAverage(complianceAnalyses, 'processing_time'),
        avgScore: this.calculateAverage(complianceAnalyses, 'compliance_score'),
        riskDistribution: this.calculateRiskDistribution(complianceAnalyses),
        throughput: complianceAnalyses.length / days
      },
      quality: {
        totalProcessed: qualityAnalyses.length,
        avgProcessingTime: this.calculateAverage(qualityAnalyses, 'processing_time'),
        avgScore: this.calculateAverage(qualityAnalyses, 'overall_score'),
        qualityDistribution: this.calculateQualityDistribution(qualityAnalyses),
        throughput: qualityAnalyses.length / days
      },
      search: {
        totalSearches: searches.length,
        avgProcessingTime: this.calculateAverage(searches, 'processing_time'),
        avgResultsPerSearch: this.calculateAverage(searches, 'results_count'),
        searchVolume: searches.length / days
      }
    };

    res.json({
      success: true,
      data: performance
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao carregar métricas de performance',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/analytics/usage
 * Estatísticas de uso dos serviços de IA
 */
router.get('/usage', async (req, res) => {
  try {
    const aiDatabase = require('../database/aiDatabase');
    
    const [
      hourlyUsage,
      dailyUsage,
      weeklyUsage,
      topUsers,
      serviceUsage
    ] = await Promise.all([
      this.getHourlyUsage(aiDatabase),
      this.getDailyUsage(aiDatabase),
      this.getWeeklyUsage(aiDatabase),
      this.getTopUsers(aiDatabase),
      this.getServiceUsage(aiDatabase)
    ]);

    const usage = {
      patterns: {
        hourly: hourlyUsage,
        daily: dailyUsage,
        weekly: weeklyUsage
      },
      users: {
        top: topUsers,
        total: topUsers.length
      },
      services: serviceUsage
    };

    res.json({
      success: true,
      data: usage
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao carregar estatísticas de uso',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/analytics/costs
 * Estimativa de custos dos serviços de IA
 */
router.get('/costs', async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    const days = parseInt(period.replace('d', ''));
    
    const aiDatabase = require('../database/aiDatabase');
    
    const [transcriptions, complianceAnalyses, embeddings] = await Promise.all([
      aiDatabase.getAllQuery(`
        SELECT * FROM transcriptions 
        WHERE created_at >= datetime('now', '-${days} days')
      `),
      aiDatabase.getAllQuery(`
        SELECT * FROM compliance_analyses 
        WHERE created_at >= datetime('now', '-${days} days')
      `),
      aiDatabase.getAllQuery(`
        SELECT tokens FROM embeddings 
        WHERE created_at >= datetime('now', '-${days} days')
      `)
    ]);

    // Estimativas de custo (valores aproximados da OpenAI)
    const costs = {
      transcription: {
        totalMinutes: transcriptions.reduce((sum, t) => sum + (t.duration || 0), 0) / 60,
        estimatedCost: (transcriptions.reduce((sum, t) => sum + (t.duration || 0), 0) / 60) * 0.006, // $0.006 per minute
        totalRequests: transcriptions.length
      },
      textGeneration: {
        totalTokens: complianceAnalyses.length * 2000, // Estimativa média
        estimatedCost: (complianceAnalyses.length * 2000) * 0.00003, // GPT-4 pricing
        totalRequests: complianceAnalyses.length
      },
      embeddings: {
        totalTokens: embeddings.reduce((sum, e) => sum + (e.tokens || 0), 0),
        estimatedCost: embeddings.reduce((sum, e) => sum + (e.tokens || 0), 0) * 0.0000001, // Embedding pricing
        totalRequests: embeddings.length
      }
    };

    const totalCost = costs.transcription.estimatedCost + 
                     costs.textGeneration.estimatedCost + 
                     costs.embeddings.estimatedCost;

    res.json({
      success: true,
      data: {
        period: `${days} days`,
        breakdown: costs,
        total: {
          estimatedCost: totalCost,
          currency: 'USD',
          dailyAverage: totalCost / days,
          monthlyProjection: (totalCost / days) * 30
        }
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao calcular custos',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/analytics/process-recording
 * Processar gravação com todos os serviços de IA
 */
router.post('/process-recording', async (req, res) => {
  try {
    const { recordingId, services = ['transcription', 'compliance', 'quality', 'semantic'] } = req.body;

    if (!recordingId) {
      return res.status(400).json({
        success: false,
        message: 'ID da gravação é obrigatório'
      });
    }

    const queueManager = require('../services/queueManager');
    const jobs = {};

    // Processar serviços solicitados
    if (services.includes('transcription')) {
      jobs.transcription = await queueManager.addTranscriptionJob(recordingId);
    }

    if (services.includes('compliance')) {
      jobs.compliance = await queueManager.addComplianceJob(recordingId);
    }

    if (services.includes('quality')) {
      jobs.quality = await queueManager.addQualityAnalysisJob(recordingId);
    }

    if (services.includes('semantic')) {
      jobs.semantic = await queueManager.addSemanticProcessingJob(recordingId);
    }

    res.json({
      success: true,
      message: `${Object.keys(jobs).length} serviços de IA iniciados para a gravação`,
      data: {
        recordingId,
        jobs: Object.entries(jobs).map(([service, job]) => ({
          service,
          jobId: job.id,
          status: 'queued'
        }))
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao processar gravação',
      error: error.message
    });
  }
});

// Métodos auxiliares
function calculateRiskDistribution(analyses) {
  const distribution = { baixo: 0, medio: 0, alto: 0, critico: 0 };
  analyses.forEach(a => {
    if (a.risk_level && distribution.hasOwnProperty(a.risk_level)) {
      distribution[a.risk_level]++;
    }
  });
  return distribution;
}

function calculateQualityDistribution(analyses) {
  const distribution = { excelente: 0, boa: 0, regular: 0, baixa: 0, muito_baixa: 0 };
  analyses.forEach(a => {
    if (a.quality_level && distribution.hasOwnProperty(a.quality_level)) {
      distribution[a.quality_level]++;
    }
  });
  return distribution;
}

function calculateAverage(array, field) {
  if (array.length === 0) return 0;
  return array.reduce((sum, item) => sum + (item[field] || 0), 0) / array.length;
}

function calculateDailyTrends(transcriptions, compliance, quality, searches) {
  const trends = {};
  const today = new Date();
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];
    
    trends[dateStr] = {
      transcriptions: transcriptions.filter(t => t.created_at.startsWith(dateStr)).length,
      compliance: compliance.filter(c => c.created_at.startsWith(dateStr)).length,
      quality: quality.filter(q => q.created_at.startsWith(dateStr)).length,
      searches: searches.filter(s => s.created_at.startsWith(dateStr)).length
    };
  }
  
  return trends;
}

// Métodos auxiliares para estatísticas de uso
router.getHourlyUsage = async function(aiDatabase) {
  // Implementar análise por hora
  return {};
};

router.getDailyUsage = async function(aiDatabase) {
  // Implementar análise diária
  return {};
};

router.getWeeklyUsage = async function(aiDatabase) {
  // Implementar análise semanal
  return {};
};

router.getTopUsers = async function(aiDatabase) {
  // Implementar análise de usuários
  return [];
};

router.getServiceUsage = async function(aiDatabase) {
  // Implementar análise de uso por serviço
  return {};
};

module.exports = router;

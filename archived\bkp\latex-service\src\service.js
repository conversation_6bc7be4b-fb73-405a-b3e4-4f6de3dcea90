const express = require('express');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);
const app = express();
const PORT = process.env.PORT || 3001;
const BACKEND_URL = process.env.BACKEND_URL || 'http://backend:3000';

app.use(express.json());

// Diretórios para armazenamento de arquivos
const templatesDir = path.join(__dirname, '../templates');
const tempDir = path.join(__dirname, '../temp');
const outputDir = path.join(__dirname, '../../storage');

// Garantir que os diretórios existam
[templatesDir, tempDir, outputDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Inicializar os templates
initializeTemplates();

/**
 * Inicializa os templates LaTeX necessários
 */
function initializeTemplates() {
  const mainTemplatePath = path.join(templatesDir, 'report_template.tex');
  
  if (!fs.existsSync(mainTemplatePath)) {
    const mainTemplateContent = `\\documentclass[12pt,a4paper]{article}

\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}
\\usepackage[brazilian]{babel}
\\usepackage{lmodern}
\\usepackage{hyperref}
\\usepackage{graphicx}
\\usepackage{fancyhdr}
\\usepackage{lastpage}
\\usepackage{tabulary}
\\usepackage{booktabs}
\\usepackage{qrcode}
\\usepackage{datetime}
\\usepackage{xcolor}
\\usepackage{listings}
\\usepackage{enumitem}
\\usepackage{geometry}

\\geometry{a4paper, margin=2.5cm}
\\pagestyle{fancy}
\\fancyhf{}
\\fancyhead[L]{Sistema de Gravação de Tela para Cartório}
\\fancyhead[R]{\\today}
\\fancyfoot[C]{Página \\thepage\\ de \\pageref{LastPage}}
\\renewcommand{\\headrulewidth}{0.4pt}
\\renewcommand{\\footrulewidth}{0.4pt}

\\hypersetup{
  colorlinks=true,
  linkcolor=blue,
  urlcolor=blue
}

\\begin{document}

\\begin{center}
  \\LARGE\\textbf{Relatório Técnico de Gravação de Tela}\\\\[0.5cm]
  \\Large Para Uso em Cartório\\\\[0.5cm]
  \\large\\today
\\end{center}

\\section*{Identificação da Gravação}
\\begin{tabulary}{\\textwidth}{LL}
  \\textbf{ID da Gravação:} & $recording_id$ \\\\
  \\textbf{Data e Hora:} & $recording_timestamp$ \\\\
  \\textbf{Dispositivo:} & $device_info$ \\\\
  \\textbf{Orientação:} & $orientation$ \\\\
  \\textbf{Duração:} & $duration$ segundos \\\\
  \\textbf{Tamanho do Arquivo:} & $file_size$ MB \\\\
\\end{tabulary}

\\section*{Verificação de Integridade}
\\begin{tabulary}{\\textwidth}{LL}
  \\textbf{Hash SHA-256:} & \\texttt{$file_hash$} \\\\
\\end{tabulary}

\\vspace{0.5cm}
O hash acima é uma impressão digital única do arquivo de vídeo gerada utilizando o algoritmo SHA-256. Este hash garante a integridade do arquivo, permitindo verificar se o conteúdo do vídeo não foi alterado após sua criação.

\\section*{Informações de Consentimento}
\\begin{tabulary}{\\textwidth}{LL}
  \\textbf{Data e Hora do Consentimento:} & $consent_timestamp$ \\\\
  \\textbf{Endereço IP:} & $consent_ip$ \\\\
  \\textbf{User Agent:} & $consent_user_agent$ \\\\
\\end{tabulary}

\\vspace{0.5cm}
De acordo com a Lei Geral de Proteção de Dados (LGPD), Lei nº 13.709/2018, o consentimento explícito foi obtido antes da gravação, conforme exigido pelo Artigo 7º.

\\section*{Valor Probatório}
De acordo com o Artigo 369 do Código de Processo Civil brasileiro (Lei nº 13.105/2015), este registro eletrônico possui valor probatório, sendo considerado um documento hábil para provar fatos juridicamente relevantes. O hash criptográfico SHA-256 garante a integridade e autenticidade do conteúdo da gravação.

\\section*{Certificação Digital}
\\begin{center}
  \\qrcode[height=1.5in]{https://verificar.sistemadozero.com.br/verificar/$recording_id$}
  \\\\[0.5cm]
  \\small Escaneie o código QR acima para verificar a autenticidade deste documento ou acesse:\\\\
  \\small \\url{https://verificar.sistemadozero.com.br/verificar/$recording_id$}
\\end{center}

\\vfill
\\begin{center}
  \\line(1,0){250}\\\\
  Assinatura do Tabelião
\\end{center}

\\end{document}`;
    
    fs.writeFileSync(mainTemplatePath, mainTemplateContent);
    console.log('Template LaTeX inicializado com sucesso');
  }
}

// Rota para gerar PDF para uma gravação
app.post('/generate-report/:recordingId', async (req, res) => {
  const { recordingId } = req.params;
  
  try {
    // Obter dados da gravação do backend
    const recordingResponse = await axios.get(`${BACKEND_URL}/api/recordings/${recordingId}`);
    const recording = recordingResponse.data;
    
    if (!recording) {
      return res.status(404).json({ error: 'Gravação não encontrada' });
    }
    
    // Gerar PDF usando LaTeX
    const pdfFilePath = await generatePDF(recording);
    
    res.json({
      recording_id: recordingId,
      pdf_path: pdfFilePath,
      message: 'Relatório em PDF gerado com sucesso'
    });
  } catch (error) {
    console.error('Erro ao gerar relatório:', error.message);
    res.status(500).json({
      error: 'Erro ao gerar relatório',
      message: error.message
    });
  }
});

/**
 * Gera um PDF a partir dos dados da gravação usando LaTeX
 * @param {Object} recording - Dados da gravação
 * @returns {Promise<string>} - Caminho do arquivo PDF gerado
 */
async function generatePDF(recording) {
  const recordingId = recording.id;
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const baseFileName = `report-${recordingId}-${timestamp}`;
  const texFilePath = path.join(tempDir, `${baseFileName}.tex`);
  const pdfFileName = `${baseFileName}.pdf`;
  const pdfOutputPath = path.join(outputDir, pdfFileName);
  
  // Ler o template
  const templatePath = path.join(templatesDir, 'report_template.tex');
  let templateContent = fs.readFileSync(templatePath, 'utf8');
  
  // Processar metadados para o template
  const recordingTimestamp = new Date(recording.created_at).toLocaleString('pt-BR');
  const consentTimestamp = new Date(recording.consent_timestamp).toLocaleString('pt-BR');
  const fileSizeMB = (recording.file_size / (1024 * 1024)).toFixed(2);
  
  // Substituir variáveis no template
  templateContent = templateContent
    .replace(/\$recording_id\$/g, recording.id)
    .replace(/\$recording_timestamp\$/g, recordingTimestamp)
    .replace(/\$device_info\$/g, recording.device_info || 'Informação não disponível')
    .replace(/\$orientation\$/g, recording.orientation || 'Não especificada')
    .replace(/\$duration\$/g, recording.duration ? recording.duration.toString() : 'Não disponível')
    .replace(/\$file_size\$/g, fileSizeMB)
    .replace(/\$file_hash\$/g, recording.hash)
    .replace(/\$consent_timestamp\$/g, consentTimestamp)
    .replace(/\$consent_ip\$/g, recording.consent_ip)
    .replace(/\$consent_user_agent\$/g, recording.consent_user_agent || 'Não disponível');
  
  // Escrever o arquivo .tex
  fs.writeFileSync(texFilePath, templateContent);
  
  try {
    // Executar o pdflatex duas vezes para garantir referências corretas
    await execAsync(`cd ${tempDir} && pdflatex -interaction=nonstopmode ${texFilePath}`);
    await execAsync(`cd ${tempDir} && pdflatex -interaction=nonstopmode ${texFilePath}`);
    
    // Mover o PDF gerado para o diretório de saída
    const generatedPdfPath = texFilePath.replace('.tex', '.pdf');
    fs.copyFileSync(generatedPdfPath, pdfOutputPath);
    
    // Limpar arquivos temporários
    const tempFiles = fs.readdirSync(tempDir).filter(file => file.startsWith(baseFileName));
    tempFiles.forEach(file => {
      fs.unlinkSync(path.join(tempDir, file));
    });
    
    return pdfOutputPath;
  } catch (error) {
    console.error('Erro ao compilar LaTeX:', error);
    throw new Error(`Falha ao gerar o PDF: ${error.message}`);
  }
}

// Iniciar o serviço
app.listen(PORT, () => {
  console.log(`Serviço LaTeX rodando na porta ${PORT}`);
});

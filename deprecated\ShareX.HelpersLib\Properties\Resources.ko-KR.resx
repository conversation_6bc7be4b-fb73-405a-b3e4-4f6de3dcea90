﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ShapeType_RegionFreehand" xml:space="preserve">
    <value>자유형 영역</value>
  </data>
  <data name="ReplCodeMenuEntry_w_Current_week_name__Local_language_" xml:space="preserve">
    <value>현재 요일 (현지 언어)</value>
  </data>
  <data name="ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_" xml:space="preserve">
    <value>설정이 클립보드에 복사되었습니다.</value>
  </data>
  <data name="ImgurThumbnailType_Big_Square" xml:space="preserve">
    <value>큰 정사각형</value>
  </data>
  <data name="ReplCodeMenuEntry_s_Current_second" xml:space="preserve">
    <value>현재 초</value>
  </data>
  <data name="TextDestination_CustomTextUploader" xml:space="preserve">
    <value>사용자 정의 텍스트 업로더</value>
  </data>
  <data name="ProxyMethod_None" xml:space="preserve">
    <value>없음</value>
  </data>
  <data name="ReplCodeMenuEntry_mo_Current_month" xml:space="preserve">
    <value>현재 달</value>
  </data>
  <data name="CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___" xml:space="preserve">
    <value>CSS 탐색하기...</value>
  </data>
  <data name="HotkeyType_VideoThumbnailer" xml:space="preserve">
    <value>비디오 섬네일 생성기</value>
  </data>
  <data name="ShapeType_EffectBlur" xml:space="preserve">
    <value>흐림 효과</value>
  </data>
  <data name="AfterCaptureTasks_ShowQuickTaskMenu" xml:space="preserve">
    <value>간편 작업 메뉴 보이기</value>
  </data>
  <data name="CustomUploaderDestinationType_URLShortener" xml:space="preserve">
    <value>URL 단축기</value>
  </data>
  <data name="ReplCodeMenuEntry_uln_User_login_name" xml:space="preserve">
    <value>사용자 아이디</value>
  </data>
  <data name="HotkeyType_ImageEffects" xml:space="preserve">
    <value>이미지 효과</value>
  </data>
  <data name="ShapeType_DrawingImageScreen" xml:space="preserve">
    <value>스크린 화면을 그리기</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available" xml:space="preserve">
    <value>{0}의 새 버전을 사용할 수 있습니다</value>
  </data>
  <data name="AfterUploadTasks_ShowQRCode" xml:space="preserve">
    <value>QR코드 창 띄우기</value>
  </data>
  <data name="ShapeType_DrawingSpeechBalloon" xml:space="preserve">
    <value>말풍선 그리기</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date" xml:space="preserve">
    <value>최신 버전의 {0}입니다</value>
  </data>
  <data name="HotkeyType_Category_ScreenRecord" xml:space="preserve">
    <value>화면 녹화</value>
  </data>
  <data name="PastebinExpiration_H1" xml:space="preserve">
    <value>1시간</value>
  </data>
  <data name="HotkeyType_ScrollingCapture" xml:space="preserve">
    <value>스크롤 촬영</value>
  </data>
  <data name="ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all" xml:space="preserve">
    <value>자동 증가하는 소문자 영숫자 (대소문자 구분). 0으로 {n}만큼 패딩</value>
  </data>
  <data name="ReplCodeMenuEntry_t_Title_of_active_window" xml:space="preserve">
    <value>현재 창의 이름</value>
  </data>
  <data name="AfterCaptureTasks_SendImageToPrinter" xml:space="preserve">
    <value>이미지 인쇄하기</value>
  </data>
  <data name="ShapeType_RegionRectangle" xml:space="preserve">
    <value>직사각형 영역</value>
  </data>
  <data name="HotkeyType_ToggleActionsToolbar" xml:space="preserve">
    <value>액션 도구상자 토글</value>
  </data>
  <data name="AfterCaptureTasks_PerformActions" xml:space="preserve">
    <value>동작 실행하기</value>
  </data>
  <data name="ReplCodeMenuCategory_Date_and_Time" xml:space="preserve">
    <value>날짜 및 시각</value>
  </data>
  <data name="HotkeyType_ImageCombiner" xml:space="preserve">
    <value>이미지 취합기</value>
  </data>
  <data name="HotkeyType_RectangleTransparent" xml:space="preserve">
    <value>직사각형 영역 촬영 (투명하게)</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_" xml:space="preserve">
    <value>다운로드 완료.</value>
  </data>
  <data name="YouTubeVideoPrivacy_Private" xml:space="preserve">
    <value>비공개</value>
  </data>
  <data name="AfterUploadTasks_ShareURL" xml:space="preserve">
    <value>URL 공유하기</value>
  </data>
  <data name="CustomUploaderDestinationType_FileUploader" xml:space="preserve">
    <value>파일 업로더</value>
  </data>
  <data name="ReplCodeMenuEntry_h_Current_hour" xml:space="preserve">
    <value>현재 시</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_Update_check_failed" xml:space="preserve">
    <value>업데이트 확인 실패</value>
  </data>
  <data name="ReplCodeMenuEntry_ms_Current_millisecond" xml:space="preserve">
    <value>현재 밀리초</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Install" xml:space="preserve">
    <value>설치</value>
  </data>
  <data name="AfterCaptureTasks_UploadImageToHost" xml:space="preserve">
    <value>이미지를 업로드하기</value>
  </data>
  <data name="ReplCodeMenuEntry_ix_Auto_increment_hexadecimal" xml:space="preserve">
    <value>자동 증가하는 16진법 숫자. 0으로 {n}만큼 패딩</value>
  </data>
  <data name="CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__" xml:space="preserve">
    <value>청록: {0:0.0}%, 마젠타: {1:0.0}%, 노랑: {2:0.0}%, 검정: {3:0.0}%</value>
  </data>
  <data name="HotkeyType_FolderUpload" xml:space="preserve">
    <value>폴더 업로드</value>
  </data>
  <data name="ReplCodeMenuEntry_mi_Current_minute" xml:space="preserve">
    <value>현재 분</value>
  </data>
  <data name="ShapeType_EffectPixelate" xml:space="preserve">
    <value>모자이크 효과</value>
  </data>
  <data name="ReplCodeMenuEntry_d_Current_day" xml:space="preserve">
    <value>현재 일</value>
  </data>
  <data name="PastebinExpiration_D1" xml:space="preserve">
    <value>1일</value>
  </data>
  <data name="ShapeType_DrawingArrow" xml:space="preserve">
    <value>화살표 그리기</value>
  </data>
  <data name="PastebinPrivacy_Unlisted" xml:space="preserve">
    <value>비공개</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_update_is_available" xml:space="preserve">
    <value>업데이트 가능</value>
  </data>
  <data name="HotkeyType_Category_Upload" xml:space="preserve">
    <value>업로드</value>
  </data>
  <data name="Extensions_AddContextMenu_Cut" xml:space="preserve">
    <value>잘라내기</value>
  </data>
  <data name="FileExistAction_Cancel" xml:space="preserve">
    <value>저장하지 않기</value>
  </data>
  <data name="AfterCaptureTasks_CopyImageToClipboard" xml:space="preserve">
    <value>이미지를 클립보드에 복사하기</value>
  </data>
  <data name="PNGBitDepth_Bit32" xml:space="preserve">
    <value>32비트</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFActiveWindow" xml:space="preserve">
    <value>활성화 된 창 영역에서 화면 녹화 (GIF) 시작/중단</value>
  </data>
  <data name="HotkeyType_PrintScreen" xml:space="preserve">
    <value>전체 화면 촬영</value>
  </data>
  <data name="ImageEditorStartMode_Normal" xml:space="preserve">
    <value>기본</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFCustomRegion" xml:space="preserve">
    <value>미리 지정된 영역에서 화면 녹화 (GIF) 시작/중단</value>
  </data>
  <data name="HotkeyType_CustomRegion" xml:space="preserve">
    <value>미리 지정된 영역 촬영</value>
  </data>
  <data name="ReplCodeMenuCategory_Image" xml:space="preserve">
    <value>이미지</value>
  </data>
  <data name="PastebinExpiration_M10" xml:space="preserve">
    <value>10분</value>
  </data>
  <data name="RegionCaptureAction_SwapToolType" xml:space="preserve">
    <value>도구 타입 교체</value>
  </data>
  <data name="HotkeyType_RectangleRegion" xml:space="preserve">
    <value>직사각형 영역 촬영</value>
  </data>
  <data name="AfterCaptureTasks_DoOCR" xml:space="preserve">
    <value>텍스트 인식하기 (OCR)</value>
  </data>
  <data name="HotkeyType_ExitShareX" xml:space="preserve">
    <value>ShareX 종료</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_Portable" xml:space="preserve">
    <value>{0}의 새 버전을 사용할 수 있습니다.
업데이트 파일을 내려받을까요?</value>
  </data>
  <data name="Helpers_DownloadString_Download_failed_" xml:space="preserve">
    <value>다운로드 실패:</value>
  </data>
  <data name="ShapeType_DrawingTextOutline" xml:space="preserve">
    <value>텍스트(획) 그리기</value>
  </data>
  <data name="RegionCaptureAction_CaptureActiveMonitor" xml:space="preserve">
    <value>활성화 된 모니터 촬영</value>
  </data>
  <data name="ImgurThumbnailType_Small_Thumbnail" xml:space="preserve">
    <value>작은 섬네일</value>
  </data>
  <data name="PrintForm_LoadSettings_Print" xml:space="preserve">
    <value>인쇄</value>
  </data>
  <data name="GIFQuality_Bit4" xml:space="preserve">
    <value>16색 팔진트리 양자화</value>
  </data>
  <data name="AfterUploadTasks_ShowAfterUploadWindow" xml:space="preserve">
    <value>"업로드 뒤" 창 띄우기</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAdjective" xml:space="preserve">
    <value>랜덤 형용사</value>
  </data>
  <data name="FileDestination_CustomFileUploader" xml:space="preserve">
    <value>사용자 정의 파일 업로더</value>
  </data>
  <data name="LinearGradientMode_Vertical" xml:space="preserve">
    <value>세로</value>
  </data>
  <data name="ReplCodeMenuCategory_Random" xml:space="preserve">
    <value>랜덤</value>
  </data>
  <data name="CustomUploaderDestinationType_ImageUploader" xml:space="preserve">
    <value>이미지 업로더</value>
  </data>
  <data name="HotkeyType_HashCheck" xml:space="preserve">
    <value>해시 검사</value>
  </data>
  <data name="HotkeyType_ScreenRecorderActiveWindow" xml:space="preserve">
    <value>활성화 된 창 영역에서 화면 녹화 시작/중단</value>
  </data>
  <data name="ReplCodeMenuEntry_rn_Random_number_0_to_9" xml:space="preserve">
    <value>임의의 숫자 (0 ~ 9)</value>
  </data>
  <data name="HotkeyType_ClipboardUploadWithContentViewer" xml:space="preserve">
    <value>클립보드 내용을 뷰어로 보고 업로드</value>
  </data>
  <data name="YouTubeVideoPrivacy_Public" xml:space="preserve">
    <value>공개</value>
  </data>
  <data name="HSB_ToString_" xml:space="preserve">
    <value>색상: {0:0.0}°, 채도: {1:0.0}%, 밝기: {2:0.0}%</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_OK" xml:space="preserve">
    <value>확인</value>
  </data>
  <data name="HotkeyType_DragDropUpload" xml:space="preserve">
    <value>끌어서 업로드</value>
  </data>
  <data name="PastebinExpiration_N" xml:space="preserve">
    <value>만료되지 않음</value>
  </data>
  <data name="HotkeyType_StartScreenRecorder" xml:space="preserve">
    <value>마지막 영역으로 화면 녹화 (FFmpeg)</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Yes" xml:space="preserve">
    <value>네</value>
  </data>
  <data name="ReplCodeMenuEntry_mon_Current_month_name__Local_language_" xml:space="preserve">
    <value>현재 달 이름 (현지 언어)</value>
  </data>
  <data name="GIFQuality_Bit8" xml:space="preserve">
    <value>256색 팔진트리 양자화 (느리지만 더 나은 품질)</value>
  </data>
  <data name="ShapeType_DrawingImage" xml:space="preserve">
    <value>이미지 파일 그리기</value>
  </data>
  <data name="ScreenRecordGIFEncoding_NET" xml:space="preserve">
    <value>.NET (나쁜 품질)</value>
  </data>
  <data name="ReplCodeMenuEntry_ia_Auto_increment_alphanumeric" xml:space="preserve">
    <value>자동 증가하는 소문자 영숫자 (대소문자 미구분). 0으로 {n}만큼 패딩</value>
  </data>
  <data name="AfterCaptureTasks_AddImageEffects" xml:space="preserve">
    <value>이미지 효과 및 워터마크 추가하기</value>
  </data>
  <data name="AfterCaptureTasks_DeleteFile" xml:space="preserve">
    <value>로컬 파일 삭제하기</value>
  </data>
  <data name="ExportImportControl_Serialize_Export_failed_" xml:space="preserve">
    <value>내보내기 실패.</value>
  </data>
  <data name="ReplCodeMenuCategory_Computer" xml:space="preserve">
    <value>컴퓨터</value>
  </data>
  <data name="FileExistAction_UniqueName" xml:space="preserve">
    <value>파일 이름에 숫자 덧붙이기</value>
  </data>
  <data name="ImgurThumbnailType_Large_Thumbnail" xml:space="preserve">
    <value>큰 섬네일</value>
  </data>
  <data name="ReplCodeMenuEntry_yy_Current_year__2_digits_" xml:space="preserve">
    <value>현재 년도 (2자리)</value>
  </data>
  <data name="PNGBitDepth_Automatic" xml:space="preserve">
    <value>자동 감지</value>
  </data>
  <data name="ImageEditorStartMode_PreviousState" xml:space="preserve">
    <value>이전 상태</value>
  </data>
  <data name="ShapeType_RegionEllipse" xml:space="preserve">
    <value>타원 영역</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIF" xml:space="preserve">
    <value>화면 녹화 (GIF)</value>
  </data>
  <data name="YouTubeVideoPrivacy_Unlisted" xml:space="preserve">
    <value>미공개</value>
  </data>
  <data name="HotkeyType_Ruler" xml:space="preserve">
    <value>측정</value>
  </data>
  <data name="ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from" xml:space="preserve">
    <value>설정을 내려받을 URL</value>
  </data>
  <data name="ShapeType_DrawingFreehand" xml:space="preserve">
    <value>자유롭게 그리기</value>
  </data>
  <data name="ReplCodeMenuEntry_pm_Gets_AM_PM" xml:space="preserve">
    <value>AM/PM</value>
  </data>
  <data name="DirectoryNameEditor_EditValue_Browse_for_a_folder___" xml:space="preserve">
    <value>폴더 탐색하기...</value>
  </data>
  <data name="LinearGradientMode_BackwardDiagonal" xml:space="preserve">
    <value>역방향(＼) 대각선</value>
  </data>
  <data name="ShapeType_DrawingCursor" xml:space="preserve">
    <value>커서 그리기</value>
  </data>
  <data name="ImgurThumbnailType_Huge_Thumbnail" xml:space="preserve">
    <value>더 큰 섬네일</value>
  </data>
  <data name="LinearGradientMode_Horizontal" xml:space="preserve">
    <value>가로</value>
  </data>
  <data name="HotkeyType_AbortScreenRecording" xml:space="preserve">
    <value>화면 녹화 중단</value>
  </data>
  <data name="ReplCodeMenuEntry_y_Current_year" xml:space="preserve">
    <value>현재 년도</value>
  </data>
  <data name="PastebinExpiration_W2" xml:space="preserve">
    <value>2주</value>
  </data>
  <data name="ImageEditorStartMode_Fullscreen" xml:space="preserve">
    <value>전체화면</value>
  </data>
  <data name="AfterCaptureTasks_CopyFilePathToClipboard" xml:space="preserve">
    <value>파일 경로를 클립보드에 복사하기</value>
  </data>
  <data name="HotkeyType_ScreenRecorder" xml:space="preserve">
    <value>화면 녹화 (FFmpeg)</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFile" xml:space="preserve">
    <value>이미지를 파일로 저장하기</value>
  </data>
  <data name="ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_" xml:space="preserve">
    <value>확장자 없는 파일 경로 + "출력 파일 이름 확장자"</value>
  </data>
  <data name="URLSharingServices_GoogleImageSearch" xml:space="preserve">
    <value>구글 이미지 검색</value>
  </data>
  <data name="HotkeyType_IndexFolder" xml:space="preserve">
    <value>폴더 색인 만들기</value>
  </data>
  <data name="ReplCodeMenuEntry_unix_Unix_timestamp" xml:space="preserve">
    <value>유닉스 타임스탬프</value>
  </data>
  <data name="ScreenRecordGIFEncoding_FFmpeg" xml:space="preserve">
    <value>FFmpeg (좋은 품질)</value>
  </data>
  <data name="HotkeyType_TweetMessage" xml:space="preserve">
    <value>트윗 날리기</value>
  </data>
  <data name="HotkeyType_StopUploads" xml:space="preserve">
    <value>현재 진행중인 모든 업로드 중단</value>
  </data>
  <data name="AfterUploadTasks_OpenURL" xml:space="preserve">
    <value>URL 열기</value>
  </data>
  <data name="AfterCaptureTasks_AnnotateImage" xml:space="preserve">
    <value>이미지 편집기에서 열기</value>
  </data>
  <data name="MyPictureBox_LoadImageAsync_Loading_image___" xml:space="preserve">
    <value>이미지 로딩중...</value>
  </data>
  <data name="HotkeyType_LastRegion" xml:space="preserve">
    <value>마지막 영역 촬영</value>
  </data>
  <data name="Helpers_OpenFolder_Folder_not_exist_" xml:space="preserve">
    <value>다음 폴더가 존재하지 않습니다:</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_CurrentVersion" xml:space="preserve">
    <value>현재 버전</value>
  </data>
  <data name="FileDestination_Email" xml:space="preserve">
    <value>이메일</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_" xml:space="preserve">
    <value>{0}의 새 버전을 사용할 수 있습니다.
업데이트 파일을 내려받은 뒤 설치할까요?</value>
  </data>
  <data name="WavFileNameEditor_EditValue_Browse_for_a_sound_file___" xml:space="preserve">
    <value>사운드 파일 탐색...</value>
  </data>
  <data name="Helpers_OpenFile_File_not_exist_" xml:space="preserve">
    <value>다음 파일이 존재하지 않습니다:</value>
  </data>
  <data name="Helpers_BrowseFolder_Choose_folder" xml:space="preserve">
    <value>폴더 고르기</value>
  </data>
  <data name="ExportImportControl_Deserialize_Import_failed_" xml:space="preserve">
    <value>가져오기 실패.</value>
  </data>
  <data name="Extensions_AddContextMenu_Paste" xml:space="preserve">
    <value>붙여넣기</value>
  </data>
  <data name="LinearGradientMode_ForwardDiagonal" xml:space="preserve">
    <value>정방향(／) 대각선</value>
  </data>
  <data name="PNGBitDepth_Bit24" xml:space="preserve">
    <value>24비트</value>
  </data>
  <data name="HotkeyType_OpenImageHistory" xml:space="preserve">
    <value>이미지 히스토리 창 열기</value>
  </data>
  <data name="ReplCodeMenuCategory_Incremental" xml:space="preserve">
    <value>자동 증가</value>
  </data>
  <data name="AfterCaptureTasks_SaveThumbnailImageToFile" xml:space="preserve">
    <value>섬네일 이미지를 파일로 저장하기</value>
  </data>
  <data name="DownloaderForm_StartDownload_Downloading_" xml:space="preserve">
    <value>다운로드 중.</value>
  </data>
  <data name="RegionCaptureAction_RemoveShapeCancelCapture" xml:space="preserve">
    <value>모양 지우기 또는 촬영 취소</value>
  </data>
  <data name="ReplCodeMenuEntry_un_User_name" xml:space="preserve">
    <value>사용자 이름</value>
  </data>
  <data name="CodeMenu_Create_Close" xml:space="preserve">
    <value>닫기</value>
  </data>
  <data name="ShapeType_DrawingSticker" xml:space="preserve">
    <value>그리기: 스티커</value>
  </data>
  <data name="HotkeyType_QRCode" xml:space="preserve">
    <value>QR코드</value>
  </data>
  <data name="PastebinExpiration_W1" xml:space="preserve">
    <value>1주</value>
  </data>
  <data name="CustomUploaderDestinationType_URLSharingService" xml:space="preserve">
    <value>URL 공유 서비스</value>
  </data>
  <data name="ShapeType_EffectHighlight" xml:space="preserve">
    <value>강조 효과</value>
  </data>
  <data name="GIFQuality_Grayscale" xml:space="preserve">
    <value>회색조 256색 팔레트 양자화</value>
  </data>
  <data name="GIFQuality_Default" xml:space="preserve">
    <value>기본 .NET 인코딩 (빠르지만 그럭저럭인 품질)</value>
  </data>
  <data name="ReplCodeMenuEntry_rx_Random_hexadecimal" xml:space="preserve">
    <value>임의의 16진법 숫자. {n}만큼 반복</value>
  </data>
  <data name="PastebinPrivacy_Private" xml:space="preserve">
    <value>비밀 (회원 전용)</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>오류</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAnimal" xml:space="preserve">
    <value>랜덤 동물</value>
  </data>
  <data name="URLSharingServices_CustomURLSharingService" xml:space="preserve">
    <value>사용자 정의 URL 공유 서비스</value>
  </data>
  <data name="RegionCaptureAction_CaptureFullscreen" xml:space="preserve">
    <value>전체 화면 촬영</value>
  </data>
  <data name="ReplCodeMenuEntry_pn_Process_name_of_active_window" xml:space="preserve">
    <value>현재 창의 프로세스명</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Waiting_" xml:space="preserve">
    <value>대기중.</value>
  </data>
  <data name="HotkeyType_ImageEditor" xml:space="preserve">
    <value>이미지 편집기</value>
  </data>
  <data name="URLSharingServices_Email" xml:space="preserve">
    <value>이메일</value>
  </data>
  <data name="HotkeyType_OpenHistory" xml:space="preserve">
    <value>히스토리 창 열기</value>
  </data>
  <data name="ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric" xml:space="preserve">
    <value>자동 증가하는 {n}진법 영숫자 (1 &lt; n &lt; 63)</value>
  </data>
  <data name="HotkeyType_CaptureWebpage" xml:space="preserve">
    <value>웹 페이지 촬영</value>
  </data>
  <data name="RegionCaptureAction_CancelCapture" xml:space="preserve">
    <value>촬영 취소</value>
  </data>
  <data name="AfterCaptureTasks_ScanQRCode" xml:space="preserve">
    <value>QR코드 스캔</value>
  </data>
  <data name="HotkeyType_RectangleLight" xml:space="preserve">
    <value>직사각형 영역 촬영 (어둡지 않게)</value>
  </data>
  <data name="ProxyMethod_Automatic" xml:space="preserve">
    <value>자동</value>
  </data>
  <data name="HotkeyType_FileUpload" xml:space="preserve">
    <value>파일 업로드</value>
  </data>
  <data name="ReplCodeMenuEntry_guid_Random_guid" xml:space="preserve">
    <value>임의의 GUID</value>
  </data>
  <data name="ShapeType_DrawingLine" xml:space="preserve">
    <value>선분 그리기</value>
  </data>
  <data name="AfterCaptureTasks_ShowBeforeUploadWindow" xml:space="preserve">
    <value>"업로드 전" 창 띄우기</value>
  </data>
  <data name="AfterCaptureTasks_ShowInExplorer" xml:space="preserve">
    <value>탐색기 창에서 파일 보여주기</value>
  </data>
  <data name="ImageDestination_CustomImageUploader" xml:space="preserve">
    <value>사용자 정의 이미지 업로더</value>
  </data>
  <data name="HotkeyType_Category_ScreenCapture" xml:space="preserve">
    <value>화면 촬영</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_No" xml:space="preserve">
    <value>아니요</value>
  </data>
  <data name="HotkeyType_ActiveWindow" xml:space="preserve">
    <value>현재 창 촬영</value>
  </data>
  <data name="ShapeType_DrawingStep" xml:space="preserve">
    <value>단계 그리기</value>
  </data>
  <data name="ReplCodeMenuEntry_i_Auto_increment_number" xml:space="preserve">
    <value>자동 증가 카운터</value>
  </data>
  <data name="HotkeyType_ClipboardUpload" xml:space="preserve">
    <value>클립보드 내용 업로드</value>
  </data>
  <data name="ReplCodeMenuEntry_n_New_line" xml:space="preserve">
    <value>줄바꿈</value>
  </data>
  <data name="ReplCodeMenuEntry_mon2_Current_month_name__English_" xml:space="preserve">
    <value>현재 달 이름 (영어)</value>
  </data>
  <data name="HotkeyType_OpenScreenshotsFolder" xml:space="preserve">
    <value>스크린샷 폴더 열기</value>
  </data>
  <data name="ReplCodeMenuEntry_width_Gets_image_width" xml:space="preserve">
    <value>이미지 너비</value>
  </data>
  <data name="ReplCodeMenuEntry_w2_Current_week_name__English_" xml:space="preserve">
    <value>현재 요일 (영어)</value>
  </data>
  <data name="ExeFileNameEditor_EditValue_Browse_for_executable___" xml:space="preserve">
    <value>실행 파일 탐색하기...</value>
  </data>
  <data name="ImageDestination_FileUploader" xml:space="preserve">
    <value>파일 업로더</value>
  </data>
  <data name="ImageEditorStartMode_AutoSize" xml:space="preserve">
    <value>자동 조정</value>
  </data>
  <data name="HotkeyType_None" xml:space="preserve">
    <value>없음</value>
  </data>
  <data name="PNGBitDepth_Default" xml:space="preserve">
    <value>기본</value>
  </data>
  <data name="Helpers_CreateDirectoryIfNotExist_Create_failed_" xml:space="preserve">
    <value>폴더를 추가할 수 없습니다. 경로 설정을 확인해 주세요.</value>
  </data>
  <data name="ProxyMethod_Manual" xml:space="preserve">
    <value>직접 입력</value>
  </data>
  <data name="DownloaderForm_ChangeStatus_Status___0_" xml:space="preserve">
    <value>상태: {0}</value>
  </data>
  <data name="HotkeyType_StartScreenRecorderGIF" xml:space="preserve">
    <value>마지막 영역으로 화면 녹화 (GIF)</value>
  </data>
  <data name="ImgurThumbnailType_Small_Square" xml:space="preserve">
    <value>작은 정사각형</value>
  </data>
  <data name="HotkeyType_MonitorTest" xml:space="preserve">
    <value>모니터 테스트</value>
  </data>
  <data name="Extensions_AddContextMenu_Copy" xml:space="preserve">
    <value>복사</value>
  </data>
  <data name="AfterUploadTasks_UseURLShortener" xml:space="preserve">
    <value>URL 단축하기</value>
  </data>
  <data name="DownloaderForm_StartDownload_Cancel" xml:space="preserve">
    <value>취소</value>
  </data>
  <data name="HotkeyType_Category_Tools" xml:space="preserve">
    <value>도구</value>
  </data>
  <data name="FileDestination_SharedFolder" xml:space="preserve">
    <value>공유된 폴더</value>
  </data>
  <data name="HotkeyType_ActiveMonitor" xml:space="preserve">
    <value>현재 모니터 촬영</value>
  </data>
  <data name="DownloaderForm_StartDownload_Getting_file_size_" xml:space="preserve">
    <value>파일 크기 가져오는 중.</value>
  </data>
  <data name="HotkeyType_Category_Other" xml:space="preserve">
    <value>기타</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Filename___0_" xml:space="preserve">
    <value>파일명: {0}</value>
  </data>
  <data name="ShapeType_DrawingEllipse" xml:space="preserve">
    <value>타원 그리기</value>
  </data>
  <data name="HotkeyType_ColorPicker" xml:space="preserve">
    <value>색상 선택기</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>중단</value>
  </data>
  <data name="TextDestination_FileUploader" xml:space="preserve">
    <value>파일 업로더</value>
  </data>
  <data name="MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__" xml:space="preserve">
    <value>이미지 로딩중: {0}%</value>
  </data>
  <data name="ReplCodeMenuEntry_ra_Random_alphanumeric_char" xml:space="preserve">
    <value>임의의 알파벳 또는 숫자</value>
  </data>
  <data name="ObjectListView_ObjectListView_Value" xml:space="preserve">
    <value>값</value>
  </data>
  <data name="HotkeyType_DisableHotkeys" xml:space="preserve">
    <value>단축키 활성화/비활성화</value>
  </data>
  <data name="RegionCaptureAction_None" xml:space="preserve">
    <value>아무것도 하지 않기</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFileWithDialog" xml:space="preserve">
    <value>이미지를 다른 이름으로 파일로 저장...</value>
  </data>
  <data name="RegionCaptureAction_RemoveShape" xml:space="preserve">
    <value>모양 지우기</value>
  </data>
  <data name="ActionsCodeMenuEntry_FilePath_File_path" xml:space="preserve">
    <value>파일 경로</value>
  </data>
  <data name="SupportedLanguage_Automatic" xml:space="preserve">
    <value>자동</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Cancel" xml:space="preserve">
    <value>취소</value>
  </data>
  <data name="FolderSelectDialog_Title_Select_a_folder" xml:space="preserve">
    <value>폴더 선택</value>
  </data>
  <data name="HotkeyType_OpenMainWindow" xml:space="preserve">
    <value>메인 창 띄우기</value>
  </data>
  <data name="HotkeyType_ScreenColorPicker" xml:space="preserve">
    <value>화면 색상 선택기</value>
  </data>
  <data name="PrintTextForm_LoadSettings_Name___0___Size___1_" xml:space="preserve">
    <value>이름: {0}, 크기: {1}</value>
  </data>
  <data name="HotkeyType_AutoCapture" xml:space="preserve">
    <value>자동 촬영</value>
  </data>
  <data name="ShapeType_DrawingRectangle" xml:space="preserve">
    <value>직사각형 그리기</value>
  </data>
  <data name="ImageEditorStartMode_Maximized" xml:space="preserve">
    <value>최대화</value>
  </data>
  <data name="HotkeyType_ScreenRecorderCustomRegion" xml:space="preserve">
    <value>미리 지정된 영역에서 화면 녹화 시작/중단</value>
  </data>
  <data name="ScreenRecordGIFEncoding_OctreeQuantizer" xml:space="preserve">
    <value>팔진트리 양자화 (보통 품질)</value>
  </data>
  <data name="Helpers_BrowseFile_Choose_file" xml:space="preserve">
    <value>파일 고르기</value>
  </data>
  <data name="ReplCodeMenuEntry_height_Gets_image_height" xml:space="preserve">
    <value>이미지 높이</value>
  </data>
  <data name="PastebinExpiration_M1" xml:space="preserve">
    <value>1달</value>
  </data>
  <data name="ShapeType_DrawingTextBackground" xml:space="preserve">
    <value>텍스트(칠) 그리기</value>
  </data>
  <data name="UrlShortenerType_CustomURLShortener" xml:space="preserve">
    <value>사용자 정의 URL 단축기</value>
  </data>
  <data name="PastebinPrivacy_Public" xml:space="preserve">
    <value>공개</value>
  </data>
  <data name="FileExistAction_Overwrite" xml:space="preserve">
    <value>파일 덮어쓰기</value>
  </data>
  <data name="HotkeyType_ShortenURL" xml:space="preserve">
    <value>URL 단축</value>
  </data>
  <data name="CustomUploaderDestinationType_TextUploader" xml:space="preserve">
    <value>텍스트 업로더</value>
  </data>
  <data name="FileExistAction_Ask" xml:space="preserve">
    <value>어떻게 할지 물어보기</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_LatestVersion" xml:space="preserve">
    <value>최신 버전</value>
  </data>
  <data name="AfterCaptureTasks_ShowAfterCaptureWindow" xml:space="preserve">
    <value>"촬영 뒤" 창 띄우기</value>
  </data>
  <data name="HotkeyType_UploadText" xml:space="preserve">
    <value>텍스트 업로드</value>
  </data>
  <data name="ShapeType_ToolCrop" xml:space="preserve">
    <value>이미지 자르기(C)</value>
  </data>
  <data name="HotkeyType_UploadURL" xml:space="preserve">
    <value>URL 내용 업로드</value>
  </data>
  <data name="AfterUploadTasks_CopyURLToClipboard" xml:space="preserve">
    <value>URL을 클립보드에 복사하기</value>
  </data>
  <data name="ReplCodeMenuEntry_cn_Computer_name" xml:space="preserve">
    <value>컴퓨터 이름</value>
  </data>
  <data name="HotkeyType_StartAutoCapture" xml:space="preserve">
    <value>마지막 영역으로 자동 촬영</value>
  </data>
  <data name="ImgurThumbnailType_Medium_Thumbnail" xml:space="preserve">
    <value>중간 섬네일</value>
  </data>
  <data name="AfterCaptureTasks_CopyFileToClipboard" xml:space="preserve">
    <value>파일을 클립보드에 복사하기</value>
  </data>
  <data name="HotkeyType_Metadata" xml:space="preserve">
    <value />
  </data>
</root>
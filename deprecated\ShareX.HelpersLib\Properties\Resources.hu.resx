﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ReplCodeMenuEntry_w_Current_week_name__Local_language_" xml:space="preserve">
    <value>Jelenlegi hét neve (helyi nyelv)</value>
  </data>
  <data name="ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_" xml:space="preserve">
    <value>A beállítások vágólapra másolva.</value>
  </data>
  <data name="ReplCodeMenuEntry_s_Current_second" xml:space="preserve">
    <value>Jelenlegi másodperc</value>
  </data>
  <data name="TextDestination_CustomTextUploader" xml:space="preserve">
    <value>Egyedi szöveg feltöltő</value>
  </data>
  <data name="ProxyMethod_None" xml:space="preserve">
    <value>Nincs</value>
  </data>
  <data name="ReplCodeMenuEntry_mo_Current_month" xml:space="preserve">
    <value>Jelenlegi hónap</value>
  </data>
  <data name="CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___" xml:space="preserve">
    <value>CSS fájl tallózása...</value>
  </data>
  <data name="ReplCodeMenuEntry_uln_User_login_name" xml:space="preserve">
    <value>Felhasználó bejelentkezési név</value>
  </data>
  <data name="HotkeyType_ImageEffects" xml:space="preserve">
    <value>Kép effektek</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available" xml:space="preserve">
    <value>Egy újabb {0} verzió elérhető</value>
  </data>
  <data name="AfterUploadTasks_ShowQRCode" xml:space="preserve">
    <value>QR kód ablak mutatása</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date" xml:space="preserve">
    <value>{0} az elérhető legfrissebb</value>
  </data>
  <data name="ReplCodeMenuEntry_t_Title_of_active_window" xml:space="preserve">
    <value>Az aktív ablak címe</value>
  </data>
  <data name="AfterCaptureTasks_SendImageToPrinter" xml:space="preserve">
    <value>Kép nyomtatása</value>
  </data>
  <data name="AfterCaptureTasks_PerformActions" xml:space="preserve">
    <value>Műveletek elvégzése</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_" xml:space="preserve">
    <value>Letöltés befejezve.</value>
  </data>
  <data name="AfterUploadTasks_ShareURL" xml:space="preserve">
    <value>URL megosztása</value>
  </data>
  <data name="ReplCodeMenuEntry_h_Current_hour" xml:space="preserve">
    <value>Jelenlegi óra</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_Update_check_failed" xml:space="preserve">
    <value>A frissítés ellenőrzése nem sikerült</value>
  </data>
  <data name="ReplCodeMenuEntry_ms_Current_millisecond" xml:space="preserve">
    <value>Jelenlegi milliszekundum</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Install" xml:space="preserve">
    <value>Telepítés</value>
  </data>
  <data name="AfterCaptureTasks_UploadImageToHost" xml:space="preserve">
    <value>Kép feltöltése hosztra</value>
  </data>
  <data name="CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__" xml:space="preserve">
    <value>Cián: {0:0.0}%, Magenta: {1:0.0}%, Sárga: {2:0.0}%, Kulcs: {3:0.0}%</value>
  </data>
  <data name="HotkeyType_FolderUpload" xml:space="preserve">
    <value>Mappa feltöltése</value>
  </data>
  <data name="ReplCodeMenuEntry_mi_Current_minute" xml:space="preserve">
    <value>Jelenlegi perc</value>
  </data>
  <data name="ReplCodeMenuEntry_d_Current_day" xml:space="preserve">
    <value>Jelenlegi nap</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_update_is_available" xml:space="preserve">
    <value>Frissítés elérhető</value>
  </data>
  <data name="Extensions_AddContextMenu_Cut" xml:space="preserve">
    <value>Kivágás</value>
  </data>
  <data name="FileExistAction_Cancel" xml:space="preserve">
    <value>Ne mentsen</value>
  </data>
  <data name="AfterCaptureTasks_CopyImageToClipboard" xml:space="preserve">
    <value>Kép vágólapra helyezése</value>
  </data>
  <data name="HotkeyType_PrintScreen" xml:space="preserve">
    <value>Teljes képernyős képlopás</value>
  </data>
  <data name="HotkeyType_RectangleRegion" xml:space="preserve">
    <value>Téglalap régió képlopás</value>
  </data>
  <data name="Helpers_DownloadString_Download_failed_" xml:space="preserve">
    <value>Sikertelen letöltés:</value>
  </data>
  <data name="PrintForm_LoadSettings_Print" xml:space="preserve">
    <value>Nyomtatás</value>
  </data>
  <data name="GIFQuality_Bit4" xml:space="preserve">
    <value>Octree quantizáló 16 szín</value>
  </data>
  <data name="FileDestination_CustomFileUploader" xml:space="preserve">
    <value>Egyéni fájl feltöltő</value>
  </data>
  <data name="HotkeyType_HashCheck" xml:space="preserve">
    <value>Hash ellenőrzés</value>
  </data>
  <data name="ReplCodeMenuEntry_rn_Random_number_0_to_9" xml:space="preserve">
    <value>0-9 véletlen szám</value>
  </data>
  <data name="HotkeyType_ClipboardUploadWithContentViewer" xml:space="preserve">
    <value>Feltöltés vágólapról tartalom nézőkével</value>
  </data>
  <data name="HSB_ToString_" xml:space="preserve">
    <value>Színárnyalat: {0:0.0}°, Telítettség: {1:0.0}%, Fényerő: {2:0.0}%</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="HotkeyType_DragDropUpload" xml:space="preserve">
    <value>Húzd és vond feltöltés</value>
  </data>
  <data name="HotkeyType_StartScreenRecorder" xml:space="preserve">
    <value>Képernyő felvételének indítása a legutóbbi régióval</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Yes" xml:space="preserve">
    <value>Igen</value>
  </data>
  <data name="ReplCodeMenuEntry_mon_Current_month_name__Local_language_" xml:space="preserve">
    <value>Jelenlegi hónap neve (helyi nyelv)</value>
  </data>
  <data name="GIFQuality_Bit8" xml:space="preserve">
    <value>Octree quantizáló 256 szín (lassú enkódolás de szebb minőség)</value>
  </data>
  <data name="AfterCaptureTasks_AddImageEffects" xml:space="preserve">
    <value>Képeffekt / vízjel hozzáadása</value>
  </data>
  <data name="AfterCaptureTasks_DeleteFile" xml:space="preserve">
    <value>A fájl lokális törlése</value>
  </data>
  <data name="ExportImportControl_Serialize_Export_failed_" xml:space="preserve">
    <value>Sikertelen exportálás.</value>
  </data>
  <data name="FileExistAction_UniqueName" xml:space="preserve">
    <value>Számozás hozzáadása a fájlnévhez</value>
  </data>
  <data name="ReplCodeMenuEntry_yy_Current_year__2_digits_" xml:space="preserve">
    <value>Jelenlegi év (2 számjegy)</value>
  </data>
  <data name="HotkeyType_Ruler" xml:space="preserve">
    <value>Vonalzó</value>
  </data>
  <data name="ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from" xml:space="preserve">
    <value>A beállítások letöltése URL-ről</value>
  </data>
  <data name="ReplCodeMenuEntry_pm_Gets_AM_PM" xml:space="preserve">
    <value>AM/PM</value>
  </data>
  <data name="DirectoryNameEditor_EditValue_Browse_for_a_folder___" xml:space="preserve">
    <value>Mappa tallózása...</value>
  </data>
  <data name="ReplCodeMenuEntry_y_Current_year" xml:space="preserve">
    <value>Jelenlegi év</value>
  </data>
  <data name="AfterCaptureTasks_CopyFilePathToClipboard" xml:space="preserve">
    <value>Fájl elérési út vágólapra másolása</value>
  </data>
  <data name="HotkeyType_ScreenRecorder" xml:space="preserve">
    <value>Képernyő felvétele</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFile" xml:space="preserve">
    <value>Kép mentése fájlba</value>
  </data>
  <data name="ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_" xml:space="preserve">
    <value>Fájl elérési út kiterjesztés nélkül + "kimeneti fájlnév kiterjesztés"</value>
  </data>
  <data name="HotkeyType_IndexFolder" xml:space="preserve">
    <value>Index mappa</value>
  </data>
  <data name="ReplCodeMenuEntry_unix_Unix_timestamp" xml:space="preserve">
    <value>Unix időbélyeg</value>
  </data>
  <data name="HotkeyType_TweetMessage" xml:space="preserve">
    <value>Tweet üzenet</value>
  </data>
  <data name="HotkeyType_StopUploads" xml:space="preserve">
    <value>Az összes aktív feltöltés leállítása</value>
  </data>
  <data name="AfterUploadTasks_OpenURL" xml:space="preserve">
    <value>URL megnyitása</value>
  </data>
  <data name="AfterCaptureTasks_AnnotateImage" xml:space="preserve">
    <value>Megnyitás képszerkesztőben</value>
  </data>
  <data name="MyPictureBox_LoadImageAsync_Loading_image___" xml:space="preserve">
    <value>Kép betöltése...</value>
  </data>
  <data name="HotkeyType_LastRegion" xml:space="preserve">
    <value>Legutóbbi régió képlopás</value>
  </data>
  <data name="Helpers_OpenFolder_Folder_not_exist_" xml:space="preserve">
    <value>A mappa nem létezik:</value>
  </data>
  <data name="FileDestination_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_" xml:space="preserve">
    <value>Egy újabb verzió {0} elérhető.</value>
  </data>
  <data name="ExportImportControl_Deserialize_Import_failed_" xml:space="preserve">
    <value>Sikertelen importálás.</value>
  </data>
  <data name="Extensions_AddContextMenu_Paste" xml:space="preserve">
    <value>Beillesztés</value>
  </data>
  <data name="AfterCaptureTasks_SaveThumbnailImageToFile" xml:space="preserve">
    <value>Előnézeti kép fájlba mentése</value>
  </data>
  <data name="DownloaderForm_StartDownload_Downloading_" xml:space="preserve">
    <value>Letöltés.</value>
  </data>
  <data name="ReplCodeMenuEntry_un_User_name" xml:space="preserve">
    <value>Felhasználónév</value>
  </data>
  <data name="CodeMenu_Create_Close" xml:space="preserve">
    <value>Bezárás</value>
  </data>
  <data name="HotkeyType_QRCode" xml:space="preserve">
    <value>QR kód</value>
  </data>
  <data name="GIFQuality_Grayscale" xml:space="preserve">
    <value>Paletta quantizáló szürkeárnyalat 256 szín</value>
  </data>
  <data name="GIFQuality_Default" xml:space="preserve">
    <value>Alapértelmezett .NET enkódolás (gyors, de átlagos minőségű)</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Hiba</value>
  </data>
  <data name="ReplCodeMenuEntry_pn_Process_name_of_active_window" xml:space="preserve">
    <value>Az aktív ablak folyamatneve</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Waiting_" xml:space="preserve">
    <value>Várakozás.</value>
  </data>
  <data name="URLSharingServices_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="HotkeyType_RectangleLight" xml:space="preserve">
    <value>Téglalap régió képlopás (finom)</value>
  </data>
  <data name="ProxyMethod_Automatic" xml:space="preserve">
    <value>Automatikus</value>
  </data>
  <data name="HotkeyType_FileUpload" xml:space="preserve">
    <value>Fájl feltöltése</value>
  </data>
  <data name="ImageDestination_CustomImageUploader" xml:space="preserve">
    <value>Egyéni kép feltöltő</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_No" xml:space="preserve">
    <value>Nem</value>
  </data>
  <data name="HotkeyType_ActiveWindow" xml:space="preserve">
    <value>Aktív ablak képlopás</value>
  </data>
  <data name="ReplCodeMenuEntry_i_Auto_increment_number" xml:space="preserve">
    <value>Automatikusan növekvő szám</value>
  </data>
  <data name="HotkeyType_ClipboardUpload" xml:space="preserve">
    <value>Feltöltés vágólapról</value>
  </data>
  <data name="ReplCodeMenuEntry_n_New_line" xml:space="preserve">
    <value>Új sor</value>
  </data>
  <data name="ReplCodeMenuEntry_mon2_Current_month_name__English_" xml:space="preserve">
    <value>Jelenlegi hónap neve (angol)</value>
  </data>
  <data name="HotkeyType_OpenScreenshotsFolder" xml:space="preserve">
    <value>Képernyőmentések mappa megnyitása</value>
  </data>
  <data name="ReplCodeMenuEntry_width_Gets_image_width" xml:space="preserve">
    <value>Kép szélesség</value>
  </data>
  <data name="ReplCodeMenuEntry_w2_Current_week_name__English_" xml:space="preserve">
    <value>Jelenlegi hét neve (angol)</value>
  </data>
  <data name="ExeFileNameEditor_EditValue_Browse_for_executable___" xml:space="preserve">
    <value>Futtatható állomány tallózása...</value>
  </data>
  <data name="ImageDestination_FileUploader" xml:space="preserve">
    <value>Fájl feltöltő</value>
  </data>
  <data name="HotkeyType_None" xml:space="preserve">
    <value>Nincs</value>
  </data>
  <data name="Helpers_CreateDirectoryIfNotExist_Create_failed_" xml:space="preserve">
    <value>Nem sikerült a könyvtár létrehozása, kérlek ellenőrizd az elérési út beállításaid.</value>
  </data>
  <data name="ProxyMethod_Manual" xml:space="preserve">
    <value>Manuális</value>
  </data>
  <data name="DownloaderForm_ChangeStatus_Status___0_" xml:space="preserve">
    <value>Állapot: {0}</value>
  </data>
  <data name="Extensions_AddContextMenu_Copy" xml:space="preserve">
    <value>Másolás</value>
  </data>
  <data name="AfterUploadTasks_UseURLShortener" xml:space="preserve">
    <value>URL rövidítése</value>
  </data>
  <data name="DownloaderForm_StartDownload_Cancel" xml:space="preserve">
    <value>Mégsem</value>
  </data>
  <data name="FileDestination_SharedFolder" xml:space="preserve">
    <value>Megosztott mappa</value>
  </data>
  <data name="HotkeyType_ActiveMonitor" xml:space="preserve">
    <value>Aktív monitor képlopás</value>
  </data>
  <data name="DownloaderForm_StartDownload_Getting_file_size_" xml:space="preserve">
    <value>Fájlméret lekérdezése.</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Filename___0_" xml:space="preserve">
    <value>Fájlnév: {0}</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>Leállítás</value>
  </data>
  <data name="TextDestination_FileUploader" xml:space="preserve">
    <value>Fájl feltöltő</value>
  </data>
  <data name="MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__" xml:space="preserve">
    <value>Kép betöltése: {0}%</value>
  </data>
  <data name="ReplCodeMenuEntry_ra_Random_alphanumeric_char" xml:space="preserve">
    <value>Véletlenszerű alfanumerikus karakter</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFileWithDialog" xml:space="preserve">
    <value>Kép mentése fájlba másként...</value>
  </data>
  <data name="ActionsCodeMenuEntry_FilePath_File_path" xml:space="preserve">
    <value>Fájl elérési út</value>
  </data>
  <data name="SupportedLanguage_Automatic" xml:space="preserve">
    <value>Automatikus</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Cancel" xml:space="preserve">
    <value>Mégsem</value>
  </data>
  <data name="FolderSelectDialog_Title_Select_a_folder" xml:space="preserve">
    <value>Válassz egy mappát</value>
  </data>
  <data name="HotkeyType_ScreenColorPicker" xml:space="preserve">
    <value>Színszedő</value>
  </data>
  <data name="PrintTextForm_LoadSettings_Name___0___Size___1_" xml:space="preserve">
    <value>Név: {0}, Méret: {1}</value>
  </data>
  <data name="HotkeyType_AutoCapture" xml:space="preserve">
    <value>Auto képlopás</value>
  </data>
  <data name="ReplCodeMenuEntry_height_Gets_image_height" xml:space="preserve">
    <value>Kép magassága</value>
  </data>
  <data name="UrlShortenerType_CustomURLShortener" xml:space="preserve">
    <value>Egyedi URL rövidítő</value>
  </data>
  <data name="FileExistAction_Overwrite" xml:space="preserve">
    <value>Fájl felülírása</value>
  </data>
  <data name="FileExistAction_Ask" xml:space="preserve">
    <value>Kérdezze meg mit tegyen</value>
  </data>
  <data name="HotkeyType_UploadURL" xml:space="preserve">
    <value>Feltöltés URL-ből</value>
  </data>
  <data name="AfterUploadTasks_CopyURLToClipboard" xml:space="preserve">
    <value>URL másolása a vágólapra</value>
  </data>
  <data name="ReplCodeMenuEntry_cn_Computer_name" xml:space="preserve">
    <value>Számítógép neve</value>
  </data>
  <data name="HotkeyType_StartAutoCapture" xml:space="preserve">
    <value>Auto képlopás a legutóbbi régióval</value>
  </data>
  <data name="AfterCaptureTasks_CopyFileToClipboard" xml:space="preserve">
    <value>Fájl vágólapra helyezése</value>
  </data>
  <data name="HotkeyType_Metadata" xml:space="preserve">
    <value />
  </data>
</root>
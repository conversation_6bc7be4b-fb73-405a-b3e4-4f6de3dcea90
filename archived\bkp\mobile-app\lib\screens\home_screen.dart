import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../services/screen_recorder_service.dart';
import '../widgets/consent_dialog.dart';
import '../widgets/recording_controls.dart';
import '../widgets/orientation_selector.dart';
import 'settings_screen.dart';
import 'recordings_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScreenRecorderService _recorderService = ScreenRecorderService();
  bool _isRecording = false;
  String _orientation = 'vertical';
  
  @override
  void initState() {
    super.initState();
    _requestPermissions();
  }
  
  Future<void> _requestPermissions() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.request();
      final micStatus = await Permission.microphone.request();
      
      if (status != PermissionStatus.granted) {
        _showPermissionDeniedDialog('Armazenamento');
      }
      
      if (micStatus != PermissionStatus.granted) {
        _showPermissionDeniedDialog('Microfone');
      }
    }
  }
  
  void _showPermissionDeniedDialog(String permission) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Permissão Negada'),
        content: Text(
          'A permissão de $permission é necessária para o funcionamento correto do aplicativo.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }
  
  Future<void> _startRecording() async {
    // Exibir diálogo de consentimento LGPD
    final consent = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => ConsentDialog(),
    );
    
    if (consent != true) {
      return; // Usuário não consentiu
    }
    
    try {
      await _recorderService.startRecording(
        orientation: _orientation,
      );
      
      setState(() {
        _isRecording = true;
      });
      
      // Forçar modo retrato ou paisagem com base na orientação selecionada
      if (_orientation == 'vertical') {
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
        ]);
      } else {
        SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
      }
      
      // Mostrar notificação de gravação em andamento
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Gravação iniciada'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao iniciar gravação: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  Future<void> _stopRecording() async {
    try {
      final recordingFile = await _recorderService.stopRecording();
      
      setState(() {
        _isRecording = false;
      });
      
      // Permitir todas as orientações novamente
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      
      // Mostrar diálogo de sucesso com opção para enviar
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Gravação Concluída'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('A gravação foi salva com sucesso!'),
              SizedBox(height: 8),
              Text('Caminho: ${recordingFile.path}'),
              SizedBox(height: 8),
              Text('Tamanho: ${(recordingFile.lengthSync() / 1024 / 1024).toStringAsFixed(2)} MB'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Fechar'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                _uploadRecording(recordingFile);
              },
              child: Text('Enviar para o Servidor'),
            ),
          ],
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao parar gravação: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  Future<void> _uploadRecording(File recordingFile) async {
    try {
      final result = await _recorderService.uploadRecording(
        file: recordingFile,
        apiUrl: context.read<AppProvider>().apiUrl,
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Gravação enviada com sucesso! ID: ${result['id']}'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erro ao enviar gravação: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  void _setOrientation(String orientation) {
    setState(() {
      _orientation = orientation;
    });
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Gravador para Cartório'),
        actions: [
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => SettingsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Seletor de orientação
          OrientationSelector(
            currentOrientation: _orientation,
            onOrientationChanged: _setOrientation,
            isRecording: _isRecording,
          ),
          
          Expanded(
            child: Center(
              child: _isRecording
                  ? RecordingControls(
                      onStopRecording: _stopRecording,
                    )
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.videocam,
                          size: 80,
                          color: Theme.of(context).primaryColor,
                        ),
                        SizedBox(height: 20),
                        Text(
                          'Pronto para gravar',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        SizedBox(height: 10),
                        Text(
                          'Pressione o botão abaixo para iniciar a gravação',
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                        SizedBox(height: 30),
                        ElevatedButton.icon(
                          onPressed: _startRecording,
                          icon: Icon(Icons.fiber_manual_record),
                          label: Text('Iniciar Gravação'),
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                              horizontal: 24, 
                              vertical: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ],
      ),
      floatingActionButton: !_isRecording
          ? FloatingActionButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => RecordingsScreen(),
                  ),
                );
              },
              child: Icon(Icons.video_library),
              tooltip: 'Minhas Gravações',
            )
          : null,
    );
  }
}

﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnLoadedAssemblies.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnLoadedAssemblies.Location" type="System.Drawing.Point, System.Drawing">
    <value>328, 512</value>
  </data>
  <data name="btnLoadedAssemblies.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 23</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnLoadedAssemblies.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btnLoadedAssemblies.Text" xml:space="preserve">
    <value>Loaded assemblies</value>
  </data>
  <data name="&gt;&gt;btnLoadedAssemblies.Name" xml:space="preserve">
    <value>btnLoadedAssemblies</value>
  </data>
  <data name="&gt;&gt;btnLoadedAssemblies.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnLoadedAssemblies.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnLoadedAssemblies.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="btnCopyAll.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="btnCopyAll.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 512</value>
  </data>
  <data name="btnCopyAll.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 23</value>
  </data>
  <data name="btnCopyAll.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnCopyAll.Text" xml:space="preserve">
    <value>Copy all</value>
  </data>
  <data name="&gt;&gt;btnCopyAll.Name" xml:space="preserve">
    <value>btnCopyAll</value>
  </data>
  <data name="&gt;&gt;btnCopyAll.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnCopyAll.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnCopyAll.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="rtbDebug.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="rtbDebug.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 8</value>
  </data>
  <data name="rtbDebug.Size" type="System.Drawing.Size, System.Drawing">
    <value>744, 472</value>
  </data>
  <data name="rtbDebug.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="rtbDebug.WordWrap" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;rtbDebug.Name" xml:space="preserve">
    <value>rtbDebug</value>
  </data>
  <data name="&gt;&gt;rtbDebug.Type" xml:space="preserve">
    <value>System.Windows.Forms.RichTextBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;rtbDebug.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;rtbDebug.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="btnOpenLogFile.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="btnOpenLogFile.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="btnOpenLogFile.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 512</value>
  </data>
  <data name="btnOpenLogFile.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 23</value>
  </data>
  <data name="btnOpenLogFile.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="btnOpenLogFile.Text" xml:space="preserve">
    <value>Open log file...</value>
  </data>
  <data name="&gt;&gt;btnOpenLogFile.Name" xml:space="preserve">
    <value>btnOpenLogFile</value>
  </data>
  <data name="&gt;&gt;btnOpenLogFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnOpenLogFile.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnOpenLogFile.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="llRunningFrom.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="llRunningFrom.Location" type="System.Drawing.Point, System.Drawing">
    <value>68, 0</value>
  </data>
  <data name="llRunningFrom.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="llRunningFrom.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 13</value>
  </data>
  <data name="llRunningFrom.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;llRunningFrom.Name" xml:space="preserve">
    <value>llRunningFrom</value>
  </data>
  <data name="&gt;&gt;llRunningFrom.Type" xml:space="preserve">
    <value>System.Windows.Forms.LinkLabel, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;llRunningFrom.Parent" xml:space="preserve">
    <value>flpRunningFrom</value>
  </data>
  <data name="&gt;&gt;llRunningFrom.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="flpRunningFrom.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="lblRunningFrom.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblRunningFrom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lblRunningFrom.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="lblRunningFrom.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 13</value>
  </data>
  <data name="lblRunningFrom.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lblRunningFrom.Text" xml:space="preserve">
    <value>Startup path:</value>
  </data>
  <data name="&gt;&gt;lblRunningFrom.Name" xml:space="preserve">
    <value>lblRunningFrom</value>
  </data>
  <data name="&gt;&gt;lblRunningFrom.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblRunningFrom.Parent" xml:space="preserve">
    <value>flpRunningFrom</value>
  </data>
  <data name="&gt;&gt;lblRunningFrom.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="flpRunningFrom.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 488</value>
  </data>
  <data name="flpRunningFrom.Size" type="System.Drawing.Size, System.Drawing">
    <value>744, 16</value>
  </data>
  <data name="flpRunningFrom.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="flpRunningFrom.WrapContents" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;flpRunningFrom.Name" xml:space="preserve">
    <value>flpRunningFrom</value>
  </data>
  <data name="&gt;&gt;flpRunningFrom.Type" xml:space="preserve">
    <value>System.Windows.Forms.FlowLayoutPanel, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;flpRunningFrom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;flpRunningFrom.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="btnUploadLog.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="btnUploadLog.Location" type="System.Drawing.Point, System.Drawing">
    <value>486, 512</value>
  </data>
  <data name="btnUploadLog.Size" type="System.Drawing.Size, System.Drawing">
    <value>152, 23</value>
  </data>
  <data name="btnUploadLog.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="btnUploadLog.Text" xml:space="preserve">
    <value>Upload log...</value>
  </data>
  <data name="&gt;&gt;btnUploadLog.Name" xml:space="preserve">
    <value>btnUploadLog</value>
  </data>
  <data name="&gt;&gt;btnUploadLog.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnUploadLog.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnUploadLog.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>760, 541</value>
  </data>
  <data name="$this.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>500, 400</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Debug log</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>DebugForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
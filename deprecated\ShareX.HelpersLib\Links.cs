﻿#region License Information (GPL v3)

/*
    ShareX - A program that allows you to take screenshots and share any file type
    Copyright (c) 2007-2025 ShareX Team

    This program is free software; you can redistribute it and/or
    modify it under the terms of the GNU General Public License
    as published by the Free Software Foundation; either version 2
    of the License, or (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON><PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program; if not, write to the Free Software
    Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.

    Optionally you can also view the license at <http://www.gnu.org/licenses/>.
*/

#endregion License Information (GPL v3)

namespace ShareX.HelpersLib
{
    public static class Links
    {
        public const string Website = "https://getsharex.com";
        public const string Callback = Website + "/callback/";
        public const string Changelog = Website + "/changelog";
        public const string Donate = Website + "/donate";
        public const string PrivacyPolicy = Website + "/privacy-policy";
        public const string ImageEffects = Website + "/image-effects";
        public const string Actions = Website + "/actions";
        private const string Docs = Website + "/docs";
        public const string DocsCustomUploader = Docs + "/custom-uploader";
        public const string DocsKeybinds = Docs + "/keybinds";
        public const string DocsOCR = Docs + "/ocr";
        public const string DocsScrollingScreenshot = Docs + "/scrolling-screenshot";

        public const string GitHub = "https://github.com/ShareX/ShareX";
        public const string GitHubIssues = GitHub + "/issues?q=is%3Aissue";
        public const string Jaex = "https://github.com/Jaex";
        public const string McoreD = "https://github.com/McoreD";
        public const string Discord = "https://discord.gg/ShareX";
        public const string X = "https://x.com/ShareX";
        public const string XFollow = "https://x.com/intent/follow?screen_name=ShareX";
        public const string Reddit = "https://www.reddit.com/r/sharex";
        public const string Steam = "https://store.steampowered.com/app/400040/ShareX/";
        public const string MicrosoftStore = "https://apps.microsoft.com/detail/9nblggh4z1sp";
    }
}
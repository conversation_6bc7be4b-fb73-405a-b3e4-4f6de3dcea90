# Use Node.js 18 LTS como base
FROM node:18-alpine

# Instalar dependências do sistema para FFmpeg e Sharp
RUN apk add --no-cache \
    ffmpeg \
    python3 \
    make \
    g++ \
    vips-dev \
    libc6-compat

# Criar diretório da aplicação
WORKDIR /app

# Copiar arquivos de dependências
COPY package*.json ./

# Instalar dependências
RUN npm ci --only=production

# Copiar código fonte
COPY src/ ./src/

# Criar diretórios necessários
RUN mkdir -p /app/storage/temp /app/storage/ai-data /app/logs && \
    chmod -R 755 /app/storage /app/logs

# Definir variáveis de ambiente
ENV NODE_ENV=production
ENV PORT=3003

# Expor porta
EXPOSE 3003

# Comando para iniciar o serviço
CMD ["npm", "start"]

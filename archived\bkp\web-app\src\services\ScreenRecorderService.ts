import axios from 'axios';
import CryptoJS from 'crypto-js';

interface RecordingOptions {
  audio: boolean;
  video: boolean;
  systemAudio?: boolean;
}

class ScreenRecorderService {
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];
  private stream: MediaStream | null = null;
  private startTime: number = 0;
  private isRecording: boolean = false;
  private apiUrl: string;
  private authToken: string;
  
  constructor(apiUrl: string, authToken: string) {
    this.apiUrl = apiUrl;
    this.authToken = authToken;
  }
  
  /**
   * Solicita permissão e inicia a gravação de tela
   */
  async startRecording(options: RecordingOptions): Promise<boolean> {
    try {
      if (this.isRecording) {
        throw new Error('Já existe uma gravação em andamento');
      }
      
      const constraints: any = {
        video: {
          cursor: 'always'
        }
      };
      
      if (options.audio) {
        constraints.audio = {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        };
      }
      
      // Solicitar acesso à tela
      this.stream = await navigator.mediaDevices.getDisplayMedia(constraints);
      
      // Se quiser gravar áudio do sistema (só funciona em alguns navegadores)
      if (options.audio && options.systemAudio && 'getSettings' in this.stream.getVideoTracks()[0]) {
        const videoTrack = this.stream.getVideoTracks()[0];
        if (videoTrack.getSettings().displaySurface === 'monitor') {
          // Isso varia de acordo com o navegador e plataforma
          // Alguns navegadores não suportam captura de áudio do sistema
        }
      }
      
      // Se quiser gravar áudio do microfone
      if (options.audio) {
        const micStream = await navigator.mediaDevices.getUserMedia({ audio: true });
        micStream.getAudioTracks().forEach(track => {
          this.stream?.addTrack(track);
        });
      }
      
      // Configurar gravador
      this.recordedChunks = [];
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: 'video/webm; codecs=vp9'
      });
      
      // Eventos
      this.mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) {
          this.recordedChunks.push(e.data);
        }
      };
      
      // Iniciar gravação
      this.mediaRecorder.start(1000); // Cria chunks a cada 1 segundo
      this.startTime = Date.now();
      this.isRecording = true;
      
      // Monitorar quando o usuário interrompe o compartilhamento de tela
      const tracks = this.stream.getTracks();
      tracks.forEach(track => {
        track.onended = () => {
          this.stopRecording();
        };
      });
      
      return true;
    } catch (error) {
      console.error('Erro ao iniciar gravação:', error);
      await this.releaseMediaDevices();
      return false;
    }
  }
  
  /**
   * Para a gravação de tela
   */
  async stopRecording(): Promise<Blob | null> {
    return new Promise((resolve) => {
      if (!this.mediaRecorder || this.mediaRecorder.state === 'inactive') {
        this.releaseMediaDevices();
        resolve(null);
        return;
      }
      
      this.mediaRecorder.onstop = async () => {
        const blob = new Blob(this.recordedChunks, {
          type: 'video/webm'
        });
        
        await this.releaseMediaDevices();
        this.isRecording = false;
        resolve(blob);
      };
      
      this.mediaRecorder.stop();
    });
  }
  
  /**
   * Libera os recursos de mídia
   */
  private async releaseMediaDevices() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => {
        track.stop();
      });
      this.stream = null;
    }
    
    this.mediaRecorder = null;
  }
  
  /**
   * Gera um hash SHA-256 para o blob
   */
  async generateHash(blob: Blob): Promise<string> {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.readAsArrayBuffer(blob);
      reader.onloadend = function () {
        const arrayBuffer = reader.result as ArrayBuffer;
        const hash = CryptoJS.SHA256(CryptoJS.lib.WordArray.create(arrayBuffer));
        resolve(hash.toString());
      };
    });
  }
  
  /**
   * Faz upload da gravação para o servidor
   */
  async uploadRecording(
    blob: Blob, 
    hash: string, 
    consent: boolean, 
    metadata: any
  ): Promise<boolean> {
    try {
      // Criar formulário de dados para upload
      const formData = new FormData();
      formData.append('recording', blob, `recording-${Date.now()}.webm`);
      formData.append('hash', hash);
      formData.append('consent', String(consent));
      formData.append('metadata', JSON.stringify(metadata));
      
      // Enviar para o servidor
      const response = await axios.post(
        `${this.apiUrl}/api/recordings/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            'Authorization': `Bearer ${this.authToken}`
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / (progressEvent.total || 100)
            );
            console.log(`Upload: ${percentCompleted}%`);
          }
        }
      );
      
      return response.status === 200;
    } catch (error) {
      console.error('Erro ao fazer upload da gravação:', error);
      return false;
    }
  }
  
  /**
   * Verifica se o navegador suporta gravação de tela
   */
  checkBrowserSupport(): boolean {
    return (
      navigator.mediaDevices && 
      'getDisplayMedia' in navigator.mediaDevices
    );
  }
  
  /**
   * Retorna o tempo de gravação atual em milissegundos
   */
  getRecordingTime(): number {
    if (!this.isRecording) return 0;
    return Date.now() - this.startTime;
  }
  
  /**
   * Verifica se está gravando atualmente
   */
  getIsRecording(): boolean {
    return this.isRecording;
  }
  
  /**
   * Pausa a gravação (se suportado pelo navegador)
   */
  pauseRecording(): boolean {
    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      try {
        this.mediaRecorder.pause();
        return true;
      } catch (error) {
        console.error('Erro ao pausar gravação:', error);
        return false;
      }
    }
    return false;
  }
  
  /**
   * Retoma a gravação (se suportado pelo navegador)
   */
  resumeRecording(): boolean {
    if (this.mediaRecorder && this.mediaRecorder.state === 'paused') {
      try {
        this.mediaRecorder.resume();
        return true;
      } catch (error) {
        console.error('Erro ao retomar gravação:', error);
        return false;
      }
    }
    return false;
  }
}

export default ScreenRecorderService;

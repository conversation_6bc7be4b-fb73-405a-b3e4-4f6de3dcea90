import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  late TextEditingController _apiUrlController;
  
  @override
  void initState() {
    super.initState();
    _apiUrlController = TextEditingController(
      text: context.read<AppProvider>().apiUrl,
    );
  }
  
  @override
  void dispose() {
    _apiUrlController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final appProvider = Provider.of<AppProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Configurações'),
      ),
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Configurações do Servidor',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 16),
                  TextField(
                    controller: _apiUrlController,
                    decoration: InputDecoration(
                      labelText: 'URL da API',
                      helperText: 'Exemplo: http://10.0.2.2:3000',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () async {
                      final newUrl = _apiUrlController.text.trim();
                      if (newUrl.isNotEmpty) {
                        await appProvider.updateApiUrl(newUrl);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('URL da API atualizada com sucesso!'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      }
                    },
                    child: Text('Salvar'),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Aparência',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 16),
                  SwitchListTile(
                    title: Text('Tema Escuro'),
                    subtitle: Text('Alternar entre tema claro e escuro'),
                    value: appProvider.themeMode == ThemeMode.dark,
                    onChanged: (_) {
                      appProvider.toggleTheme();
                    },
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Sobre o Aplicativo',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 16),
                  ListTile(
                    title: Text('Versão'),
                    subtitle: Text('1.0.0'),
                    leading: Icon(Icons.info_outline),
                  ),
                  Divider(),
                  ListTile(
                    title: Text('Sistema do Zero'),
                    subtitle: Text('Todos os direitos reservados'),
                    leading: Icon(Icons.copyright),
                  ),
                  Divider(),
                  ListTile(
                    title: Text('Política de Privacidade'),
                    subtitle: Text('Leia nossa política de privacidade'),
                    leading: Icon(Icons.privacy_tip_outlined),
                    onTap: () {
                      // Abrir política de privacidade
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

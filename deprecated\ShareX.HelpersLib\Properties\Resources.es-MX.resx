﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ShapeType_RegionFreehand" xml:space="preserve">
    <value>Forma libre</value>
  </data>
  <data name="ReplCodeMenuEntry_w_Current_week_name__Local_language_" xml:space="preserve">
    <value>Año actual (idioma local)</value>
  </data>
  <data name="ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_" xml:space="preserve">
    <value>Configuración copiada al portapapeles.</value>
  </data>
  <data name="ImgurThumbnailType_Big_Square" xml:space="preserve">
    <value>Cuadro grande</value>
  </data>
  <data name="ReplCodeMenuEntry_s_Current_second" xml:space="preserve">
    <value>Segundo actual</value>
  </data>
  <data name="TextDestination_CustomTextUploader" xml:space="preserve">
    <value>Servidor personalizado de texto</value>
  </data>
  <data name="ProxyMethod_None" xml:space="preserve">
    <value>Ninguno</value>
  </data>
  <data name="ReplCodeMenuEntry_mo_Current_month" xml:space="preserve">
    <value>Mes actual</value>
  </data>
  <data name="CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___" xml:space="preserve">
    <value>Buscar hoja de estilo en cascada...</value>
  </data>
  <data name="Extensions_AddContextMenu_Redo" xml:space="preserve">
    <value>Rehacer</value>
  </data>
  <data name="HotkeyType_VideoThumbnailer" xml:space="preserve">
    <value>Generador de miniaturas de video</value>
  </data>
  <data name="ShapeType_EffectBlur" xml:space="preserve">
    <value>Desenfocado (B)</value>
  </data>
  <data name="AfterCaptureTasks_ShowQuickTaskMenu" xml:space="preserve">
    <value>Mostrar menú de tarea rápida</value>
  </data>
  <data name="CustomUploaderDestinationType_URLShortener" xml:space="preserve">
    <value>Acortador de enlaces</value>
  </data>
  <data name="ReplCodeMenuEntry_uln_User_login_name" xml:space="preserve">
    <value>Nombre de acceso</value>
  </data>
  <data name="HotkeyType_ImageEffects" xml:space="preserve">
    <value>Efectos de imágenes</value>
  </data>
  <data name="ShapeType_DrawingImageScreen" xml:space="preserve">
    <value>Imagen (pantalla)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available" xml:space="preserve">
    <value>Hay una nueva versión de {0}</value>
  </data>
  <data name="AfterUploadTasks_ShowQRCode" xml:space="preserve">
    <value>Mostrar ventana de código QR</value>
  </data>
  <data name="ShapeType_DrawingSpeechBalloon" xml:space="preserve">
    <value>Globo de diálogo (S)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date" xml:space="preserve">
    <value>{0} está actualizado</value>
  </data>
  <data name="HotkeyType_Category_ScreenRecord" xml:space="preserve">
    <value>Grabación de pantalla</value>
  </data>
  <data name="PastebinExpiration_H1" xml:space="preserve">
    <value>1 hora</value>
  </data>
  <data name="HotkeyType_ScrollingCapture" xml:space="preserve">
    <value>Captura de desplazamiento</value>
  </data>
  <data name="ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all" xml:space="preserve">
    <value>Incremento alfanumérico automático con distinción de mayúsculas. Rellenar con ceros a la izq. con {n}</value>
  </data>
  <data name="ReplCodeMenuEntry_t_Title_of_active_window" xml:space="preserve">
    <value>Título de la ventana</value>
  </data>
  <data name="AfterCaptureTasks_SendImageToPrinter" xml:space="preserve">
    <value>Imprimir imagen</value>
  </data>
  <data name="ShapeType_RegionRectangle" xml:space="preserve">
    <value>Rectángulo</value>
  </data>
  <data name="HotkeyType_ToggleActionsToolbar" xml:space="preserve">
    <value>Alternar la barra de herramientas de acciones</value>
  </data>
  <data name="AfterCaptureTasks_PerformActions" xml:space="preserve">
    <value>Realizar acciones</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfCanvas" xml:space="preserve">
    <value>Porcentaje de lienzo</value>
  </data>
  <data name="ReplCodeMenuCategory_Date_and_Time" xml:space="preserve">
    <value>Fecha y hora</value>
  </data>
  <data name="HotkeyType_ImageCombiner" xml:space="preserve">
    <value>Acoplador de imágenes</value>
  </data>
  <data name="HotkeyType_RectangleTransparent" xml:space="preserve">
    <value>Capturar región (transparente)</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_" xml:space="preserve">
    <value>Descarga completa.</value>
  </data>
  <data name="YouTubeVideoPrivacy_Private" xml:space="preserve">
    <value>Privado</value>
  </data>
  <data name="AfterUploadTasks_ShareURL" xml:space="preserve">
    <value>Compartir enlace</value>
  </data>
  <data name="CustomUploaderDestinationType_FileUploader" xml:space="preserve">
    <value>Servidor de archivos</value>
  </data>
  <data name="ReplCodeMenuEntry_h_Current_hour" xml:space="preserve">
    <value>Hora actual</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_Update_check_failed" xml:space="preserve">
    <value>La comprobación de actualizaciones falló</value>
  </data>
  <data name="ReplCodeMenuEntry_ms_Current_millisecond" xml:space="preserve">
    <value>Milisegundo actual</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Install" xml:space="preserve">
    <value>Instalar</value>
  </data>
  <data name="AfterCaptureTasks_UploadImageToHost" xml:space="preserve">
    <value>Subir imagen a servidor</value>
  </data>
  <data name="ReplCodeMenuEntry_ix_Auto_increment_hexadecimal" xml:space="preserve">
    <value>Incremento hexadecimal automático. Rellenar con ceros a la izq. con {n}</value>
  </data>
  <data name="CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__" xml:space="preserve">
    <value>Cian: {0:0.0} %, Magenta: {1:0.0} %, Amarillo: {2:0.0} %, Negro: {3:0.0} %</value>
  </data>
  <data name="HotkeyType_FolderUpload" xml:space="preserve">
    <value>Subir carpeta</value>
  </data>
  <data name="ReplCodeMenuEntry_mi_Current_minute" xml:space="preserve">
    <value>Minuto actual</value>
  </data>
  <data name="ShapeType_EffectPixelate" xml:space="preserve">
    <value>Pixelar (P)</value>
  </data>
  <data name="ReplCodeMenuEntry_d_Current_day" xml:space="preserve">
    <value>Día actual</value>
  </data>
  <data name="PastebinExpiration_D1" xml:space="preserve">
    <value>1 día</value>
  </data>
  <data name="ShapeType_DrawingArrow" xml:space="preserve">
    <value>Flecha (A)</value>
  </data>
  <data name="ShapeType_DrawingSmartEraser" xml:space="preserve">
    <value>Borrador inteligente</value>
  </data>
  <data name="PastebinPrivacy_Unlisted" xml:space="preserve">
    <value>Sin listar</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_update_is_available" xml:space="preserve">
    <value>Actualización disponible</value>
  </data>
  <data name="HotkeyType_Category_Upload" xml:space="preserve">
    <value>Subir</value>
  </data>
  <data name="Extensions_AddContextMenu_Cut" xml:space="preserve">
    <value>Cortar</value>
  </data>
  <data name="FileExistAction_Cancel" xml:space="preserve">
    <value>No guardar</value>
  </data>
  <data name="AfterCaptureTasks_CopyImageToClipboard" xml:space="preserve">
    <value>Copiar imagen al portapapeles</value>
  </data>
  <data name="PNGBitDepth_Bit32" xml:space="preserve">
    <value>32 bit</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFActiveWindow" xml:space="preserve">
    <value>Iniciar o detener grabación de pantalla (GIF) usando la región de ventana activa</value>
  </data>
  <data name="HotkeyType_PrintScreen" xml:space="preserve">
    <value>Capturar pantalla completa</value>
  </data>
  <data name="ImageEditorStartMode_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFCustomRegion" xml:space="preserve">
    <value>Iniciar o detener grabación de pantalla (GIF) usando una región predefinida</value>
  </data>
  <data name="HotkeyType_CustomRegion" xml:space="preserve">
    <value>Capturar región predefinida</value>
  </data>
  <data name="ReplCodeMenuCategory_Image" xml:space="preserve">
    <value>Imagen</value>
  </data>
  <data name="PastebinExpiration_M10" xml:space="preserve">
    <value>10 minutos</value>
  </data>
  <data name="RegionCaptureAction_SwapToolType" xml:space="preserve">
    <value>Cambiar tipo de herramienta</value>
  </data>
  <data name="HotkeyType_RectangleRegion" xml:space="preserve">
    <value>Capturar región</value>
  </data>
  <data name="AfterCaptureTasks_DoOCR" xml:space="preserve">
    <value>Reconocer texto (OCR)</value>
  </data>
  <data name="HotkeyType_ExitShareX" xml:space="preserve">
    <value>Salir de ShareX</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_Portable" xml:space="preserve">
    <value>Hay una nueva versión de {0}.
¿Desea descargarla?</value>
  </data>
  <data name="Helpers_DownloadString_Download_failed_" xml:space="preserve">
    <value>La descarga falló:</value>
  </data>
  <data name="ShapeType_DrawingTextOutline" xml:space="preserve">
    <value>Texto con contorno (O)</value>
  </data>
  <data name="RegionCaptureAction_CaptureActiveMonitor" xml:space="preserve">
    <value>Capturar monitor activo</value>
  </data>
  <data name="ImgurThumbnailType_Small_Thumbnail" xml:space="preserve">
    <value>Miniatura pequeña</value>
  </data>
  <data name="PrintForm_LoadSettings_Print" xml:space="preserve">
    <value>Imprimir</value>
  </data>
  <data name="GIFQuality_Bit4" xml:space="preserve">
    <value>Cuantificador Octree de 16 colores</value>
  </data>
  <data name="AfterUploadTasks_ShowAfterUploadWindow" xml:space="preserve">
    <value>Mostrar ventana de "Después de subir"</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAdjective" xml:space="preserve">
    <value>Adjetivo aleatorio (inglés)</value>
  </data>
  <data name="Extensions_AddContextMenu_SelectAll" xml:space="preserve">
    <value>Seleccionar todo</value>
  </data>
  <data name="FileDestination_CustomFileUploader" xml:space="preserve">
    <value>Servidor personalizado de archivos</value>
  </data>
  <data name="LinearGradientMode_Vertical" xml:space="preserve">
    <value>Vertical</value>
  </data>
  <data name="ReplCodeMenuCategory_Random" xml:space="preserve">
    <value>Aleatorio</value>
  </data>
  <data name="CustomUploaderDestinationType_ImageUploader" xml:space="preserve">
    <value>Servidor de imágenes</value>
  </data>
  <data name="HotkeyType_HashCheck" xml:space="preserve">
    <value>Verificación de hash</value>
  </data>
  <data name="HotkeyType_ScreenRecorderActiveWindow" xml:space="preserve">
    <value>Iniciar o detener grabación de pantalla una región de ventana activa</value>
  </data>
  <data name="ReplCodeMenuEntry_rn_Random_number_0_to_9" xml:space="preserve">
    <value>Número aleatorio del 0 al 9. Repetir con {n}</value>
  </data>
  <data name="HotkeyType_ClipboardUploadWithContentViewer" xml:space="preserve">
    <value>Subir desde el portapapeles con visor de contenido</value>
  </data>
  <data name="YouTubeVideoPrivacy_Public" xml:space="preserve">
    <value>Público</value>
  </data>
  <data name="HSB_ToString_" xml:space="preserve">
    <value>Tono: {0:0.0}°, Saturación: {1:0.0} %, Brillo: {2:0.0} %</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_OK" xml:space="preserve">
    <value>Aceptar</value>
  </data>
  <data name="HotkeyType_DragDropUpload" xml:space="preserve">
    <value>Subir con arrastrar y soltar</value>
  </data>
  <data name="PastebinExpiration_N" xml:space="preserve">
    <value>Nunca</value>
  </data>
  <data name="HotkeyType_StartScreenRecorder" xml:space="preserve">
    <value>Iniciar o detener grabación de pantalla usando la última región</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Yes" xml:space="preserve">
    <value>Sí</value>
  </data>
  <data name="HotkeyType_ImageThumbnailer" xml:space="preserve">
    <value>Generador de miniaturas de imágenes</value>
  </data>
  <data name="ReplCodeMenuEntry_mon_Current_month_name__Local_language_" xml:space="preserve">
    <value>Nombre del mes actual (idioma local)</value>
  </data>
  <data name="GIFQuality_Bit8" xml:space="preserve">
    <value>Cuantificador Octree de 256 colores (codificación lenta pero de mejor calidad)</value>
  </data>
  <data name="ShapeType_DrawingImage" xml:space="preserve">
    <value>Imagen (archivo)</value>
  </data>
  <data name="ScreenRecordGIFEncoding_NET" xml:space="preserve">
    <value>.NET (calidad baja)</value>
  </data>
  <data name="ReplCodeMenuEntry_ia_Auto_increment_alphanumeric" xml:space="preserve">
    <value>Incremento alfanumérico automático sin distinción de mayúsculas. Rellenar con ceros a la izq. con {n}</value>
  </data>
  <data name="AfterCaptureTasks_AddImageEffects" xml:space="preserve">
    <value>Agregar efectos o marca de agua</value>
  </data>
  <data name="AfterCaptureTasks_DeleteFile" xml:space="preserve">
    <value>Eliminar archivo localmente</value>
  </data>
  <data name="ExportImportControl_Serialize_Export_failed_" xml:space="preserve">
    <value>La exportación falló.</value>
  </data>
  <data name="ReplCodeMenuCategory_Computer" xml:space="preserve">
    <value>Equipo</value>
  </data>
  <data name="FileExistAction_UniqueName" xml:space="preserve">
    <value>Anexar número al nombre de archivo</value>
  </data>
  <data name="ImgurThumbnailType_Large_Thumbnail" xml:space="preserve">
    <value>Miniatura grande</value>
  </data>
  <data name="ReplCodeMenuEntry_yy_Current_year__2_digits_" xml:space="preserve">
    <value>Año actual (2 dígitos)</value>
  </data>
  <data name="PNGBitDepth_Automatic" xml:space="preserve">
    <value>Detectar automáticamente</value>
  </data>
  <data name="ImageEditorStartMode_PreviousState" xml:space="preserve">
    <value>Estado anterior</value>
  </data>
  <data name="ShapeType_RegionEllipse" xml:space="preserve">
    <value>Elipse</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIF" xml:space="preserve">
    <value>Iniciar o detener grabación de pantalla (GIF) usando una región personalizada</value>
  </data>
  <data name="YouTubeVideoPrivacy_Unlisted" xml:space="preserve">
    <value>Sin listar</value>
  </data>
  <data name="ObjectListView_ObjectListView_Name" xml:space="preserve">
    <value>Nombre</value>
  </data>
  <data name="ReplCodeMenuCategory_Window" xml:space="preserve">
    <value>Ventana</value>
  </data>
  <data name="HotkeyType_Ruler" xml:space="preserve">
    <value>Regla</value>
  </data>
  <data name="ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from" xml:space="preserve">
    <value>Enlace de descarga de configuración</value>
  </data>
  <data name="ShapeType_DrawingFreehand" xml:space="preserve">
    <value>Forma libre (F)</value>
  </data>
  <data name="ReplCodeMenuEntry_pm_Gets_AM_PM" xml:space="preserve">
    <value>a. m. o p. m.</value>
  </data>
  <data name="DirectoryNameEditor_EditValue_Browse_for_a_folder___" xml:space="preserve">
    <value>Buscar carpeta...</value>
  </data>
  <data name="LinearGradientMode_BackwardDiagonal" xml:space="preserve">
    <value>Diagonal inversa</value>
  </data>
  <data name="ShapeType_DrawingCursor" xml:space="preserve">
    <value>Puntero</value>
  </data>
  <data name="ImgurThumbnailType_Huge_Thumbnail" xml:space="preserve">
    <value>Miniatura enorme</value>
  </data>
  <data name="LinearGradientMode_Horizontal" xml:space="preserve">
    <value>Horizontal</value>
  </data>
  <data name="HotkeyType_AbortScreenRecording" xml:space="preserve">
    <value>Interrumpir grabación de pantalla</value>
  </data>
  <data name="ReplCodeMenuEntry_y_Current_year" xml:space="preserve">
    <value>Año actual</value>
  </data>
  <data name="PastebinExpiration_W2" xml:space="preserve">
    <value>2 semanas</value>
  </data>
  <data name="ImageEditorStartMode_Fullscreen" xml:space="preserve">
    <value>Pantalla completa</value>
  </data>
  <data name="AfterCaptureTasks_CopyFilePathToClipboard" xml:space="preserve">
    <value>Copiar ubicación al portapapeles</value>
  </data>
  <data name="HotkeyType_ScreenRecorder" xml:space="preserve">
    <value>Iniciar o detener grabación de pantalla usando una región personalizada</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFile" xml:space="preserve">
    <value>Guardar imagen</value>
  </data>
  <data name="ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_" xml:space="preserve">
    <value>Ubicación del archivo sin extensión + "Extensión de archivo final"</value>
  </data>
  <data name="URLSharingServices_GoogleImageSearch" xml:space="preserve">
    <value>Buscar con Google Imágenes</value>
  </data>
  <data name="HotkeyType_IndexFolder" xml:space="preserve">
    <value>Indizar carpeta</value>
  </data>
  <data name="ReplCodeMenuEntry_unix_Unix_timestamp" xml:space="preserve">
    <value>Marca de tiempo Unix</value>
  </data>
  <data name="ScreenRecordGIFEncoding_FFmpeg" xml:space="preserve">
    <value>FFmpeg (calidad buena)</value>
  </data>
  <data name="HotkeyType_TweetMessage" xml:space="preserve">
    <value>Tuitear mensaje</value>
  </data>
  <data name="DrawImageSizeMode_DontResize" xml:space="preserve">
    <value>No redimensionar</value>
  </data>
  <data name="HotkeyType_StopUploads" xml:space="preserve">
    <value>Detener todas las subidas en curso</value>
  </data>
  <data name="AfterUploadTasks_OpenURL" xml:space="preserve">
    <value>Abrir enlace</value>
  </data>
  <data name="AfterCaptureTasks_AnnotateImage" xml:space="preserve">
    <value>Abrir en el editor de imágenes</value>
  </data>
  <data name="MyPictureBox_LoadImageAsync_Loading_image___" xml:space="preserve">
    <value>Cargando imagen...</value>
  </data>
  <data name="HotkeyType_LastRegion" xml:space="preserve">
    <value>Capturar última región</value>
  </data>
  <data name="Helpers_OpenFolder_Folder_not_exist_" xml:space="preserve">
    <value>La carpeta no existe:</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_CurrentVersion" xml:space="preserve">
    <value>Versión actual</value>
  </data>
  <data name="FileDestination_Email" xml:space="preserve">
    <value>Correo electrónico</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_" xml:space="preserve">
    <value>Hay una nueva versión de {0}.
¿Desea descargarla e instalarla?</value>
  </data>
  <data name="WavFileNameEditor_EditValue_Browse_for_a_sound_file___" xml:space="preserve">
    <value>Buscar archivo de sonido...</value>
  </data>
  <data name="Helpers_OpenFile_File_not_exist_" xml:space="preserve">
    <value>El archivo no existe:</value>
  </data>
  <data name="Helpers_BrowseFolder_Choose_folder" xml:space="preserve">
    <value>Elegir carpeta</value>
  </data>
  <data name="ExportImportControl_Deserialize_Import_failed_" xml:space="preserve">
    <value>La importación falló.</value>
  </data>
  <data name="Extensions_AddContextMenu_Delete" xml:space="preserve">
    <value>Eliminar</value>
  </data>
  <data name="Extensions_AddContextMenu_Paste" xml:space="preserve">
    <value>Pegar</value>
  </data>
  <data name="HotkeyType_QRCodeDecodeFromScreen" xml:space="preserve">
    <value>Código QR (Decodificar desde la pantalla)</value>
  </data>
  <data name="LinearGradientMode_ForwardDiagonal" xml:space="preserve">
    <value>Diagonal</value>
  </data>
  <data name="PNGBitDepth_Bit24" xml:space="preserve">
    <value>24 bit</value>
  </data>
  <data name="ReplCodeMenuEntry_wy_Week_of_year" xml:space="preserve">
    <value>Semana del año</value>
  </data>
  <data name="DrawImageSizeMode_AbsoluteSize" xml:space="preserve">
    <value>Tamaño absoluto</value>
  </data>
  <data name="HotkeyType_OpenImageHistory" xml:space="preserve">
    <value>Abrir ventana de historial de imagen</value>
  </data>
  <data name="ReplCodeMenuCategory_Incremental" xml:space="preserve">
    <value>Incremento</value>
  </data>
  <data name="RandomEmojiRepeatUsingN" xml:space="preserve">
    <value>Emojis aleatorios. Usar {n} para repetir</value>
  </data>
  <data name="AfterCaptureTasks_SaveThumbnailImageToFile" xml:space="preserve">
    <value>Guardar miniatura</value>
  </data>
  <data name="DownloaderForm_StartDownload_Downloading_" xml:space="preserve">
    <value>Descargando.</value>
  </data>
  <data name="RegionCaptureAction_RemoveShapeCancelCapture" xml:space="preserve">
    <value>Eliminar forma o cancelar captura</value>
  </data>
  <data name="ReplCodeMenuEntry_un_User_name" xml:space="preserve">
    <value>Nombre de usuario</value>
  </data>
  <data name="ShapeType_DrawingMagnify" xml:space="preserve">
    <value>Ampliar</value>
  </data>
  <data name="CodeMenu_Create_Close" xml:space="preserve">
    <value>Cerrar</value>
  </data>
  <data name="ShapeType_DrawingSticker" xml:space="preserve">
    <value>Calcomanía</value>
  </data>
  <data name="HotkeyType_QRCode" xml:space="preserve">
    <value>Código QR</value>
  </data>
  <data name="PastebinExpiration_W1" xml:space="preserve">
    <value>1 semana</value>
  </data>
  <data name="CustomUploaderDestinationType_URLSharingService" xml:space="preserve">
    <value>Servicio para compartir enlaces</value>
  </data>
  <data name="ShapeType_EffectHighlight" xml:space="preserve">
    <value>Resaltar (H)</value>
  </data>
  <data name="GIFQuality_Grayscale" xml:space="preserve">
    <value>Cuantificador de paleta de 256 niveles de gris</value>
  </data>
  <data name="GIFQuality_Default" xml:space="preserve">
    <value>Codificación predefinida de .NET (rápida pero con calidad media)</value>
  </data>
  <data name="ReplCodeMenuEntry_rx_Random_hexadecimal" xml:space="preserve">
    <value>Carácter hexadecimal aleatorio. Repetir con {n}</value>
  </data>
  <data name="PastebinPrivacy_Private" xml:space="preserve">
    <value>Privado (solo miembros)</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAnimal" xml:space="preserve">
    <value>Nombre de animal aleatorio (inglés)</value>
  </data>
  <data name="URLSharingServices_CustomURLSharingService" xml:space="preserve">
    <value>Servicio personalizado para compartir enlaces</value>
  </data>
  <data name="RegionCaptureAction_CaptureFullscreen" xml:space="preserve">
    <value>Capturar pantalla completa</value>
  </data>
  <data name="ReplCodeMenuEntry_pn_Process_name_of_active_window" xml:space="preserve">
    <value>Nombre del proceso de la ventana activa</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Waiting_" xml:space="preserve">
    <value>En espera.</value>
  </data>
  <data name="HotkeyType_ImageEditor" xml:space="preserve">
    <value>Editor de imágenes</value>
  </data>
  <data name="URLSharingServices_Email" xml:space="preserve">
    <value>Correo electrónico</value>
  </data>
  <data name="HotkeyType_OpenHistory" xml:space="preserve">
    <value>Abrir ventana de historial</value>
  </data>
  <data name="ShapeType_ToolSelect" xml:space="preserve">
    <value>Seleccionar y mover (M)</value>
  </data>
  <data name="ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric" xml:space="preserve">
    <value>Incremento automático en base {n} usando caracteres alfanuméricos (1 &lt; n &lt; 63)</value>
  </data>
  <data name="HotkeyType_CaptureWebpage" xml:space="preserve">
    <value>Capturar página web</value>
  </data>
  <data name="RegionCaptureAction_CancelCapture" xml:space="preserve">
    <value>Cancelar captura</value>
  </data>
  <data name="AfterCaptureTasks_ScanQRCode" xml:space="preserve">
    <value>Escanear código QR</value>
  </data>
  <data name="HotkeyType_RectangleLight" xml:space="preserve">
    <value>Capturar región (simple)</value>
  </data>
  <data name="ProxyMethod_Automatic" xml:space="preserve">
    <value>Automático</value>
  </data>
  <data name="HotkeyType_FileUpload" xml:space="preserve">
    <value>Subir archivo</value>
  </data>
  <data name="ReplCodeMenuEntry_guid_Random_guid" xml:space="preserve">
    <value>Identificador único universal (GUID) aleatorio</value>
  </data>
  <data name="ShapeType_DrawingLine" xml:space="preserve">
    <value>Línea (L)</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_value" xml:space="preserve">
    <value>Copiar valor</value>
  </data>
  <data name="AfterCaptureTasks_ShowBeforeUploadWindow" xml:space="preserve">
    <value>Mostrar ventana de "Antes de subir"</value>
  </data>
  <data name="AfterCaptureTasks_ShowInExplorer" xml:space="preserve">
    <value>Mostrar archivo en el Explorador de archivos</value>
  </data>
  <data name="ImageDestination_CustomImageUploader" xml:space="preserve">
    <value>Servidor personalizado de imágenes</value>
  </data>
  <data name="HotkeyType_Category_ScreenCapture" xml:space="preserve">
    <value>Capturar pantalla</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="HotkeyType_ActiveWindow" xml:space="preserve">
    <value>Capturar ventana activa</value>
  </data>
  <data name="ShapeType_DrawingStep" xml:space="preserve">
    <value>Paso (I)</value>
  </data>
  <data name="ReplCodeMenuEntry_i_Auto_increment_number" xml:space="preserve">
    <value>Incremento automático de número. Rellenar con ceros a la izq. con {n}</value>
  </data>
  <data name="HotkeyType_ClipboardUpload" xml:space="preserve">
    <value>Subir desde el portapapeles</value>
  </data>
  <data name="ReplCodeMenuEntry_n_New_line" xml:space="preserve">
    <value>Nueva línea</value>
  </data>
  <data name="ReplCodeMenuEntry_mon2_Current_month_name__English_" xml:space="preserve">
    <value>Nombre del mes actual (inglés)</value>
  </data>
  <data name="HotkeyType_OpenScreenshotsFolder" xml:space="preserve">
    <value>Abrir carpeta de capturas de pantalla</value>
  </data>
  <data name="ReplCodeMenuEntry_width_Gets_image_width" xml:space="preserve">
    <value>Ancho</value>
  </data>
  <data name="ReplCodeMenuEntry_w2_Current_week_name__English_" xml:space="preserve">
    <value>Nombre de la semana actual (inglés)</value>
  </data>
  <data name="ExeFileNameEditor_EditValue_Browse_for_executable___" xml:space="preserve">
    <value>Buscar archivo ejecutable...</value>
  </data>
  <data name="ImageDestination_FileUploader" xml:space="preserve">
    <value>Servidor de archivos</value>
  </data>
  <data name="ImageEditorStartMode_AutoSize" xml:space="preserve">
    <value>Tamaño automático</value>
  </data>
  <data name="HotkeyType_None" xml:space="preserve">
    <value>Ninguno</value>
  </data>
  <data name="PNGBitDepth_Default" xml:space="preserve">
    <value>Predefinida</value>
  </data>
  <data name="Helpers_CreateDirectoryIfNotExist_Create_failed_" xml:space="preserve">
    <value>No se pudo crear la carpeta.</value>
  </data>
  <data name="ProxyMethod_Manual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="DownloaderForm_ChangeStatus_Status___0_" xml:space="preserve">
    <value>Estado: {0}</value>
  </data>
  <data name="HotkeyType_StartScreenRecorderGIF" xml:space="preserve">
    <value>Iniciar o detener grabación de pantalla (GIF) usando la última región</value>
  </data>
  <data name="ImgurThumbnailType_Small_Square" xml:space="preserve">
    <value>Cuadro pequeño</value>
  </data>
  <data name="HotkeyType_MonitorTest" xml:space="preserve">
    <value>Prueba de pantalla</value>
  </data>
  <data name="Extensions_AddContextMenu_Copy" xml:space="preserve">
    <value>Copiar</value>
  </data>
  <data name="AfterUploadTasks_UseURLShortener" xml:space="preserve">
    <value>Acortar enlace</value>
  </data>
  <data name="ReplCodeMenuEntry_rf_Random_line_from_file" xml:space="preserve">
    <value>Línea al azar de un archivo. Usar {ubicación} para indicar el archivo</value>
  </data>
  <data name="DownloaderForm_StartDownload_Cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="HotkeyType_Category_Tools" xml:space="preserve">
    <value>Herramientas</value>
  </data>
  <data name="FileDestination_SharedFolder" xml:space="preserve">
    <value>Carpeta compartida</value>
  </data>
  <data name="HotkeyType_ActiveMonitor" xml:space="preserve">
    <value>Capturar monitor activo</value>
  </data>
  <data name="DownloaderForm_StartDownload_Getting_file_size_" xml:space="preserve">
    <value>Determinando tamaño.</value>
  </data>
  <data name="HotkeyType_Category_Other" xml:space="preserve">
    <value>Otro</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Filename___0_" xml:space="preserve">
    <value>Nombre de archivo: {0}</value>
  </data>
  <data name="ShapeType_DrawingEllipse" xml:space="preserve">
    <value>Elipse (E)</value>
  </data>
  <data name="HotkeyType_ColorPicker" xml:space="preserve">
    <value>Selector de color</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>Detener</value>
  </data>
  <data name="TextDestination_FileUploader" xml:space="preserve">
    <value>Servidor de archivos</value>
  </data>
  <data name="MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__" xml:space="preserve">
    <value>Cargando imagen: {0} %</value>
  </data>
  <data name="ReplCodeMenuEntry_ra_Random_alphanumeric_char" xml:space="preserve">
    <value>Carácter alfanumérico aleatorio. Repetir con {n}</value>
  </data>
  <data name="ObjectListView_ObjectListView_Value" xml:space="preserve">
    <value>Valor</value>
  </data>
  <data name="HotkeyType_DisableHotkeys" xml:space="preserve">
    <value>Habilitar o deshabilitar atajos de teclado</value>
  </data>
  <data name="RegionCaptureAction_None" xml:space="preserve">
    <value>No hacer nada</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFileWithDialog" xml:space="preserve">
    <value>Guardar imagen como...</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_name" xml:space="preserve">
    <value>Copiar nombre</value>
  </data>
  <data name="RegionCaptureAction_RemoveShape" xml:space="preserve">
    <value>Eliminar forma</value>
  </data>
  <data name="ActionsCodeMenuEntry_FilePath_File_path" xml:space="preserve">
    <value>Ubicación del archivo</value>
  </data>
  <data name="SupportedLanguage_Automatic" xml:space="preserve">
    <value>Automático</value>
  </data>
  <data name="HotkeyType_VideoConverter" xml:space="preserve">
    <value>Conversor de video</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Cancel" xml:space="preserve">
    <value>Cancelar</value>
  </data>
  <data name="FolderSelectDialog_Title_Select_a_folder" xml:space="preserve">
    <value>Elegir carpeta</value>
  </data>
  <data name="HotkeyType_OpenMainWindow" xml:space="preserve">
    <value>Abrir ventana principal</value>
  </data>
  <data name="HotkeyType_ScreenColorPicker" xml:space="preserve">
    <value>Pantalla de selector de color</value>
  </data>
  <data name="PrintTextForm_LoadSettings_Name___0___Size___1_" xml:space="preserve">
    <value>Nombre: {0}, Tamaño: {1}</value>
  </data>
  <data name="HotkeyType_AutoCapture" xml:space="preserve">
    <value>Captura automática</value>
  </data>
  <data name="ShapeType_DrawingRectangle" xml:space="preserve">
    <value>Rectángulo (R)</value>
  </data>
  <data name="ImageEditorStartMode_Maximized" xml:space="preserve">
    <value>Maximizado</value>
  </data>
  <data name="HotkeyType_ScreenRecorderCustomRegion" xml:space="preserve">
    <value>Iniciar o detener grabación de pantalla usando una región predefinida</value>
  </data>
  <data name="ScreenRecordGIFEncoding_OctreeQuantizer" xml:space="preserve">
    <value>Cuantificador Octree (calidad media)</value>
  </data>
  <data name="Helpers_BrowseFile_Choose_file" xml:space="preserve">
    <value>Elegir archivo</value>
  </data>
  <data name="ReplCodeMenuEntry_height_Gets_image_height" xml:space="preserve">
    <value>Alto</value>
  </data>
  <data name="PastebinExpiration_M1" xml:space="preserve">
    <value>1 mes</value>
  </data>
  <data name="ShapeType_DrawingTextBackground" xml:space="preserve">
    <value>Texto con fondo (T)</value>
  </data>
  <data name="RandomNonAmbiguousAlphanumericCharRepeatUsingN" xml:space="preserve">
    <value>Caracteres alfanuméricos no ambiguos. Usar {n} para repetir</value>
  </data>
  <data name="UrlShortenerType_CustomURLShortener" xml:space="preserve">
    <value>Acortador de enlaces personalizado</value>
  </data>
  <data name="PastebinPrivacy_Public" xml:space="preserve">
    <value>Público</value>
  </data>
  <data name="FileExistAction_Overwrite" xml:space="preserve">
    <value>Sobrescribir archivo</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfWatermark" xml:space="preserve">
    <value>Porcentaje de imagen</value>
  </data>
  <data name="HotkeyType_ShortenURL" xml:space="preserve">
    <value>Acortar enlace</value>
  </data>
  <data name="CustomUploaderDestinationType_TextUploader" xml:space="preserve">
    <value>Servidor de textos</value>
  </data>
  <data name="FileExistAction_Ask" xml:space="preserve">
    <value>Preguntar qué hacer</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_LatestVersion" xml:space="preserve">
    <value>Última versión</value>
  </data>
  <data name="AfterCaptureTasks_ShowAfterCaptureWindow" xml:space="preserve">
    <value>Mostrar ventana de "Después de capturar"</value>
  </data>
  <data name="HotkeyType_UploadText" xml:space="preserve">
    <value>Subir texto</value>
  </data>
  <data name="ShapeType_ToolCrop" xml:space="preserve">
    <value>Recortar (C)</value>
  </data>
  <data name="HotkeyType_UploadURL" xml:space="preserve">
    <value>Subir desde enlace</value>
  </data>
  <data name="HotkeyType_ImageSplitter" xml:space="preserve">
    <value>Divisor de imágenes</value>
  </data>
  <data name="AfterUploadTasks_CopyURLToClipboard" xml:space="preserve">
    <value>Copiar enlace al portapapeles</value>
  </data>
  <data name="ReplCodeMenuEntry_cn_Computer_name" xml:space="preserve">
    <value>Nombre del equipo</value>
  </data>
  <data name="HotkeyType_StartAutoCapture" xml:space="preserve">
    <value>Iniciar captura automática usando la última región</value>
  </data>
  <data name="ImgurThumbnailType_Medium_Thumbnail" xml:space="preserve">
    <value>Miniatura media</value>
  </data>
  <data name="Extensions_AddContextMenu_Undo" xml:space="preserve">
    <value>Deshacer</value>
  </data>
  <data name="AfterCaptureTasks_CopyFileToClipboard" xml:space="preserve">
    <value>Copiar archivo al portapapeles</value>
  </data>
  <data name="ResultOfFirstFile" xml:space="preserve">
    <value>Resultado del primer archivo:</value>
  </data>
  <data name="ResultOfSecondFile" xml:space="preserve">
    <value>Resultado del segundo archivo:</value>
  </data>
  <data name="Result" xml:space="preserve">
    <value>Resultado:</value>
  </data>
  <data name="Target" xml:space="preserve">
    <value>Objetivo:</value>
  </data>
  <data name="ArrowHeadDirection_End" xml:space="preserve">
    <value>Fin</value>
  </data>
  <data name="ArrowHeadDirection_Start" xml:space="preserve">
    <value>Inicio</value>
  </data>
  <data name="ArrowHeadDirection_Both" xml:space="preserve">
    <value>Ambos</value>
  </data>
  <data name="StepType_LettersLowercase" xml:space="preserve">
    <value>Letras (Minúsculas)</value>
  </data>
  <data name="StepType_LettersUppercase" xml:space="preserve">
    <value>Letras (Mayúsculas)</value>
  </data>
  <data name="StepType_Numbers" xml:space="preserve">
    <value>Números</value>
  </data>
  <data name="StepType_RomanNumeralsLowercase" xml:space="preserve">
    <value>Numerales romanos (Minúsculas)</value>
  </data>
  <data name="StepType_RomanNumeralsUppercase" xml:space="preserve">
    <value>Numerales romanos (Mayúsculas)</value>
  </data>
  <data name="HotkeyType_ClipboardViewer" xml:space="preserve">
    <value>Visualizador de portapapeles</value>
  </data>
  <data name="HotkeyType_InspectWindow" xml:space="preserve">
    <value>Inspeccionar ventana</value>
  </data>
  <data name="BorderStyle_Solid" xml:space="preserve">
    <value>Sólido</value>
  </data>
  <data name="BorderStyle_Dash" xml:space="preserve">
    <value>Guión</value>
  </data>
  <data name="BorderStyle_Dot" xml:space="preserve">
    <value>Punto</value>
  </data>
  <data name="BorderStyle_DashDot" xml:space="preserve">
    <value>GuiónPunto</value>
  </data>
  <data name="BorderStyle_DashDotDot" xml:space="preserve">
    <value>GuiónPuntoPunto</value>
  </data>
  <data name="ToastClickAction_CloseNotification" xml:space="preserve">
    <value>Cerrar notificación</value>
  </data>
  <data name="ToastClickAction_AnnotateImage" xml:space="preserve">
    <value>Editar imagen</value>
  </data>
  <data name="ToastClickAction_CopyImageToClipboard" xml:space="preserve">
    <value>Copiar imagen</value>
  </data>
  <data name="ToastClickAction_CopyFile" xml:space="preserve">
    <value>Copiar archivo</value>
  </data>
  <data name="ToastClickAction_CopyFilePath" xml:space="preserve">
    <value>Copiar ruta de archivo</value>
  </data>
  <data name="ToastClickAction_CopyUrl" xml:space="preserve">
    <value>Copiar enlace</value>
  </data>
  <data name="ToastClickAction_OpenFile" xml:space="preserve">
    <value>Abrir archivo</value>
  </data>
  <data name="ToastClickAction_OpenFolder" xml:space="preserve">
    <value>Abrir carpeta</value>
  </data>
  <data name="ToastClickAction_OpenUrl" xml:space="preserve">
    <value>Abrir enlace</value>
  </data>
  <data name="ToastClickAction_Upload" xml:space="preserve">
    <value>Subir archivo</value>
  </data>
  <data name="ContentAlignment_TopLeft" xml:space="preserve">
    <value>Superior izquierda</value>
  </data>
  <data name="ContentAlignment_TopCenter" xml:space="preserve">
    <value>Centro superior</value>
  </data>
  <data name="ContentAlignment_TopRight" xml:space="preserve">
    <value>Superior derecha</value>
  </data>
  <data name="ContentAlignment_MiddleLeft" xml:space="preserve">
    <value>Centro izquierda</value>
  </data>
  <data name="ContentAlignment_MiddleCenter" xml:space="preserve">
    <value>Centro medio</value>
  </data>
  <data name="ContentAlignment_MiddleRight" xml:space="preserve">
    <value>Centro derecha</value>
  </data>
  <data name="ContentAlignment_BottomLeft" xml:space="preserve">
    <value>Inferior izquierda</value>
  </data>
  <data name="ContentAlignment_BottomCenter" xml:space="preserve">
    <value>Centro inferior</value>
  </data>
  <data name="ContentAlignment_BottomRight" xml:space="preserve">
    <value>Inferior derecha</value>
  </data>
  <data name="URLSharingServices_BingVisualSearch" xml:space="preserve">
    <value>Búsqueda visual de Bing</value>
  </data>
  <data name="EDataType_Default" xml:space="preserve">
    <value>Predeterminado</value>
  </data>
  <data name="EDataType_File" xml:space="preserve">
    <value>Archivo</value>
  </data>
  <data name="EDataType_Image" xml:space="preserve">
    <value>Imagen</value>
  </data>
  <data name="EDataType_Text" xml:space="preserve">
    <value>Texto</value>
  </data>
  <data name="EDataType_URL" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="RegionCaptureAction_CaptureLastRegion" xml:space="preserve">
    <value>Capturar última región</value>
  </data>
  <data name="HotkeyType_StopScreenRecording" xml:space="preserve">
    <value>Detener grabación de pantalla</value>
  </data>
  <data name="HotkeyType_ToggleTrayMenu" xml:space="preserve">
    <value>Activar/Desactivar menú de la bandeja</value>
  </data>
  <data name="ThumbnailViewClickAction_Default" xml:space="preserve">
    <value>Predeterminado</value>
  </data>
  <data name="ThumbnailViewClickAction_EditImage" xml:space="preserve">
    <value>Editar imagen</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFile" xml:space="preserve">
    <value>Abrir archivo</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFolder" xml:space="preserve">
    <value>Abrir carpeta</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenImageViewer" xml:space="preserve">
    <value>Abrir visualizador de imágenes</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenURL" xml:space="preserve">
    <value>Abrir URL</value>
  </data>
  <data name="ThumbnailViewClickAction_Select" xml:space="preserve">
    <value>Seleccionar</value>
  </data>
  <data name="ImagePreviewLocation_Bottom" xml:space="preserve">
    <value>Inferior</value>
  </data>
  <data name="ImagePreviewLocation_Side" xml:space="preserve">
    <value>Lateral</value>
  </data>
  <data name="ImagePreviewVisibility_Automatic" xml:space="preserve">
    <value>Automático</value>
  </data>
  <data name="ImagePreviewVisibility_Hide" xml:space="preserve">
    <value>Ocultar</value>
  </data>
  <data name="ImagePreviewVisibility_Show" xml:space="preserve">
    <value>Mostrar</value>
  </data>
  <data name="TaskViewMode_ListView" xml:space="preserve">
    <value>Vista de lista</value>
  </data>
  <data name="TaskViewMode_ThumbnailView" xml:space="preserve">
    <value>Vista de miniatura</value>
  </data>
  <data name="ThumbnailTitleLocation_Bottom" xml:space="preserve">
    <value>Inferior</value>
  </data>
  <data name="ThumbnailTitleLocation_Top" xml:space="preserve">
    <value>Superior</value>
  </data>
  <data name="HotkeyType_ImageViewer" xml:space="preserve">
    <value>Visualizador de imágenes</value>
  </data>
  <data name="HotkeyType_OCR" xml:space="preserve">
    <value>OCR</value>
  </data>
  <data name="HotkeyType_BorderlessWindow" xml:space="preserve">
    <value>Ventana sin bordes</value>
  </data>
  <data name="AfterCaptureTasks_PinToScreen" xml:space="preserve">
    <value>Fijar a pantalla</value>
  </data>
  <data name="ToastClickAction_PinToScreen" xml:space="preserve">
    <value>Fijar a pantalla</value>
  </data>
  <data name="ShareXImageViewer" xml:space="preserve">
    <value>ShareX - Visualizador de imagen</value>
  </data>
  <data name="HotkeyType_PinToScreen" xml:space="preserve">
    <value>Fijar a pantalla</value>
  </data>
  <data name="CutOutEffectType_None" xml:space="preserve">
    <value>Sin efecto</value>
  </data>
  <data name="CutOutEffectType_TornEdge" xml:space="preserve">
    <value>Bordes rasgados</value>
  </data>
  <data name="CutOutEffectType_Wave" xml:space="preserve">
    <value>Onda</value>
  </data>
  <data name="CutOutEffectType_ZigZag" xml:space="preserve">
    <value>Dientes de sierra</value>
  </data>
  <data name="ShapeType_ToolCutOut" xml:space="preserve">
    <value>Recorte (X)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromClipboard" xml:space="preserve">
    <value>Fijar en pantalla (Desde portapapeles)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromFile" xml:space="preserve">
    <value>Fijar en pantalla (Desde archivo)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromScreen" xml:space="preserve">
    <value>Fijar en pantalla (Desde pantalla)</value>
  </data>
  <data name="HotkeyType_PauseScreenRecording" xml:space="preserve">
    <value>Pausar grabacióñ de pantalla</value>
  </data>
  <data name="ShapeType_DrawingFreehandArrow" xml:space="preserve">
    <value>Flecha a mano alzada</value>
  </data>
  <data name="HotkeyType_ImageBeautifier" xml:space="preserve">
    <value>Embellecedor de imagen</value>
  </data>
  <data name="AfterCaptureTasks_BeautifyImage" xml:space="preserve">
    <value>Embellecer imagen</value>
  </data>
  <data name="HotkeyType_Metadata" xml:space="preserve">
    <value />
  </data>
</root>
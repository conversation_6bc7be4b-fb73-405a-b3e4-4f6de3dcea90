const express = require('express');
const SemanticSearchService = require('../services/semanticSearchService');
const queueManager = require('../services/queueManager');

const router = express.Router();
const semanticSearchService = new SemanticSearchService();

/**
 * POST /api/ai/semantic-search/search
 * Buscar conteúdo usando busca semântica
 */
router.post('/search', async (req, res) => {
  try {
    const { query, options = {} } = req.body;

    if (!query || query.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Query de busca é obrigatória'
      });
    }

    const result = await semanticSearchService.findSimilarContent(query, options);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro na busca semântica',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/semantic-search/similar/:recordingId
 * Encontrar documentos similares a uma gravação específica
 */
router.get('/similar/:recordingId', async (req, res) => {
  try {
    const { recordingId } = req.params;
    const options = {
      minSimilarity: parseFloat(req.query.minSimilarity) || 0.6,
      maxResults: parseInt(req.query.maxResults) || 10
    };

    const result = await semanticSearchService.findSimilarDocuments(recordingId, options);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao buscar documentos similares',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/semantic-search/cluster
 * Agrupar documentos por similaridade
 */
router.post('/cluster', async (req, res) => {
  try {
    const { recordingIds, options = {} } = req.body;

    if (!recordingIds || !Array.isArray(recordingIds)) {
      return res.status(400).json({
        success: false,
        message: 'Lista de IDs de gravações é obrigatória'
      });
    }

    const result = await semanticSearchService.clusterDocuments(recordingIds, options);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro no clustering de documentos',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/semantic-search/process/:recordingId
 * Processar gravação para busca semântica
 */
router.post('/process/:recordingId', async (req, res) => {
  try {
    const { recordingId } = req.params;
    
    // Buscar transcrição
    const axios = require('axios');
    const transcriptionResponse = await axios.get(`http://localhost:3003/api/ai/transcription/recording/${recordingId}`);
    const transcriptionData = transcriptionResponse.data?.data;

    if (!transcriptionData) {
      return res.status(400).json({
        success: false,
        message: 'Transcrição não encontrada. Execute a transcrição primeiro.'
      });
    }

    const result = await semanticSearchService.processRecordingForSearch(
      recordingId, 
      transcriptionData.text
    );

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao processar gravação para busca',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/semantic-search/batch-process
 * Processar múltiplas gravações para busca semântica
 */
router.post('/batch-process', async (req, res) => {
  try {
    const { recordingIds } = req.body;

    if (!recordingIds || !Array.isArray(recordingIds)) {
      return res.status(400).json({
        success: false,
        message: 'Lista de IDs de gravações é obrigatória'
      });
    }

    // Adicionar trabalhos à fila
    const jobs = [];
    for (const recordingId of recordingIds) {
      const job = await queueManager.addSemanticProcessingJob(recordingId);
      jobs.push({
        recordingId,
        jobId: job.id,
        status: 'queued'
      });
    }

    res.json({
      success: true,
      message: `${jobs.length} trabalhos de processamento semântico adicionados à fila`,
      data: jobs
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao processar lote para busca semântica',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/semantic-search/suggest-tags
 * Sugerir tags baseadas no conteúdo
 */
router.post('/suggest-tags', async (req, res) => {
  try {
    const { text, existingTags = [] } = req.body;

    if (!text || text.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Texto é obrigatório'
      });
    }

    const suggestedTags = await semanticSearchService.suggestTags(text, existingTags);

    res.json({
      success: true,
      data: {
        suggestedTags,
        existingTags
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao sugerir tags',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/semantic-search/keywords/:recordingId
 * Obter palavras-chave de uma gravação
 */
router.get('/keywords/:recordingId', async (req, res) => {
  try {
    const { recordingId } = req.params;
    
    const aiDatabase = require('../database/aiDatabase');
    const keywords = await aiDatabase.getRecordingKeywords(recordingId);

    if (!keywords) {
      return res.status(404).json({
        success: false,
        message: 'Palavras-chave não encontradas'
      });
    }

    res.json({
      success: true,
      data: keywords
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao buscar palavras-chave',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/semantic-search/stats
 * Estatísticas da busca semântica
 */
router.get('/stats', async (req, res) => {
  try {
    const aiDatabase = require('../database/aiDatabase');
    
    const [
      totalEmbeddings,
      recentSearches,
      topKeywords,
      popularTags
    ] = await Promise.all([
      aiDatabase.getTotalEmbeddings(),
      aiDatabase.getRecentSearches(7),
      aiDatabase.getTopKeywords(20),
      aiDatabase.getPopularTags(15)
    ]);

    const stats = {
      totalProcessedRecordings: totalEmbeddings,
      recentSearches: recentSearches.length,
      searchVolume: recentSearches.reduce((sum, s) => sum + s.count, 0),
      topKeywords: topKeywords.slice(0, 10),
      popularTags: popularTags.slice(0, 10),
      averageProcessingTime: recentSearches.reduce((sum, s) => sum + s.processing_time, 0) / Math.max(recentSearches.length, 1)
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao carregar estatísticas',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/semantic-search/feedback
 * Registrar feedback sobre resultados de busca
 */
router.post('/feedback', async (req, res) => {
  try {
    const { sessionId, recordingId, relevant, feedback } = req.body;

    if (!sessionId || !recordingId) {
      return res.status(400).json({
        success: false,
        message: 'Session ID e Recording ID são obrigatórios'
      });
    }

    const aiDatabase = require('../database/aiDatabase');
    await aiDatabase.saveSearchFeedback({
      sessionId,
      recordingId,
      relevant: Boolean(relevant),
      feedback: feedback || null,
      timestamp: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'Feedback registrado com sucesso'
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao registrar feedback',
      error: error.message
    });
  }
});

module.exports = router;

﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" id="root">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="btnCancel.Text" xml:space="preserve">
    <value>Скасувати</value>
  </data>
  <data name="btnOK.Text" xml:space="preserve">
    <value>ОК</value>
  </data>
  <data name="lblOld.Text" xml:space="preserve">
    <value>Старий:</value>
  </data>
  <data name="lblNew.Text" xml:space="preserve">
    <value>Новий:</value>
  </data>
  <data name="lblHex.Text" xml:space="preserve">
    <value>Шістнадцятковий:</value>
  </data>
  <data name="lblKey.Text" xml:space="preserve">
    <value>Ключовий:</value>
  </data>
  <data name="lblYellow.Text" xml:space="preserve">
    <value>Жовтий:</value>
  </data>
  <data name="lblMagenta.Text" xml:space="preserve">
    <value>Пурпуровий:</value>
  </data>
  <data name="lblCyan.Text" xml:space="preserve">
    <value>Блакитний:</value>
  </data>
  <data name="rbBlue.Text" xml:space="preserve">
    <value>Синій:</value>
  </data>
  <data name="rbGreen.Text" xml:space="preserve">
    <value>Зелений:</value>
  </data>
  <data name="rbRed.Text" xml:space="preserve">
    <value>Червоний:</value>
  </data>
  <data name="rbBrightness.Text" xml:space="preserve">
    <value>Яскравість:</value>
  </data>
  <data name="rbSaturation.Text" xml:space="preserve">
    <value>Насиченість:</value>
  </data>
  <data name="rbHue.Text" xml:space="preserve">
    <value>Відтінок:</value>
  </data>
  <data name="lblDecimal.Text" xml:space="preserve">
    <value>Десятковий:</value>
  </data>
  <data name="lblAlpha.Text" xml:space="preserve">
    <value>Альфа:</value>
  </data>
  <data name="btnScreenColorPicker.ToolTip" xml:space="preserve">
    <value>Вибрати колір з екрану</value>
  </data>
  <data name="tsmiCopyAll.Text" xml:space="preserve">
    <value>Скопіювати все</value>
  </data>
  <data name="tsmiCopyRGB.Text" xml:space="preserve">
    <value>Копіювати RGB</value>
  </data>
  <data name="tsmiCopyHexadecimal.Text" xml:space="preserve">
    <value>Копіювати шістнадцяткове значення</value>
  </data>
  <data name="tsmiCopyCMYK.Text" xml:space="preserve">
    <value>Копіювати CMYK</value>
  </data>
  <data name="tsmiCopyHSB.Text" xml:space="preserve">
    <value>Копіювати HSB</value>
  </data>
  <data name="tsmiCopyDecimal.Text" xml:space="preserve">
    <value>Копіювати десяткове значення</value>
  </data>
  <data name="tsmiCopyPosition.Text" xml:space="preserve">
    <value>Копіювати розташування</value>
  </data>
  <data name="lblCursorPosition.Text" xml:space="preserve">
    <value>Позиція вказівника:</value>
  </data>
  <data name="btnClose.Text" xml:space="preserve">
    <value>Закрити</value>
  </data>
  <data name="rbRecentColors.Text" xml:space="preserve">
    <value>Останні кольори</value>
  </data>
  <data name="rbStandardColors.Text" xml:space="preserve">
    <value>Типові кольори</value>
  </data>
  <data name="lblName.Text" xml:space="preserve">
    <value>Назва:</value>
  </data>
  <data name="mbCopy.Text" xml:space="preserve">
    <value>Копіювати</value>
  </data>
  <data name="cbTransparent.ToolTip" xml:space="preserve">
    <value>Прозорий</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX — Вибір кольору</value>
  </data>
  <data name="btnClipboardColorPicker.ToolTip" xml:space="preserve">
    <value>Вибрати колір з буфера обміну</value>
  </data>
</root>
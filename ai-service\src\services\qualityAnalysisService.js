const OpenAI = require('openai');
const ffmpeg = require('fluent-ffmpeg');
const winston = require('winston');
const { v4: uuidv4 } = require('uuid');
const fs = require('fs').promises;

// Configurar o caminho do FFmpeg para Docker
ffmpeg.setFfmpegPath('/usr/bin/ffmpeg');
ffmpeg.setFfprobePath('/usr/bin/ffprobe');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'quality-analysis-service' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: '/app/logs/quality-analysis.log' })
  ]
});

class QualityAnalysisService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
  }

  /**
   * Analisar qualidade completa de uma gravação
   */
  async analyzeRecordingQuality(videoPath, transcription, metadata = {}) {
    const sessionId = uuidv4();
    const startTime = Date.now();

    try {
      logger.info(`Iniciando análise de qualidade [${sessionId}]`, { 
        videoPath: videoPath ? 'presente' : 'ausente',
        transcriptionLength: transcription ? transcription.length : 0
      });

      const [audioMetrics, textMetrics] = await Promise.all([
        videoPath ? this.analyzeAudioQuality(videoPath) : null,
        transcription ? this.analyzeTranscriptionQuality(transcription) : null
      ]);

      const overallScore = this.calculateOverallScore(audioMetrics, textMetrics);
      const recommendations = this.generateRecommendations(audioMetrics, textMetrics);
      const qualityLevel = this.determineQualityLevel(overallScore);

      const result = {
        sessionId,
        overallScore,
        qualityLevel,
        audioQuality: audioMetrics,
        transcriptionQuality: textMetrics,
        recommendations,
        processingTime: Date.now() - startTime,
        metadata: {
          analysisDate: new Date().toISOString(),
          hasAudio: !!videoPath,
          hasTranscription: !!transcription
        }
      };

      logger.info(`Análise de qualidade concluída [${sessionId}]`, {
        overallScore,
        qualityLevel,
        processingTime: result.processingTime
      });

      return result;

    } catch (error) {
      logger.error(`Erro na análise de qualidade [${sessionId}]:`, error);
      throw new Error(`Falha na análise de qualidade: ${error.message}`);
    }
  }

  /**
   * Analisar qualidade do áudio
   */
  async analyzeAudioQuality(videoPath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(videoPath, (err, metadata) => {
        if (err) {
          reject(new Error(`Erro ao analisar áudio: ${err.message}`));
          return;
        }

        try {
          const audioStream = metadata.streams.find(s => s.codec_type === 'audio');
          
          if (!audioStream) {
            resolve({
              hasAudio: false,
              score: 0,
              issues: ['Nenhum stream de áudio encontrado']
            });
            return;
          }

          const metrics = {
            hasAudio: true,
            duration: parseFloat(metadata.format.duration) || 0,
            bitrate: parseInt(audioStream.bit_rate) || 0,
            sampleRate: parseInt(audioStream.sample_rate) || 0,
            channels: audioStream.channels || 0,
            codec: audioStream.codec_name || 'unknown'
          };

          // Calcular score baseado nas métricas
          let score = 100;
          const issues = [];

          // Verificar duração
          if (metrics.duration < 1) {
            score -= 30;
            issues.push('Duração muito curta (< 1 segundo)');
          } else if (metrics.duration > 3600) {
            score -= 10;
            issues.push('Duração muito longa (> 1 hora)');
          }

          // Verificar bitrate
          if (metrics.bitrate < 32000) {
            score -= 25;
            issues.push('Bitrate muito baixo (< 32kbps)');
          } else if (metrics.bitrate < 64000) {
            score -= 10;
            issues.push('Bitrate baixo (< 64kbps)');
          }

          // Verificar sample rate
          if (metrics.sampleRate < 16000) {
            score -= 20;
            issues.push('Sample rate baixo (< 16kHz)');
          } else if (metrics.sampleRate < 22050) {
            score -= 10;
            issues.push('Sample rate moderado (< 22kHz)');
          }

          // Verificar canais
          if (metrics.channels < 1) {
            score -= 30;
            issues.push('Nenhum canal de áudio');
          }

          resolve({
            ...metrics,
            score: Math.max(0, score),
            issues,
            recommendations: this.generateAudioRecommendations(metrics, issues)
          });

        } catch (error) {
          reject(new Error(`Erro ao processar metadados de áudio: ${error.message}`));
        }
      });
    });
  }

  /**
   * Analisar qualidade da transcrição
   */
  async analyzeTranscriptionQuality(transcription) {
    try {
      const prompt = `
      Analise a qualidade desta transcrição de cartório brasileiro:

      "${transcription}"

      Avalie os seguintes aspectos:
      1. Clareza e coerência do texto
      2. Presença e correção de termos jurídicos
      3. Estrutura gramatical e pontuação
      4. Completude das informações
      5. Possíveis erros de transcrição
      6. Adequação ao contexto cartorial

      Retorne um JSON com:
      {
        "score": number (0-100),
        "clarity_score": number (0-100),
        "legal_terms_score": number (0-100),
        "grammar_score": number (0-100),
        "completeness_score": number (0-100),
        "accuracy_score": number (0-100),
        "issues": ["lista de problemas encontrados"],
        "strengths": ["pontos positivos"],
        "suggestions": ["sugestões de melhoria"],
        "word_count": number,
        "sentence_count": number,
        "legal_terms_found": ["termos jurídicos identificados"]
      }
      `;

      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [{ role: "user", content: prompt }],
        response_format: { type: "json_object" },
        temperature: 0.1,
        max_tokens: 1500
      });

      const analysis = JSON.parse(response.choices[0].message.content);

      // Adicionar métricas básicas
      const basicMetrics = this.calculateBasicTextMetrics(transcription);

      return {
        ...analysis,
        ...basicMetrics,
        recommendations: this.generateTranscriptionRecommendations(analysis)
      };

    } catch (error) {
      logger.error('Erro na análise de transcrição:', error);
      throw new Error(`Falha na análise de transcrição: ${error.message}`);
    }
  }

  /**
   * Calcular métricas básicas do texto
   */
  calculateBasicTextMetrics(text) {
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);

    return {
      actualWordCount: words.length,
      actualSentenceCount: sentences.length,
      paragraphCount: paragraphs.length,
      averageWordsPerSentence: words.length / Math.max(sentences.length, 1),
      averageSentencesPerParagraph: sentences.length / Math.max(paragraphs.length, 1),
      readabilityScore: this.calculateReadabilityScore(words, sentences)
    };
  }

  /**
   * Calcular score de legibilidade
   */
  calculateReadabilityScore(words, sentences) {
    if (sentences.length === 0) return 0;
    
    const avgWordsPerSentence = words.length / sentences.length;
    
    // Score baseado na complexidade das sentenças
    let score = 100;
    
    if (avgWordsPerSentence > 25) {
      score -= 20; // Sentenças muito longas
    } else if (avgWordsPerSentence > 20) {
      score -= 10;
    }

    return Math.max(0, score);
  }

  /**
   * Calcular score geral
   */
  calculateOverallScore(audioMetrics, textMetrics) {
    let totalScore = 0;
    let components = 0;

    if (audioMetrics && audioMetrics.hasAudio) {
      totalScore += audioMetrics.score * 0.4; // 40% peso para áudio
      components += 0.4;
    }

    if (textMetrics) {
      totalScore += textMetrics.score * 0.6; // 60% peso para texto
      components += 0.6;
    }

    return components > 0 ? Math.round(totalScore / components) : 0;
  }

  /**
   * Determinar nível de qualidade
   */
  determineQualityLevel(score) {
    if (score >= 90) return 'excelente';
    if (score >= 80) return 'boa';
    if (score >= 70) return 'regular';
    if (score >= 60) return 'baixa';
    return 'muito_baixa';
  }

  /**
   * Gerar recomendações gerais
   */
  generateRecommendations(audioMetrics, textMetrics) {
    const recommendations = [];

    if (audioMetrics) {
      recommendations.push(...(audioMetrics.recommendations || []));
    }

    if (textMetrics) {
      recommendations.push(...(textMetrics.recommendations || []));
    }

    // Recomendações gerais
    if (audioMetrics && textMetrics) {
      if (audioMetrics.score < 70 && textMetrics.score > 80) {
        recommendations.push({
          type: 'audio',
          priority: 'alta',
          message: 'Melhorar qualidade do áudio para transcrições mais precisas'
        });
      }
    }

    return recommendations;
  }

  /**
   * Gerar recomendações para áudio
   */
  generateAudioRecommendations(metrics, issues) {
    const recommendations = [];

    if (metrics.bitrate < 64000) {
      recommendations.push({
        type: 'audio',
        priority: 'alta',
        message: 'Aumentar bitrate para pelo menos 64kbps'
      });
    }

    if (metrics.sampleRate < 22050) {
      recommendations.push({
        type: 'audio',
        priority: 'media',
        message: 'Usar sample rate de pelo menos 22kHz'
      });
    }

    if (metrics.duration < 5) {
      recommendations.push({
        type: 'content',
        priority: 'baixa',
        message: 'Gravações muito curtas podem ter transcrição limitada'
      });
    }

    return recommendations;
  }

  /**
   * Gerar recomendações para transcrição
   */
  generateTranscriptionRecommendations(analysis) {
    const recommendations = [];

    if (analysis.grammar_score < 70) {
      recommendations.push({
        type: 'transcription',
        priority: 'alta',
        message: 'Revisar gramática e pontuação da transcrição'
      });
    }

    if (analysis.legal_terms_score < 80) {
      recommendations.push({
        type: 'content',
        priority: 'media',
        message: 'Verificar correção dos termos jurídicos'
      });
    }

    if (analysis.completeness_score < 75) {
      recommendations.push({
        type: 'content',
        priority: 'alta',
        message: 'Transcrição pode estar incompleta - verificar gravação original'
      });
    }

    return recommendations;
  }

  /**
   * Analisar lote de gravações
   */
  async analyzeBatch(recordings) {
    const results = [];
    
    for (const recording of recordings) {
      try {
        const result = await this.analyzeRecordingQuality(
          recording.filePath,
          recording.transcription,
          recording.metadata
        );
        
        results.push({
          recordingId: recording.id,
          success: true,
          result
        });
      } catch (error) {
        results.push({
          recordingId: recording.id,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Gerar relatório de qualidade
   */
  async generateQualityReport(recordingIds, period = '30d') {
    try {
      const aiDatabase = require('../database/aiDatabase');
      const analyses = await aiDatabase.getQualityAnalyses(recordingIds, period);

      const report = {
        period,
        totalAnalyses: analyses.length,
        averageScore: analyses.reduce((sum, a) => sum + a.overall_score, 0) / analyses.length,
        qualityDistribution: this.calculateQualityDistribution(analyses),
        commonIssues: this.identifyCommonIssues(analyses),
        improvements: this.suggestImprovements(analyses),
        trends: this.analyzeQualityTrends(analyses)
      };

      return report;
    } catch (error) {
      throw new Error(`Erro ao gerar relatório de qualidade: ${error.message}`);
    }
  }

  /**
   * Calcular distribuição de qualidade
   */
  calculateQualityDistribution(analyses) {
    const distribution = {
      excelente: 0,
      boa: 0,
      regular: 0,
      baixa: 0,
      muito_baixa: 0
    };
    
    analyses.forEach(analysis => {
      distribution[analysis.quality_level]++;
    });

    return distribution;
  }

  /**
   * Identificar problemas comuns
   */
  identifyCommonIssues(analyses) {
    const issueCounts = {};
    
    analyses.forEach(analysis => {
      if (analysis.analysis?.audioQuality?.issues) {
        analysis.analysis.audioQuality.issues.forEach(issue => {
          issueCounts[issue] = (issueCounts[issue] || 0) + 1;
        });
      }
      
      if (analysis.analysis?.transcriptionQuality?.issues) {
        analysis.analysis.transcriptionQuality.issues.forEach(issue => {
          issueCounts[issue] = (issueCounts[issue] || 0) + 1;
        });
      }
    });

    return Object.entries(issueCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([issue, count]) => ({ issue, count, percentage: (count / analyses.length) * 100 }));
  }

  /**
   * Sugerir melhorias
   */
  suggestImprovements(analyses) {
    const improvements = [];
    
    const avgAudioScore = analyses.reduce((sum, a) => sum + (a.analysis?.audioQuality?.score || 0), 0) / analyses.length;
    const avgTextScore = analyses.reduce((sum, a) => sum + (a.analysis?.transcriptionQuality?.score || 0), 0) / analyses.length;

    if (avgAudioScore < 70) {
      improvements.push({
        area: 'audio',
        priority: 'alta',
        suggestion: 'Melhorar configurações de gravação de áudio',
        impact: 'Alto impacto na qualidade das transcrições'
      });
    }

    if (avgTextScore < 75) {
      improvements.push({
        area: 'transcription',
        priority: 'media',
        suggestion: 'Implementar revisão manual das transcrições',
        impact: 'Melhoria na precisão dos documentos'
      });
    }

    return improvements;
  }

  /**
   * Analisar tendências de qualidade
   */
  analyzeQualityTrends(analyses) {
    // Implementar análise de tendências temporais
    return {
      scoreImprovement: 0,
      qualityStability: 0,
      improvementRate: 0
    };
  }
}

module.exports = QualityAnalysisService;

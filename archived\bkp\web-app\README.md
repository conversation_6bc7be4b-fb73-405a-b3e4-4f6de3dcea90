# Cartório - Aplicação Web

Esta é a versão web (PWA) do Sistema de Gravação para Cartórios, desenvolvida com React, TypeScript e Material UI.

## Funcionalidades

- Autenticação e controle de acesso (login/senha/admin)
- <PERSON>end<PERSON> de acesso (assinaturas)
- Consentimento LGPD para gravações
- Gravação de tela (desktop e mobile)
- Armazenamento seguro com criptografia
- Geração de hash SHA-256 para verificação de integridade
- Tema claro/escuro
- Interface responsiva para desktop e dispositivos móveis

## Requisitos

- Node.js 16+
- npm ou yarn

## Instalação e Execução Local

```bash
# Instalar dependências
npm install

# Executar em modo de desenvolvimento
npm start

# Criar build para produção
npm run build
```

## Variáveis de Ambiente

Crie um arquivo `.env` na raiz do projeto com as seguintes variáveis:

```
REACT_APP_API_URL=http://localhost:3000
REACT_APP_VERSION=1.0.0
GENERATE_SOURCEMAP=false
```

## Estrutura do Projeto

- `src/contexts` - Contextos React para gerenciamento de estado
- `src/components` - Componentes reutilizáveis
- `src/pages` - Páginas da aplicação
- `src/services` - Serviços para comunicação com o backend
- `src/utils` - Utilitários e funções auxiliares

## Progressive Web App (PWA)

Esta aplicação é configurada como uma PWA, permitindo:

- Instalação na tela inicial de dispositivos
- Funcionamento offline para algumas funcionalidades
- Carregamento rápido
- Atualizações automáticas

## Segurança

- Os dados sensíveis são armazenados de forma criptografada
- As senhas nunca são armazenadas no cliente
- Tokens de autenticação são armazenados em sessionStorage (expiram ao fechar o navegador)
- Criptografia AES para dados locais
- Validação de integridade com hash SHA-256

## Containerização

Um Dockerfile está incluído para facilitar a implantação. Para construir e executar em um container:

```bash
docker build -t cartorio-web-app .
docker run -p 80:80 cartorio-web-app
```

Ou utilizando o docker-compose:

```bash
docker-compose up -d web-app
```

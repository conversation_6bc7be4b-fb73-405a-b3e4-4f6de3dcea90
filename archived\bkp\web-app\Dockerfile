# Estágio de build
FROM node:16-alpine as build

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . ./
RUN npm run build

# Estágio de produção
FROM nginx:alpine

# Copiar build estático
COPY --from=build /app/build /usr/share/nginx/html

# Configurar nginx para rotas SPA
COPY ./nginx.conf /etc/nginx/conf.d/default.conf

# Expor porta
EXPOSE 80

# Iniciar nginx
CMD ["nginx", "-g", "daemon off;"]

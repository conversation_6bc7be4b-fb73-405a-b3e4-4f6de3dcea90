const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs').promises;
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'ai-database' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: '/app/logs/ai-database.log' })
  ]
});

class AIDatabase {
  constructor() {
    this.dbPath = '/app/storage/ai-database.sqlite';
    this.db = null;
  }

  /**
   * Inicializar banco de dados
   */
  async initialize() {
    try {
      // Garantir que o diretório existe
      await fs.mkdir(path.dirname(this.dbPath), { recursive: true });

      this.db = new sqlite3.Database(this.dbPath);
      
      await this.createTables();
      logger.info('Banco de dados de IA inicializado com sucesso');
    } catch (error) {
      logger.error('Erro ao inicializar banco de dados de IA:', error);
      throw error;
    }
  }

  /**
   * Criar tabelas necessárias
   */
  async createTables() {
    const tables = [
      // Tabela de transcrições
      `CREATE TABLE IF NOT EXISTS transcriptions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        recording_id TEXT NOT NULL UNIQUE,
        session_id TEXT NOT NULL,
        text TEXT NOT NULL,
        language TEXT DEFAULT 'pt',
        duration REAL,
        confidence REAL,
        word_count INTEGER,
        segments TEXT, -- JSON
        processing_time INTEGER,
        model TEXT DEFAULT 'whisper-1',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tabela de embeddings para busca semântica
      `CREATE TABLE IF NOT EXISTS embeddings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        recording_id TEXT NOT NULL UNIQUE,
        embedding TEXT NOT NULL, -- JSON array
        model TEXT DEFAULT 'text-embedding-3-small',
        tokens INTEGER,
        keywords TEXT, -- JSON
        suggested_tags TEXT, -- JSON
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tabela de análises de compliance
      `CREATE TABLE IF NOT EXISTS compliance_analyses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        recording_id TEXT NOT NULL,
        session_id TEXT NOT NULL,
        analysis TEXT NOT NULL, -- JSON
        compliance_score REAL,
        risk_level TEXT,
        recommendations TEXT, -- JSON
        processing_time INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tabela de análises de qualidade
      `CREATE TABLE IF NOT EXISTS quality_analyses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        recording_id TEXT NOT NULL,
        session_id TEXT NOT NULL,
        analysis TEXT NOT NULL, -- JSON
        overall_score REAL,
        quality_level TEXT,
        audio_score REAL,
        transcription_score REAL,
        recommendations TEXT, -- JSON
        processing_time INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tabela de buscas semânticas (para analytics)
      `CREATE TABLE IF NOT EXISTS semantic_searches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT NOT NULL,
        query TEXT NOT NULL,
        results_count INTEGER,
        processing_time INTEGER,
        filters TEXT, -- JSON
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tabela de feedback de busca
      `CREATE TABLE IF NOT EXISTS search_feedback (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id TEXT NOT NULL,
        recording_id TEXT NOT NULL,
        relevant BOOLEAN,
        feedback TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tabela de jobs processados
      `CREATE TABLE IF NOT EXISTS ai_jobs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        job_id TEXT NOT NULL UNIQUE,
        job_type TEXT NOT NULL,
        recording_id TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        result TEXT, -- JSON
        error_message TEXT,
        processing_time INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME
      )`
    ];

    for (const tableSQL of tables) {
      await this.runQuery(tableSQL);
    }

    // Criar índices
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_transcriptions_recording_id ON transcriptions(recording_id)',
      'CREATE INDEX IF NOT EXISTS idx_embeddings_recording_id ON embeddings(recording_id)',
      'CREATE INDEX IF NOT EXISTS idx_compliance_recording_id ON compliance_analyses(recording_id)',
      'CREATE INDEX IF NOT EXISTS idx_quality_recording_id ON quality_analyses(recording_id)',
      'CREATE INDEX IF NOT EXISTS idx_searches_session_id ON semantic_searches(session_id)',
      'CREATE INDEX IF NOT EXISTS idx_feedback_session_id ON search_feedback(session_id)',
      'CREATE INDEX IF NOT EXISTS idx_jobs_recording_id ON ai_jobs(recording_id)',
      'CREATE INDEX IF NOT EXISTS idx_jobs_status ON ai_jobs(status)'
    ];

    for (const indexSQL of indexes) {
      await this.runQuery(indexSQL);
    }
  }

  /**
   * Executar query com Promise
   */
  runQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  /**
   * Executar query SELECT
   */
  getQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  /**
   * Executar query SELECT ALL
   */
  getAllQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // ===== MÉTODOS PARA TRANSCRIÇÕES =====

  /**
   * Salvar transcrição
   */
  async saveTranscription(recordingId, transcriptionData) {
    const sql = `
      INSERT OR REPLACE INTO transcriptions 
      (recording_id, session_id, text, language, duration, confidence, word_count, segments, processing_time, model)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      recordingId,
      transcriptionData.sessionId,
      transcriptionData.text,
      transcriptionData.language || 'pt',
      transcriptionData.duration,
      transcriptionData.confidence,
      transcriptionData.wordCount,
      JSON.stringify(transcriptionData.segments || []),
      transcriptionData.processingTime,
      transcriptionData.metadata?.model || 'whisper-1'
    ];

    return await this.runQuery(sql, params);
  }

  /**
   * Obter transcrição
   */
  async getTranscription(recordingId) {
    const sql = 'SELECT * FROM transcriptions WHERE recording_id = ?';
    const row = await this.getQuery(sql, [recordingId]);
    
    if (row) {
      row.segments = JSON.parse(row.segments || '[]');
    }
    
    return row;
  }

  // ===== MÉTODOS PARA EMBEDDINGS =====

  /**
   * Salvar embedding
   */
  async saveEmbedding(recordingId, embeddingData) {
    const sql = `
      INSERT OR REPLACE INTO embeddings 
      (recording_id, embedding, model, tokens, keywords, suggested_tags)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      recordingId,
      JSON.stringify(embeddingData.embedding),
      embeddingData.model,
      embeddingData.tokens,
      JSON.stringify(embeddingData.keywords || []),
      JSON.stringify(embeddingData.suggestedTags || [])
    ];

    return await this.runQuery(sql, params);
  }

  /**
   * Buscar conteúdo similar
   */
  async searchSimilarContent(queryEmbedding, filters = {}) {
    // Implementação simplificada - em produção usar extensão vetorial
    const sql = `
      SELECT 
        e.recording_id,
        e.embedding,
        e.keywords,
        e.suggested_tags,
        t.text as transcription,
        t.created_at
      FROM embeddings e
      LEFT JOIN transcriptions t ON e.recording_id = t.recording_id
      ORDER BY e.created_at DESC
      LIMIT ?
    `;
    
    const rows = await this.getAllQuery(sql, [filters.maxResults || 20]);
    
    // Calcular similaridade (implementação básica)
    const results = rows.map(row => {
      const embedding = JSON.parse(row.embedding);
      const similarity = this.calculateCosineSimilarity(queryEmbedding, embedding);
      
      return {
        recording_id: row.recording_id,
        similarity,
        transcription: row.transcription,
        keywords: JSON.parse(row.keywords || '[]'),
        suggested_tags: JSON.parse(row.suggested_tags || '[]'),
        created_at: row.created_at
      };
    });

    // Filtrar por similaridade mínima
    return results
      .filter(r => r.similarity >= (filters.minSimilarity || 0.7))
      .sort((a, b) => b.similarity - a.similarity);
  }

  /**
   * Calcular similaridade de cosseno (implementação básica)
   */
  calculateCosineSimilarity(vecA, vecB) {
    if (vecA.length !== vecB.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < vecA.length; i++) {
      dotProduct += vecA[i] * vecB[i];
      normA += vecA[i] * vecA[i];
      normB += vecB[i] * vecB[i];
    }
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  // ===== MÉTODOS PARA COMPLIANCE =====

  /**
   * Salvar análise de compliance
   */
  async saveComplianceAnalysis(recordingId, analysisData) {
    const sql = `
      INSERT INTO compliance_analyses 
      (recording_id, session_id, analysis, compliance_score, risk_level, recommendations, processing_time)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      recordingId,
      analysisData.sessionId,
      JSON.stringify(analysisData.analysis),
      analysisData.complianceScore,
      analysisData.riskLevel,
      JSON.stringify(analysisData.recommendations || []),
      analysisData.processingTime
    ];

    return await this.runQuery(sql, params);
  }

  /**
   * Obter análise de compliance
   */
  async getComplianceAnalysis(recordingId) {
    const sql = 'SELECT * FROM compliance_analyses WHERE recording_id = ? ORDER BY created_at DESC LIMIT 1';
    const row = await this.getQuery(sql, [recordingId]);
    
    if (row) {
      row.analysis = JSON.parse(row.analysis);
      row.recommendations = JSON.parse(row.recommendations || '[]');
    }
    
    return row;
  }

  // ===== MÉTODOS PARA QUALIDADE =====

  /**
   * Salvar análise de qualidade
   */
  async saveQualityAnalysis(recordingId, analysisData) {
    const sql = `
      INSERT INTO quality_analyses 
      (recording_id, session_id, analysis, overall_score, quality_level, audio_score, transcription_score, recommendations, processing_time)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const params = [
      recordingId,
      analysisData.sessionId,
      JSON.stringify(analysisData),
      analysisData.overallScore,
      analysisData.qualityLevel,
      analysisData.audioQuality?.score || null,
      analysisData.transcriptionQuality?.score || null,
      JSON.stringify(analysisData.recommendations || []),
      analysisData.processingTime
    ];

    return await this.runQuery(sql, params);
  }

  /**
   * Obter análise de qualidade
   */
  async getQualityAnalysis(recordingId) {
    const sql = 'SELECT * FROM quality_analyses WHERE recording_id = ? ORDER BY created_at DESC LIMIT 1';
    const row = await this.getQuery(sql, [recordingId]);
    
    if (row) {
      row.analysis = JSON.parse(row.analysis);
      row.recommendations = JSON.parse(row.recommendations || '[]');
    }
    
    return row;
  }

  // ===== MÉTODOS DE ANALYTICS =====

  /**
   * Obter estatísticas gerais
   */
  async getGeneralStats() {
    const stats = await Promise.all([
      this.getQuery('SELECT COUNT(*) as count FROM transcriptions'),
      this.getQuery('SELECT COUNT(*) as count FROM embeddings'),
      this.getQuery('SELECT COUNT(*) as count FROM compliance_analyses'),
      this.getQuery('SELECT COUNT(*) as count FROM quality_analyses'),
      this.getQuery('SELECT COUNT(*) as count FROM semantic_searches WHERE created_at >= datetime("now", "-7 days")')
    ]);

    return {
      totalTranscriptions: stats[0].count,
      totalEmbeddings: stats[1].count,
      totalComplianceAnalyses: stats[2].count,
      totalQualityAnalyses: stats[3].count,
      recentSearches: stats[4].count
    };
  }

  /**
   * Salvar busca semântica
   */
  async saveSemanticSearch(searchData) {
    const sql = `
      INSERT INTO semantic_searches 
      (session_id, query, results_count, processing_time, filters)
      VALUES (?, ?, ?, ?, ?)
    `;
    
    const params = [
      searchData.sessionId,
      searchData.query,
      searchData.resultsCount,
      searchData.processingTime,
      JSON.stringify(searchData.filters || {})
    ];

    return await this.runQuery(sql, params);
  }

  /**
   * Salvar feedback de busca
   */
  async saveSearchFeedback(feedbackData) {
    const sql = `
      INSERT INTO search_feedback 
      (session_id, recording_id, relevant, feedback)
      VALUES (?, ?, ?, ?)
    `;
    
    const params = [
      feedbackData.sessionId,
      feedbackData.recordingId,
      feedbackData.relevant,
      feedbackData.feedback
    ];

    return await this.runQuery(sql, params);
  }

  /**
   * Fechar conexão
   */
  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

// Singleton instance
let aiDatabaseInstance = null;

module.exports = {
  initialize: async () => {
    if (!aiDatabaseInstance) {
      aiDatabaseInstance = new AIDatabase();
      await aiDatabaseInstance.initialize();
    }
    return aiDatabaseInstance;
  },
  getInstance: () => aiDatabaseInstance,
  
  // Métodos de conveniência
  saveTranscription: (recordingId, data) => aiDatabaseInstance?.saveTranscription(recordingId, data),
  getTranscription: (recordingId) => aiDatabaseInstance?.getTranscription(recordingId),
  saveEmbedding: (recordingId, data) => aiDatabaseInstance?.saveEmbedding(recordingId, data),
  searchSimilarContent: (embedding, filters) => aiDatabaseInstance?.searchSimilarContent(embedding, filters),
  saveComplianceAnalysis: (recordingId, data) => aiDatabaseInstance?.saveComplianceAnalysis(recordingId, data),
  getComplianceAnalysis: (recordingId) => aiDatabaseInstance?.getComplianceAnalysis(recordingId),
  saveQualityAnalysis: (recordingId, data) => aiDatabaseInstance?.saveQualityAnalysis(recordingId, data),
  getQualityAnalysis: (recordingId) => aiDatabaseInstance?.getQualityAnalysis(recordingId),
  saveSemanticSearch: (data) => aiDatabaseInstance?.saveSemanticSearch(data),
  saveSearchFeedback: (data) => aiDatabaseInstance?.saveSearchFeedback(data),
  getGeneralStats: () => aiDatabaseInstance?.getGeneralStats(),

  // Métodos adicionais para analytics
  getTotalEmbeddings: () => aiDatabaseInstance?.getQuery('SELECT COUNT(*) as count FROM embeddings').then(r => r.count),
  getTotalComplianceAnalyses: () => aiDatabaseInstance?.getQuery('SELECT COUNT(*) as count FROM compliance_analyses').then(r => r.count),
  getTotalQualityAnalyses: () => aiDatabaseInstance?.getQuery('SELECT COUNT(*) as count FROM quality_analyses').then(r => r.count),
  getRecentSearches: (days) => aiDatabaseInstance?.getAllQuery('SELECT * FROM semantic_searches WHERE created_at >= datetime("now", "-' + days + ' days") ORDER BY created_at DESC'),
  getRecentComplianceAnalyses: (days) => aiDatabaseInstance?.getAllQuery('SELECT * FROM compliance_analyses WHERE created_at >= datetime("now", "-' + days + ' days") ORDER BY created_at DESC'),
  getRecentQualityAnalyses: (days) => aiDatabaseInstance?.getAllQuery('SELECT * FROM quality_analyses WHERE created_at >= datetime("now", "-' + days + ' days") ORDER BY created_at DESC'),
  getAverageComplianceScore: () => aiDatabaseInstance?.getQuery('SELECT AVG(compliance_score) as avg FROM compliance_analyses').then(r => r.avg || 0),
  getAverageQualityScore: () => aiDatabaseInstance?.getQuery('SELECT AVG(overall_score) as avg FROM quality_analyses').then(r => r.avg || 0),
  getComplianceRiskDistribution: () => aiDatabaseInstance?.getAllQuery('SELECT risk_level, COUNT(*) as count FROM compliance_analyses GROUP BY risk_level'),
  getQualityDistribution: () => aiDatabaseInstance?.getAllQuery('SELECT quality_level, COUNT(*) as count FROM quality_analyses GROUP BY quality_level')
};

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';
import { jwtDecode } from 'jwt-decode';
import CryptoJS from 'crypto-js';

// Definindo tipos
interface AuthState {
  isAuthenticated: boolean;
  authToken: string | null;
  userId: string | null;
  userName: string | null;
  userEmail: string | null;
  userRole: string | null;
  subscriptionEndDate: Date | null;
}

interface AppContextType {
  // Estado do tema
  darkMode: boolean;
  toggleTheme: () => void;
  
  // Estado da API
  apiUrl: string;
  updateApiUrl: (url: string) => void;
  
  // Estado de autenticação
  isAuthenticated: boolean;
  authToken: string | null;
  userId: string | null;
  userName: string | null;
  userEmail: string | null;
  userRole: string | null;
  subscriptionEndDate: Date | null;
  isAdmin: boolean;
  hasActiveSubscription: boolean;
  
  // Métodos de autenticação
  login: (email: string, password: string) => Promise<boolean>;
  register: (name: string, email: string, password: string) => Promise<boolean>;
  logout: () => void;
  updateSubscription: (subscriptionId: string, isRenewal?: boolean) => Promise<boolean>;
  
  // Permissões
  canRecord: () => boolean;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Chave para criptografia simples (para dados não tão sensíveis)
const ENCRYPTION_KEY = 'cartorios_seguro_2025';

export const AppProvider = ({ children }: { children: ReactNode }) => {
  // Estado do tema
  const [darkMode, setDarkMode] = useState<boolean>(() => {
    const savedTheme = localStorage.getItem('darkMode');
    return savedTheme ? JSON.parse(savedTheme) : false;
  });
  
  // URL da API
  const [apiUrl, setApiUrl] = useState<string>(() => {
    return localStorage.getItem('apiUrl') || 'http://localhost:3000';
  });
  
  // Estado de autenticação
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    authToken: null,
    userId: null,
    userName: null,
    userEmail: null,
    userRole: null,
    subscriptionEndDate: null
  });
  
  // Métodos de criptografia
  const encrypt = (data: string): string => {
    return CryptoJS.AES.encrypt(data, ENCRYPTION_KEY).toString();
  };
  
  const decrypt = (encryptedData: string | null): string | null => {
    if (!encryptedData) return null;
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY);
      return bytes.toString(CryptoJS.enc.Utf8);
    } catch (e) {
      console.error('Erro ao descriptografar:', e);
      return null;
    }
  };
  
  // Carregar dados de autenticação
  useEffect(() => {
    loadAuthData();
  }, []);
  
  const loadAuthData = () => {
    try {
      // Usar sessionStorage para o token (mais seguro, expira quando fecha o navegador)
      const token = sessionStorage.getItem('authToken');
      
      // Carregar dados menos sensíveis do localStorage
      const userId = decrypt(localStorage.getItem('userId'));
      const userName = decrypt(localStorage.getItem('userName'));
      const userEmail = decrypt(localStorage.getItem('userEmail'));
      const userRole = localStorage.getItem('userRole');
      const subscriptionEndTimestamp = localStorage.getItem('subscriptionEndDate');
      
      let subscriptionEndDate = null;
      if (subscriptionEndTimestamp) {
        subscriptionEndDate = new Date(parseInt(subscriptionEndTimestamp));
      }
      
      const isAuthenticated = !!token;
      
      setAuthState({
        isAuthenticated,
        authToken: token,
        userId,
        userName,
        userEmail,
        userRole,
        subscriptionEndDate
      });
      
      // Validar token se autenticado
      if (isAuthenticated && token) {
        validateToken(token);
      }
    } catch (e) {
      console.error('Erro ao carregar dados de autenticação:', e);
      setAuthState({
        isAuthenticated: false,
        authToken: null,
        userId: null,
        userName: null,
        userEmail: null,
        userRole: null,
        subscriptionEndDate: null
      });
    }
  };
  
  // Validar token com o backend
  const validateToken = async (token: string) => {
    try {
      // Verificar se o token expirou localmente primeiro
      const decodedToken: any = jwtDecode(token);
      const currentTime = Date.now() / 1000;
      
      if (decodedToken.exp && decodedToken.exp < currentTime) {
        // Token expirado
        await handleLogout();
        return;
      }
      
      // Validar com o backend
      const response = await axios.get(`${apiUrl}/api/auth/validate`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.status !== 200) {
        // Token inválido
        await handleLogout();
      }
    } catch (e) {
      console.error('Erro ao validar token:', e);
      // Manter o usuário logado mas o token será validado na próxima operação
    }
  };
  
  // Alternar tema
  const toggleTheme = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    localStorage.setItem('darkMode', JSON.stringify(newDarkMode));
  };
  
  // Atualizar URL da API
  const updateApiUrl = (url: string) => {
    setApiUrl(url);
    localStorage.setItem('apiUrl', url);
  };
  
  // Autenticar usuário (login)
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const response = await axios.post(`${apiUrl}/api/auth/login`, {
        email,
        password
      });
      
      if (response.status === 200) {
        const data = response.data;
        
        // Salvar token no sessionStorage (expira quando fecha o navegador)
        sessionStorage.setItem('authToken', data.token);
        
        // Salvar dados menos sensíveis no localStorage (criptografados)
        localStorage.setItem('userId', encrypt(data.userId));
        localStorage.setItem('userName', encrypt(data.name));
        localStorage.setItem('userEmail', encrypt(email));
        localStorage.setItem('userRole', data.role);
        
        let subscriptionEndDate = null;
        if (data.subscriptionEnd) {
          subscriptionEndDate = new Date(data.subscriptionEnd);
          localStorage.setItem(
            'subscriptionEndDate', 
            subscriptionEndDate.getTime().toString()
          );
        }
        
        setAuthState({
          isAuthenticated: true,
          authToken: data.token,
          userId: data.userId,
          userName: data.name,
          userEmail: email,
          userRole: data.role,
          subscriptionEndDate
        });
        
        return true;
      } else {
        throw new Error(response.data.message || 'Falha na autenticação');
      }
    } catch (e: any) {
      console.error('Erro no login:', e);
      return false;
    }
  };
  
  // Registrar novo usuário
  const register = async (name: string, email: string, password: string): Promise<boolean> => {
    try {
      const response = await axios.post(`${apiUrl}/api/auth/register`, {
        name,
        email,
        password
      });
      
      if (response.status === 201) {
        // Após o registro, fazer login automaticamente
        return login(email, password);
      } else {
        throw new Error(response.data.message || 'Falha no registro');
      }
    } catch (e) {
      console.error('Erro no registro:', e);
      return false;
    }
  };
  
  // Logout
  const handleLogout = async () => {
    // Limpar todos os dados de autenticação
    sessionStorage.removeItem('authToken');
    localStorage.removeItem('userId');
    localStorage.removeItem('userName');
    localStorage.removeItem('userEmail');
    localStorage.removeItem('userRole');
    localStorage.removeItem('subscriptionEndDate');
    
    setAuthState({
      isAuthenticated: false,
      authToken: null,
      userId: null,
      userName: null,
      userEmail: null,
      userRole: null,
      subscriptionEndDate: null
    });
  };
  
  // Atualizar assinatura
  const updateSubscription = async (
    subscriptionId: string, 
    isRenewal: boolean = false
  ): Promise<boolean> => {
    if (!authState.isAuthenticated || !authState.authToken) return false;
    
    try {
      const response = await axios.post(
        `${apiUrl}/api/subscriptions/activate`,
        {
          subscriptionId,
          isRenewal
        },
        {
          headers: {
            'Authorization': `Bearer ${authState.authToken}`
          }
        }
      );
      
      if (response.status === 200) {
        const subscriptionEndDate = new Date(response.data.subscriptionEnd);
        
        localStorage.setItem(
          'subscriptionEndDate',
          subscriptionEndDate.getTime().toString()
        );
        
        setAuthState(prev => ({
          ...prev,
          subscriptionEndDate
        }));
        
        return true;
      } else {
        return false;
      }
    } catch (e) {
      console.error('Erro ao atualizar assinatura:', e);
      return false;
    }
  };
  
  // Verificar se o usuário tem permissão para gravar
  const canRecord = (): boolean => {
    return authState.isAuthenticated && 
      (authState.userRole === 'admin' || 
       (authState.subscriptionEndDate !== null && 
        authState.subscriptionEndDate > new Date()));
  };
  
  // Verificar se é admin
  const isAdmin = authState.userRole === 'admin';
  
  // Verificar se tem assinatura ativa
  const hasActiveSubscription = authState.subscriptionEndDate !== null &&
    authState.subscriptionEndDate > new Date();
  
  const value = {
    darkMode,
    toggleTheme,
    apiUrl,
    updateApiUrl,
    isAuthenticated: authState.isAuthenticated,
    authToken: authState.authToken,
    userId: authState.userId,
    userName: authState.userName,
    userEmail: authState.userEmail,
    userRole: authState.userRole,
    subscriptionEndDate: authState.subscriptionEndDate,
    isAdmin,
    hasActiveSubscription,
    login,
    register,
    logout: handleLogout,
    updateSubscription,
    canRecord
  };
  
  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// Hook personalizado para usar o contexto
export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp deve ser usado dentro de um AppProvider');
  }
  return context;
};

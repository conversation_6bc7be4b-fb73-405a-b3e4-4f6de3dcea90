import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  Button,
  CircularProgress,
  Paper,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Psychology as PsychologyIcon,
  Transcribe as TranscribeIcon,
  Security as SecurityIcon,
  HighQuality as QualityIcon,
  Search as SearchIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer, PieChart, Pie, Cell, BarChart, Bar } from 'recharts';

interface DashboardData {
  overview: {
    totalProcessedRecordings: number;
    totalEmbeddings: number;
    totalComplianceAnalyses: number;
    totalQualityAnalyses: number;
    recentSearches: number;
  };
  queues: {
    [key: string]: {
      waiting: number;
      active: number;
      completed: number;
      failed: number;
      delayed: number;
    };
  };
  activity: {
    transcriptions: {
      recent: number;
      avgProcessingTime: number;
      successRate: number;
    };
    compliance: {
      recent: number;
      avgScore: number;
      riskDistribution: { [key: string]: number };
    };
    quality: {
      recent: number;
      avgScore: number;
      qualityDistribution: { [key: string]: number };
    };
    search: {
      recent: number;
      avgResultsPerSearch: number;
      avgProcessingTime: number;
    };
  };
  trends: {
    daily: { [key: string]: any };
  };
}

const AIDashboard: React.FC = () => {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/ai/analytics/dashboard');
      const result = await response.json();

      if (result.success) {
        setData(result.data);
        setLastUpdate(new Date());
      } else {
        setError(result.message || 'Erro ao carregar dashboard');
      }
    } catch (err) {
      setError('Erro de conexão com o serviço de IA');
      console.error('Erro ao carregar dashboard:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
    
    // Auto-refresh a cada 30 segundos
    const interval = setInterval(fetchDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}min`;
  };

  const formatPercentage = (value: number) => {
    return `${Math.round(value * 100)}%`;
  };

  const getQueueStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'info';
      case 'waiting': return 'warning';
      case 'failed': return 'error';
      case 'completed': return 'success';
      default: return 'default';
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'baixo': return '#4caf50';
      case 'medio': return '#ff9800';
      case 'alto': return '#f44336';
      case 'critico': return '#d32f2f';
      default: return '#9e9e9e';
    }
  };

  const getQualityLevelColor = (level: string) => {
    switch (level) {
      case 'excelente': return '#4caf50';
      case 'boa': return '#8bc34a';
      case 'regular': return '#ff9800';
      case 'baixa': return '#f44336';
      case 'muito_baixa': return '#d32f2f';
      default: return '#9e9e9e';
    }
  };

  if (loading && !data) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" action={
        <Button color="inherit" size="small" onClick={fetchDashboardData}>
          Tentar Novamente
        </Button>
      }>
        {error}
      </Alert>
    );
  }

  if (!data) return null;

  // Preparar dados para gráficos
  const dailyTrendsData = Object.entries(data.trends.daily).map(([date, values]: [string, any]) => ({
    date: new Date(date).toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' }),
    transcriptions: values.transcriptions,
    compliance: values.compliance,
    quality: values.quality,
    searches: values.searches
  }));

  const riskDistributionData = Object.entries(data.activity.compliance.riskDistribution).map(([level, count]) => ({
    name: level,
    value: count,
    color: getRiskLevelColor(level)
  }));

  const qualityDistributionData = Object.entries(data.activity.quality.qualityDistribution).map(([level, count]) => ({
    name: level,
    value: count,
    color: getQualityLevelColor(level)
  }));

  return (
    <Box sx={{ maxWidth: 1400, margin: '0 auto', padding: 2 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">
          🤖 Dashboard de Inteligência Artificial
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="caption" color="text.secondary">
            Última atualização: {lastUpdate.toLocaleTimeString('pt-BR')}
          </Typography>
          <Tooltip title="Atualizar dados">
            <IconButton onClick={fetchDashboardData} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Cards de Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <TranscribeIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
              <Typography variant="h4">{data.overview.totalProcessedRecordings}</Typography>
              <Typography variant="body2" color="text.secondary">
                Gravações Processadas
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <SearchIcon sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
              <Typography variant="h4">{data.overview.totalEmbeddings}</Typography>
              <Typography variant="body2" color="text.secondary">
                Embeddings Criados
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <SecurityIcon sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4">{data.overview.totalComplianceAnalyses}</Typography>
              <Typography variant="body2" color="text.secondary">
                Análises LGPD
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <QualityIcon sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
              <Typography variant="h4">{data.overview.totalQualityAnalyses}</Typography>
              <Typography variant="body2" color="text.secondary">
                Análises de Qualidade
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={2.4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <PsychologyIcon sx={{ fontSize: 40, color: 'secondary.main', mb: 1 }} />
              <Typography variant="h4">{data.overview.recentSearches}</Typography>
              <Typography variant="body2" color="text.secondary">
                Buscas Recentes
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Status das Filas */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Status das Filas de Processamento
          </Typography>
          <Grid container spacing={2}>
            {Object.entries(data.queues).map(([queueName, queueData]) => (
              <Grid item xs={12} md={6} lg={3} key={queueName}>
                <Paper sx={{ p: 2 }}>
                  <Typography variant="subtitle1" gutterBottom sx={{ textTransform: 'capitalize' }}>
                    {queueName}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip label={`Ativo: ${queueData.active}`} color="info" size="small" />
                    <Chip label={`Aguardando: ${queueData.waiting}`} color="warning" size="small" />
                    <Chip label={`Concluído: ${queueData.completed}`} color="success" size="small" />
                    {queueData.failed > 0 && (
                      <Chip label={`Falhou: ${queueData.failed}`} color="error" size="small" />
                    )}
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Métricas de Atividade */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Transcrições
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Processadas recentemente: {data.activity.transcriptions.recent}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Tempo médio: {formatTime(data.activity.transcriptions.avgProcessingTime)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Taxa de sucesso: {formatPercentage(data.activity.transcriptions.successRate)}
                </Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={data.activity.transcriptions.successRate * 100}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Busca Semântica
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Buscas recentes: {data.activity.search.recent}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Resultados médios: {data.activity.search.avgResultsPerSearch.toFixed(1)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Tempo médio: {formatTime(data.activity.search.avgProcessingTime)}
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Gráficos */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} lg={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Tendências Diárias
              </Typography>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={dailyTrendsData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <RechartsTooltip />
                  <Line type="monotone" dataKey="transcriptions" stroke="#2196f3" name="Transcrições" />
                  <Line type="monotone" dataKey="compliance" stroke="#ff9800" name="Compliance" />
                  <Line type="monotone" dataKey="quality" stroke="#4caf50" name="Qualidade" />
                  <Line type="monotone" dataKey="searches" stroke="#9c27b0" name="Buscas" />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Distribuição de Riscos LGPD
              </Typography>
              <ResponsiveContainer width="100%" height={250}>
                <PieChart>
                  <Pie
                    data={riskDistributionData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}`}
                  >
                    {riskDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Scores Médios */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Score Médio de Compliance LGPD
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="h3" color="primary">
                  {Math.round(data.activity.compliance.avgScore)}
                </Typography>
                <Box sx={{ flex: 1 }}>
                  <LinearProgress 
                    variant="determinate" 
                    value={data.activity.compliance.avgScore}
                    sx={{ height: 12, borderRadius: 6 }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    {data.activity.compliance.recent} análises recentes
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Score Médio de Qualidade
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Typography variant="h3" color="success.main">
                  {Math.round(data.activity.quality.avgScore)}
                </Typography>
                <Box sx={{ flex: 1 }}>
                  <LinearProgress 
                    variant="determinate" 
                    value={data.activity.quality.avgScore}
                    color="success"
                    sx={{ height: 12, borderRadius: 6 }}
                  />
                  <Typography variant="caption" color="text.secondary">
                    {data.activity.quality.recent} análises recentes
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AIDashboard;

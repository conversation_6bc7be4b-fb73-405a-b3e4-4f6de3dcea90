{"name": "cartorio-ai-service", "version": "1.0.0", "description": "Serviço de IA para Sistema de Cartório - Transcrição, Análise e Busca Semântica", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "openai": "^4.20.1", "fluent-ffmpeg": "^2.1.2", "sharp": "^0.32.6", "redis": "^4.6.10", "bull": "^4.12.2", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "axios": "^1.6.0", "node-cron": "^3.0.3", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3"}}
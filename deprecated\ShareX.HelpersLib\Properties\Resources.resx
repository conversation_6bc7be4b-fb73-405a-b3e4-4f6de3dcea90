﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ShapeType_RegionFreehand" xml:space="preserve">
    <value>Freehand region</value>
  </data>
  <data name="ReplCodeMenuEntry_w_Current_week_name__Local_language_" xml:space="preserve">
    <value>Week name (Local language)</value>
  </data>
  <data name="ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_" xml:space="preserve">
    <value>Settings copied to your clipboard.</value>
  </data>
  <data name="ImgurThumbnailType_Big_Square" xml:space="preserve">
    <value>Big square</value>
  </data>
  <data name="ReplCodeMenuEntry_s_Current_second" xml:space="preserve">
    <value>Second</value>
  </data>
  <data name="TextDestination_CustomTextUploader" xml:space="preserve">
    <value>Custom text uploader</value>
  </data>
  <data name="ProxyMethod_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="ReplCodeMenuEntry_mo_Current_month" xml:space="preserve">
    <value>Month</value>
  </data>
  <data name="CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___" xml:space="preserve">
    <value>Browse for a Cascading Style Sheet...</value>
  </data>
  <data name="Extensions_AddContextMenu_Redo" xml:space="preserve">
    <value>Redo</value>
  </data>
  <data name="HotkeyType_VideoThumbnailer" xml:space="preserve">
    <value>Video thumbnailer</value>
  </data>
  <data name="ShapeType_EffectBlur" xml:space="preserve">
    <value>Blur (B)</value>
  </data>
  <data name="AfterCaptureTasks_ShowQuickTaskMenu" xml:space="preserve">
    <value>Show quick task menu</value>
  </data>
  <data name="CustomUploaderDestinationType_URLShortener" xml:space="preserve">
    <value>URL shortener</value>
  </data>
  <data name="ReplCodeMenuEntry_uln_User_login_name" xml:space="preserve">
    <value>User login name</value>
  </data>
  <data name="HotkeyType_ImageEffects" xml:space="preserve">
    <value>Image effects</value>
  </data>
  <data name="ShapeType_DrawingImageScreen" xml:space="preserve">
    <value>Image (Screen)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available" xml:space="preserve">
    <value>A newer version of {0} is available</value>
  </data>
  <data name="AfterUploadTasks_ShowQRCode" xml:space="preserve">
    <value>Show QR code window</value>
  </data>
  <data name="ShapeType_DrawingSpeechBalloon" xml:space="preserve">
    <value>Speech balloon (S)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date" xml:space="preserve">
    <value>{0} is up to date</value>
  </data>
  <data name="HotkeyType_Category_ScreenRecord" xml:space="preserve">
    <value>Screen record</value>
  </data>
  <data name="PastebinExpiration_H1" xml:space="preserve">
    <value>1 Hour</value>
  </data>
  <data name="HotkeyType_ScrollingCapture" xml:space="preserve">
    <value>Start/Stop scrolling capture</value>
  </data>
  <data name="ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all" xml:space="preserve">
    <value>Auto increment alphanumeric case-sensitive (0 pad left using {n})</value>
  </data>
  <data name="ReplCodeMenuEntry_t_Title_of_active_window" xml:space="preserve">
    <value>Title of window</value>
  </data>
  <data name="AfterCaptureTasks_SendImageToPrinter" xml:space="preserve">
    <value>Print image</value>
  </data>
  <data name="ShapeType_RegionRectangle" xml:space="preserve">
    <value>Rectangle region</value>
  </data>
  <data name="HotkeyType_ToggleActionsToolbar" xml:space="preserve">
    <value>Toggle actions toolbar</value>
  </data>
  <data name="AfterCaptureTasks_PerformActions" xml:space="preserve">
    <value>Perform actions</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfCanvas" xml:space="preserve">
    <value>Percentage of canvas</value>
  </data>
  <data name="ReplCodeMenuCategory_Date_and_Time" xml:space="preserve">
    <value>Date and time</value>
  </data>
  <data name="HotkeyType_ImageCombiner" xml:space="preserve">
    <value>Image combiner</value>
  </data>
  <data name="HotkeyType_RectangleTransparent" xml:space="preserve">
    <value>Capture region (Transparent)</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_" xml:space="preserve">
    <value>Download completed.</value>
  </data>
  <data name="YouTubeVideoPrivacy_Private" xml:space="preserve">
    <value>Private</value>
  </data>
  <data name="AfterUploadTasks_ShareURL" xml:space="preserve">
    <value>Share URL</value>
  </data>
  <data name="CustomUploaderDestinationType_FileUploader" xml:space="preserve">
    <value>File uploader</value>
  </data>
  <data name="ReplCodeMenuEntry_h_Current_hour" xml:space="preserve">
    <value>Hour</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_Update_check_failed" xml:space="preserve">
    <value>Update check failed</value>
  </data>
  <data name="ReplCodeMenuEntry_ms_Current_millisecond" xml:space="preserve">
    <value>Millisecond</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Install" xml:space="preserve">
    <value>Install</value>
  </data>
  <data name="AfterCaptureTasks_UploadImageToHost" xml:space="preserve">
    <value>Upload image to host</value>
  </data>
  <data name="ReplCodeMenuEntry_ix_Auto_increment_hexadecimal" xml:space="preserve">
    <value>Auto increment hexadecimal (0 pad left using {n})</value>
  </data>
  <data name="CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__" xml:space="preserve">
    <value>Cyan: {0:0.0}%, Magenta: {1:0.0}%, Yellow: {2:0.0}%, Key: {3:0.0}%</value>
  </data>
  <data name="HotkeyType_FolderUpload" xml:space="preserve">
    <value>Upload folder</value>
  </data>
  <data name="ReplCodeMenuEntry_mi_Current_minute" xml:space="preserve">
    <value>Minute</value>
  </data>
  <data name="ShapeType_EffectPixelate" xml:space="preserve">
    <value>Pixelate (P)</value>
  </data>
  <data name="ReplCodeMenuEntry_d_Current_day" xml:space="preserve">
    <value>Day</value>
  </data>
  <data name="PastebinExpiration_D1" xml:space="preserve">
    <value>1 Day</value>
  </data>
  <data name="ShapeType_DrawingArrow" xml:space="preserve">
    <value>Arrow (A)</value>
  </data>
  <data name="ShapeType_DrawingSmartEraser" xml:space="preserve">
    <value>Smart eraser</value>
  </data>
  <data name="PastebinPrivacy_Unlisted" xml:space="preserve">
    <value>Unlisted</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_update_is_available" xml:space="preserve">
    <value>Update is available</value>
  </data>
  <data name="HotkeyType_Category_Upload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="Extensions_AddContextMenu_Cut" xml:space="preserve">
    <value>Cut</value>
  </data>
  <data name="FileExistAction_Cancel" xml:space="preserve">
    <value>Do not save</value>
  </data>
  <data name="AfterCaptureTasks_CopyImageToClipboard" xml:space="preserve">
    <value>Copy image to clipboard</value>
  </data>
  <data name="PNGBitDepth_Bit32" xml:space="preserve">
    <value>32 bit</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFActiveWindow" xml:space="preserve">
    <value>Start/Stop screen recording (GIF) using active window region</value>
  </data>
  <data name="HotkeyType_PrintScreen" xml:space="preserve">
    <value>Capture entire screen</value>
  </data>
  <data name="ImageEditorStartMode_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFCustomRegion" xml:space="preserve">
    <value>Start/Stop screen recording (GIF) using pre configured region</value>
  </data>
  <data name="HotkeyType_CustomRegion" xml:space="preserve">
    <value>Capture pre configured region</value>
  </data>
  <data name="ReplCodeMenuCategory_Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="PastebinExpiration_M10" xml:space="preserve">
    <value>10 Minutes</value>
  </data>
  <data name="RegionCaptureAction_SwapToolType" xml:space="preserve">
    <value>Swap tool type</value>
  </data>
  <data name="HotkeyType_RectangleRegion" xml:space="preserve">
    <value>Capture region</value>
  </data>
  <data name="AfterCaptureTasks_DoOCR" xml:space="preserve">
    <value>Recognize text (OCR)</value>
  </data>
  <data name="HotkeyType_ExitShareX" xml:space="preserve">
    <value>Exit ShareX</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_Portable" xml:space="preserve">
    <value>A newer version of {0} is available.
Would you like to download it?</value>
  </data>
  <data name="Helpers_DownloadString_Download_failed_" xml:space="preserve">
    <value>Download failed:</value>
  </data>
  <data name="ShapeType_DrawingTextOutline" xml:space="preserve">
    <value>Text (Outline) (O)</value>
  </data>
  <data name="RegionCaptureAction_CaptureActiveMonitor" xml:space="preserve">
    <value>Capture active monitor</value>
  </data>
  <data name="ImgurThumbnailType_Small_Thumbnail" xml:space="preserve">
    <value>Small thumbnail</value>
  </data>
  <data name="PrintForm_LoadSettings_Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="GIFQuality_Bit4" xml:space="preserve">
    <value>Octree quantizer 16 colors</value>
  </data>
  <data name="AfterUploadTasks_ShowAfterUploadWindow" xml:space="preserve">
    <value>Show "After upload" window</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="Crosshair" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\crosshair.cur;System.Byte[], mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAdjective" xml:space="preserve">
    <value>Random adjective</value>
  </data>
  <data name="Extensions_AddContextMenu_SelectAll" xml:space="preserve">
    <value>Select All</value>
  </data>
  <data name="FileDestination_CustomFileUploader" xml:space="preserve">
    <value>Custom file uploader</value>
  </data>
  <data name="LinearGradientMode_Vertical" xml:space="preserve">
    <value>Vertical</value>
  </data>
  <data name="ReplCodeMenuCategory_Random" xml:space="preserve">
    <value>Random</value>
  </data>
  <data name="CustomUploaderDestinationType_ImageUploader" xml:space="preserve">
    <value>Image uploader</value>
  </data>
  <data name="HotkeyType_HashCheck" xml:space="preserve">
    <value>Hash checker</value>
  </data>
  <data name="HotkeyType_ScreenRecorderActiveWindow" xml:space="preserve">
    <value>Start/Stop screen recording using active window region</value>
  </data>
  <data name="ReplCodeMenuEntry_rn_Random_number_0_to_9" xml:space="preserve">
    <value>Random number 0 to 9 (Repeat using {n})</value>
  </data>
  <data name="HotkeyType_ClipboardUploadWithContentViewer" xml:space="preserve">
    <value>Upload from clipboard with content viewer</value>
  </data>
  <data name="YouTubeVideoPrivacy_Public" xml:space="preserve">
    <value>Public</value>
  </data>
  <data name="HSB_ToString_" xml:space="preserve">
    <value>Hue: {0:0.0}°, Saturation: {1:0.0}%, Brightness: {2:0.0}%</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="HotkeyType_DragDropUpload" xml:space="preserve">
    <value>Drag and drop upload</value>
  </data>
  <data name="PastebinExpiration_N" xml:space="preserve">
    <value>Never</value>
  </data>
  <data name="HotkeyType_StartScreenRecorder" xml:space="preserve">
    <value>Start/Stop screen recording using last region</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="HotkeyType_ImageThumbnailer" xml:space="preserve">
    <value>Image thumbnailer</value>
  </data>
  <data name="ReplCodeMenuEntry_mon_Current_month_name__Local_language_" xml:space="preserve">
    <value>Month name (Local language)</value>
  </data>
  <data name="GIFQuality_Bit8" xml:space="preserve">
    <value>Octree quantizer 256 colors (Slow encoding but better quality)</value>
  </data>
  <data name="ShapeType_DrawingImage" xml:space="preserve">
    <value>Image (File)</value>
  </data>
  <data name="ScreenRecordGIFEncoding_NET" xml:space="preserve">
    <value>.NET (Bad quality)</value>
  </data>
  <data name="ReplCodeMenuEntry_ia_Auto_increment_alphanumeric" xml:space="preserve">
    <value>Auto increment alphanumeric case-insensitive (0 pad left using {n})</value>
  </data>
  <data name="AfterCaptureTasks_AddImageEffects" xml:space="preserve">
    <value>Add image effects</value>
  </data>
  <data name="AfterCaptureTasks_DeleteFile" xml:space="preserve">
    <value>Delete file locally</value>
  </data>
  <data name="ExportImportControl_Serialize_Export_failed_" xml:space="preserve">
    <value>Export failed.</value>
  </data>
  <data name="ReplCodeMenuCategory_Computer" xml:space="preserve">
    <value>Computer</value>
  </data>
  <data name="FileExistAction_UniqueName" xml:space="preserve">
    <value>Append number to the filename</value>
  </data>
  <data name="ImgurThumbnailType_Large_Thumbnail" xml:space="preserve">
    <value>Large thumbnail</value>
  </data>
  <data name="ReplCodeMenuEntry_yy_Current_year__2_digits_" xml:space="preserve">
    <value>Year (2 digits)</value>
  </data>
  <data name="PNGBitDepth_Automatic" xml:space="preserve">
    <value>Automatically detect</value>
  </data>
  <data name="ImageEditorStartMode_PreviousState" xml:space="preserve">
    <value>Previous state</value>
  </data>
  <data name="ShapeType_RegionEllipse" xml:space="preserve">
    <value>Ellipse region</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIF" xml:space="preserve">
    <value>Start/Stop screen recording (GIF)</value>
  </data>
  <data name="YouTubeVideoPrivacy_Unlisted" xml:space="preserve">
    <value>Unlisted</value>
  </data>
  <data name="ObjectListView_ObjectListView_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="ReplCodeMenuCategory_Window" xml:space="preserve">
    <value>Window</value>
  </data>
  <data name="HotkeyType_Ruler" xml:space="preserve">
    <value>Ruler</value>
  </data>
  <data name="ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from" xml:space="preserve">
    <value>URL to download settings from</value>
  </data>
  <data name="ShapeType_DrawingFreehand" xml:space="preserve">
    <value>Freehand (F)</value>
  </data>
  <data name="animals" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\animals.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;utf-8</value>
  </data>
  <data name="ReplCodeMenuEntry_pm_Gets_AM_PM" xml:space="preserve">
    <value>AM/PM</value>
  </data>
  <data name="DirectoryNameEditor_EditValue_Browse_for_a_folder___" xml:space="preserve">
    <value>Browse for a folder...</value>
  </data>
  <data name="LinearGradientMode_BackwardDiagonal" xml:space="preserve">
    <value>Backward diagonal</value>
  </data>
  <data name="ShapeType_DrawingCursor" xml:space="preserve">
    <value>Cursor</value>
  </data>
  <data name="ImgurThumbnailType_Huge_Thumbnail" xml:space="preserve">
    <value>Huge thumbnail</value>
  </data>
  <data name="LinearGradientMode_Horizontal" xml:space="preserve">
    <value>Horizontal</value>
  </data>
  <data name="HotkeyType_AbortScreenRecording" xml:space="preserve">
    <value>Abort screen recording</value>
  </data>
  <data name="ReplCodeMenuEntry_y_Current_year" xml:space="preserve">
    <value>Year</value>
  </data>
  <data name="PastebinExpiration_W2" xml:space="preserve">
    <value>2 Weeks</value>
  </data>
  <data name="ImageEditorStartMode_Fullscreen" xml:space="preserve">
    <value>Fullscreen</value>
  </data>
  <data name="AfterCaptureTasks_CopyFilePathToClipboard" xml:space="preserve">
    <value>Copy file path to clipboard</value>
  </data>
  <data name="HotkeyType_ScreenRecorder" xml:space="preserve">
    <value>Start/Stop screen recording</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFile" xml:space="preserve">
    <value>Save image to file</value>
  </data>
  <data name="ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_" xml:space="preserve">
    <value>File path with output file name extension</value>
  </data>
  <data name="URLSharingServices_GoogleImageSearch" xml:space="preserve">
    <value>Google Lens</value>
  </data>
  <data name="HotkeyType_IndexFolder" xml:space="preserve">
    <value>Directory indexer</value>
  </data>
  <data name="ReplCodeMenuEntry_unix_Unix_timestamp" xml:space="preserve">
    <value>Unix timestamp</value>
  </data>
  <data name="ScreenRecordGIFEncoding_FFmpeg" xml:space="preserve">
    <value>FFmpeg (Good quality)</value>
  </data>
  <data name="DrawImageSizeMode_DontResize" xml:space="preserve">
    <value>Don't resize</value>
  </data>
  <data name="HotkeyType_StopUploads" xml:space="preserve">
    <value>Stop all active uploads</value>
  </data>
  <data name="AfterUploadTasks_OpenURL" xml:space="preserve">
    <value>Open URL</value>
  </data>
  <data name="AfterCaptureTasks_AnnotateImage" xml:space="preserve">
    <value>Open in image editor</value>
  </data>
  <data name="cross" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\cross.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="MyPictureBox_LoadImageAsync_Loading_image___" xml:space="preserve">
    <value>Loading image...</value>
  </data>
  <data name="HotkeyType_LastRegion" xml:space="preserve">
    <value>Capture last region</value>
  </data>
  <data name="Helpers_OpenFolder_Folder_not_exist_" xml:space="preserve">
    <value>Folder does not exist:</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_CurrentVersion" xml:space="preserve">
    <value>Current version</value>
  </data>
  <data name="FileDestination_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="adjectives" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\adjectives.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;iso-8859-1</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_" xml:space="preserve">
    <value>A newer version of {0} is available.
Would you like to download and install it?</value>
  </data>
  <data name="WavFileNameEditor_EditValue_Browse_for_a_sound_file___" xml:space="preserve">
    <value>Browse for a sound file...</value>
  </data>
  <data name="Helpers_OpenFile_File_not_exist_" xml:space="preserve">
    <value>File does not exist:</value>
  </data>
  <data name="Helpers_BrowseFolder_Choose_folder" xml:space="preserve">
    <value>Choose folder</value>
  </data>
  <data name="ExportImportControl_Deserialize_Import_failed_" xml:space="preserve">
    <value>Import failed.</value>
  </data>
  <data name="Extensions_AddContextMenu_Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Extensions_AddContextMenu_Paste" xml:space="preserve">
    <value>Paste</value>
  </data>
  <data name="HotkeyType_QRCodeDecodeFromScreen" xml:space="preserve">
    <value>QR code (Scan screen)</value>
  </data>
  <data name="LinearGradientMode_ForwardDiagonal" xml:space="preserve">
    <value>Forward diagonal</value>
  </data>
  <data name="PNGBitDepth_Bit24" xml:space="preserve">
    <value>24 bit</value>
  </data>
  <data name="ReplCodeMenuEntry_wy_Week_of_year" xml:space="preserve">
    <value>Week of year</value>
  </data>
  <data name="DrawImageSizeMode_AbsoluteSize" xml:space="preserve">
    <value>Absolute size</value>
  </data>
  <data name="HotkeyType_OpenImageHistory" xml:space="preserve">
    <value>Open image history window</value>
  </data>
  <data name="ReplCodeMenuCategory_Incremental" xml:space="preserve">
    <value>Incremental</value>
  </data>
  <data name="RandomEmojiRepeatUsingN" xml:space="preserve">
    <value>Random emoji (Repeat using {n})</value>
  </data>
  <data name="AfterCaptureTasks_SaveThumbnailImageToFile" xml:space="preserve">
    <value>Save thumbnail image to file</value>
  </data>
  <data name="DownloaderForm_StartDownload_Downloading_" xml:space="preserve">
    <value>Downloading...</value>
  </data>
  <data name="RegionCaptureAction_RemoveShapeCancelCapture" xml:space="preserve">
    <value>Remove shape or cancel capture</value>
  </data>
  <data name="ReplCodeMenuEntry_un_User_name" xml:space="preserve">
    <value>User name</value>
  </data>
  <data name="ShapeType_DrawingMagnify" xml:space="preserve">
    <value>Magnify</value>
  </data>
  <data name="CodeMenu_Create_Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="ShapeType_DrawingSticker" xml:space="preserve">
    <value>Sticker</value>
  </data>
  <data name="HotkeyType_QRCode" xml:space="preserve">
    <value>QR code</value>
  </data>
  <data name="PastebinExpiration_W1" xml:space="preserve">
    <value>1 Week</value>
  </data>
  <data name="CustomUploaderDestinationType_URLSharingService" xml:space="preserve">
    <value>URL sharing service</value>
  </data>
  <data name="ShapeType_EffectHighlight" xml:space="preserve">
    <value>Highlight (H)</value>
  </data>
  <data name="GIFQuality_Grayscale" xml:space="preserve">
    <value>Palette quantizer grayscale 256 colors</value>
  </data>
  <data name="GIFQuality_Default" xml:space="preserve">
    <value>Default .NET encoding (Fast encoding but average quality)</value>
  </data>
  <data name="ReplCodeMenuEntry_rx_Random_hexadecimal" xml:space="preserve">
    <value>Random hexadecimal char (Repeat using {n})</value>
  </data>
  <data name="PastebinPrivacy_Private" xml:space="preserve">
    <value>Private (members only)</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAnimal" xml:space="preserve">
    <value>Random animal</value>
  </data>
  <data name="URLSharingServices_CustomURLSharingService" xml:space="preserve">
    <value>Custom URL sharing service</value>
  </data>
  <data name="RegionCaptureAction_CaptureFullscreen" xml:space="preserve">
    <value>Capture fullscreen</value>
  </data>
  <data name="ReplCodeMenuEntry_pn_Process_name_of_active_window" xml:space="preserve">
    <value>Process name of window</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Waiting_" xml:space="preserve">
    <value>Waiting...</value>
  </data>
  <data name="HotkeyType_ImageEditor" xml:space="preserve">
    <value>Image editor</value>
  </data>
  <data name="URLSharingServices_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="HotkeyType_OpenHistory" xml:space="preserve">
    <value>Open history window</value>
  </data>
  <data name="ShapeType_ToolSelect" xml:space="preserve">
    <value>Select and move (M)</value>
  </data>
  <data name="ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric" xml:space="preserve">
    <value>Auto increment by base {n} using alphanumeric (1 &lt; n &lt; 63)</value>
  </data>
  <data name="HotkeyType_CaptureWebpage" xml:space="preserve">
    <value>Webpage capture</value>
  </data>
  <data name="RegionCaptureAction_CancelCapture" xml:space="preserve">
    <value>Cancel capture</value>
  </data>
  <data name="AfterCaptureTasks_ScanQRCode" xml:space="preserve">
    <value>Scan QR code</value>
  </data>
  <data name="HotkeyType_RectangleLight" xml:space="preserve">
    <value>Capture region (Light)</value>
  </data>
  <data name="Loading" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\Loading.gif;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ProxyMethod_Automatic" xml:space="preserve">
    <value>Automatic</value>
  </data>
  <data name="HotkeyType_FileUpload" xml:space="preserve">
    <value>Upload file</value>
  </data>
  <data name="ShareX_Logo" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\ShareX_Logo.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ReplCodeMenuEntry_guid_Random_guid" xml:space="preserve">
    <value>Random GUID</value>
  </data>
  <data name="ShapeType_DrawingLine" xml:space="preserve">
    <value>Line (L)</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_value" xml:space="preserve">
    <value>Copy value</value>
  </data>
  <data name="AfterCaptureTasks_ShowBeforeUploadWindow" xml:space="preserve">
    <value>Show "Before upload" window</value>
  </data>
  <data name="AfterCaptureTasks_ShowInExplorer" xml:space="preserve">
    <value>Show file in explorer</value>
  </data>
  <data name="ImageDestination_CustomImageUploader" xml:space="preserve">
    <value>Custom image uploader</value>
  </data>
  <data name="HotkeyType_Category_ScreenCapture" xml:space="preserve">
    <value>Screen capture</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="HotkeyType_ActiveWindow" xml:space="preserve">
    <value>Capture active window</value>
  </data>
  <data name="ShapeType_DrawingStep" xml:space="preserve">
    <value>Step (I)</value>
  </data>
  <data name="ReplCodeMenuEntry_i_Auto_increment_number" xml:space="preserve">
    <value>Auto increment number (0 pad left using {n})</value>
  </data>
  <data name="HotkeyType_ClipboardUpload" xml:space="preserve">
    <value>Upload from clipboard</value>
  </data>
  <data name="ReplCodeMenuEntry_n_New_line" xml:space="preserve">
    <value>New line</value>
  </data>
  <data name="ReplCodeMenuEntry_mon2_Current_month_name__English_" xml:space="preserve">
    <value>Month name (English)</value>
  </data>
  <data name="HotkeyType_OpenScreenshotsFolder" xml:space="preserve">
    <value>Open screenshots folder</value>
  </data>
  <data name="ReplCodeMenuEntry_width_Gets_image_width" xml:space="preserve">
    <value>Image width</value>
  </data>
  <data name="ReplCodeMenuEntry_w2_Current_week_name__English_" xml:space="preserve">
    <value>Week name (English)</value>
  </data>
  <data name="LoadingSmallWhite" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\LoadingSmallWhite.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ExeFileNameEditor_EditValue_Browse_for_executable___" xml:space="preserve">
    <value>Browse for executable...</value>
  </data>
  <data name="ImageDestination_FileUploader" xml:space="preserve">
    <value>File uploader</value>
  </data>
  <data name="ImageEditorStartMode_AutoSize" xml:space="preserve">
    <value>Auto size</value>
  </data>
  <data name="HotkeyType_None" xml:space="preserve">
    <value>None</value>
  </data>
  <data name="PNGBitDepth_Default" xml:space="preserve">
    <value>Default</value>
  </data>
  <data name="Helpers_CreateDirectoryIfNotExist_Create_failed_" xml:space="preserve">
    <value>Could not create directory.</value>
  </data>
  <data name="ProxyMethod_Manual" xml:space="preserve">
    <value>Manual</value>
  </data>
  <data name="DownloaderForm_ChangeStatus_Status___0_" xml:space="preserve">
    <value>Status: {0}</value>
  </data>
  <data name="HotkeyType_StartScreenRecorderGIF" xml:space="preserve">
    <value>Start/Stop screen recording (GIF) using last region</value>
  </data>
  <data name="ImgurThumbnailType_Small_Square" xml:space="preserve">
    <value>Small square</value>
  </data>
  <data name="HotkeyType_MonitorTest" xml:space="preserve">
    <value>Monitor test</value>
  </data>
  <data name="Extensions_AddContextMenu_Copy" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="AfterUploadTasks_UseURLShortener" xml:space="preserve">
    <value>Shorten URL</value>
  </data>
  <data name="ReplCodeMenuEntry_rf_Random_line_from_file" xml:space="preserve">
    <value>Random line from a file (Use {filepath} to determine the file)</value>
  </data>
  <data name="DownloaderForm_StartDownload_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="HotkeyType_Category_Tools" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="FileDestination_SharedFolder" xml:space="preserve">
    <value>Shared folder</value>
  </data>
  <data name="HotkeyType_ActiveMonitor" xml:space="preserve">
    <value>Capture active monitor</value>
  </data>
  <data name="DownloaderForm_StartDownload_Getting_file_size_" xml:space="preserve">
    <value>Getting file size...</value>
  </data>
  <data name="HotkeyType_Category_Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="clipboard-block" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\clipboard-block.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Filename___0_" xml:space="preserve">
    <value>Filename: {0}</value>
  </data>
  <data name="ShapeType_DrawingEllipse" xml:space="preserve">
    <value>Ellipse (E)</value>
  </data>
  <data name="HotkeyType_ColorPicker" xml:space="preserve">
    <value>Color picker</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>Stop</value>
  </data>
  <data name="TextDestination_FileUploader" xml:space="preserve">
    <value>File uploader</value>
  </data>
  <data name="MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__" xml:space="preserve">
    <value>Loading image: {0}%</value>
  </data>
  <data name="ReplCodeMenuEntry_ra_Random_alphanumeric_char" xml:space="preserve">
    <value>Random alphanumeric char (Repeat using {n})</value>
  </data>
  <data name="ObjectListView_ObjectListView_Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="ShareX_Icon_White" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\ShareX_Icon_White.ico;System.Drawing.Icon, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="HotkeyType_DisableHotkeys" xml:space="preserve">
    <value>Disable/Enable hotkeys</value>
  </data>
  <data name="RegionCaptureAction_None" xml:space="preserve">
    <value>Do nothing</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFileWithDialog" xml:space="preserve">
    <value>Save image to file as...</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_name" xml:space="preserve">
    <value>Copy name</value>
  </data>
  <data name="RegionCaptureAction_RemoveShape" xml:space="preserve">
    <value>Remove shape</value>
  </data>
  <data name="ActionsCodeMenuEntry_FilePath_File_path" xml:space="preserve">
    <value>File path</value>
  </data>
  <data name="ShareX_Icon" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\ShareX_Icon.ico;System.Drawing.Icon, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="SupportedLanguage_Automatic" xml:space="preserve">
    <value>Automatic</value>
  </data>
  <data name="HotkeyType_VideoConverter" xml:space="preserve">
    <value>Video converter</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="FolderSelectDialog_Title_Select_a_folder" xml:space="preserve">
    <value>Select a folder</value>
  </data>
  <data name="LoadingSmallBlack" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\LoadingSmallBlack.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="HotkeyType_OpenMainWindow" xml:space="preserve">
    <value>Open main window</value>
  </data>
  <data name="HotkeyType_ScreenColorPicker" xml:space="preserve">
    <value>Screen color picker</value>
  </data>
  <data name="PrintTextForm_LoadSettings_Name___0___Size___1_" xml:space="preserve">
    <value>Name: {0}, Size: {1}</value>
  </data>
  <data name="HotkeyType_AutoCapture" xml:space="preserve">
    <value>Auto capture</value>
  </data>
  <data name="ShapeType_DrawingRectangle" xml:space="preserve">
    <value>Rectangle (R)</value>
  </data>
  <data name="ImageEditorStartMode_Maximized" xml:space="preserve">
    <value>Maximized</value>
  </data>
  <data name="HotkeyType_ScreenRecorderCustomRegion" xml:space="preserve">
    <value>Start/Stop screen recording using pre configured region</value>
  </data>
  <data name="ScreenRecordGIFEncoding_OctreeQuantizer" xml:space="preserve">
    <value>Octree quantizer (Medium quality)</value>
  </data>
  <data name="Helpers_BrowseFile_Choose_file" xml:space="preserve">
    <value>Choose file</value>
  </data>
  <data name="ReplCodeMenuEntry_height_Gets_image_height" xml:space="preserve">
    <value>Image height</value>
  </data>
  <data name="PastebinExpiration_M1" xml:space="preserve">
    <value>1 Month</value>
  </data>
  <data name="ShapeType_DrawingTextBackground" xml:space="preserve">
    <value>Text (Background) (T)</value>
  </data>
  <data name="RandomNonAmbiguousAlphanumericCharRepeatUsingN" xml:space="preserve">
    <value>Random non ambiguous alphanumeric char (Repeat using {n})</value>
  </data>
  <data name="UrlShortenerType_CustomURLShortener" xml:space="preserve">
    <value>Custom URL shortener</value>
  </data>
  <data name="PastebinPrivacy_Public" xml:space="preserve">
    <value>Public</value>
  </data>
  <data name="FileExistAction_Overwrite" xml:space="preserve">
    <value>Overwrite file</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfWatermark" xml:space="preserve">
    <value>Percentage of image</value>
  </data>
  <data name="HotkeyType_ShortenURL" xml:space="preserve">
    <value>Shorten URL</value>
  </data>
  <data name="CustomUploaderDestinationType_TextUploader" xml:space="preserve">
    <value>Text uploader</value>
  </data>
  <data name="FileExistAction_Ask" xml:space="preserve">
    <value>Ask what to do</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_LatestVersion" xml:space="preserve">
    <value>Latest version</value>
  </data>
  <data name="AfterCaptureTasks_ShowAfterCaptureWindow" xml:space="preserve">
    <value>Show "After capture" window</value>
  </data>
  <data name="pipette" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\pipette.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="HotkeyType_UploadText" xml:space="preserve">
    <value>Upload text</value>
  </data>
  <data name="ShapeType_ToolCrop" xml:space="preserve">
    <value>Crop image (C)</value>
  </data>
  <data name="HotkeyType_UploadURL" xml:space="preserve">
    <value>Upload from URL</value>
  </data>
  <data name="HotkeyType_ImageSplitter" xml:space="preserve">
    <value>Image splitter</value>
  </data>
  <data name="AfterUploadTasks_CopyURLToClipboard" xml:space="preserve">
    <value>Copy URL to clipboard</value>
  </data>
  <data name="ReplCodeMenuEntry_cn_Computer_name" xml:space="preserve">
    <value>Computer name</value>
  </data>
  <data name="HotkeyType_StartAutoCapture" xml:space="preserve">
    <value>Start auto capture using last region</value>
  </data>
  <data name="ImgurThumbnailType_Medium_Thumbnail" xml:space="preserve">
    <value>Medium thumbnail</value>
  </data>
  <data name="Extensions_AddContextMenu_Undo" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="AfterCaptureTasks_CopyFileToClipboard" xml:space="preserve">
    <value>Copy file to clipboard</value>
  </data>
  <data name="tick" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\tick.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ResultOfFirstFile" xml:space="preserve">
    <value>Result of first file:</value>
  </data>
  <data name="ResultOfSecondFile" xml:space="preserve">
    <value>Result of second file:</value>
  </data>
  <data name="Result" xml:space="preserve">
    <value>Result:</value>
  </data>
  <data name="Target" xml:space="preserve">
    <value>Target:</value>
  </data>
  <data name="ArrowHeadDirection_End" xml:space="preserve">
    <value>End</value>
  </data>
  <data name="ArrowHeadDirection_Start" xml:space="preserve">
    <value>Start</value>
  </data>
  <data name="ArrowHeadDirection_Both" xml:space="preserve">
    <value>Both</value>
  </data>
  <data name="StepType_LettersLowercase" xml:space="preserve">
    <value>Letters (Lowercase)</value>
  </data>
  <data name="StepType_LettersUppercase" xml:space="preserve">
    <value>Letters (Uppercase)</value>
  </data>
  <data name="StepType_Numbers" xml:space="preserve">
    <value>Numbers</value>
  </data>
  <data name="StepType_RomanNumeralsLowercase" xml:space="preserve">
    <value>Roman numerals (Lowercase)</value>
  </data>
  <data name="StepType_RomanNumeralsUppercase" xml:space="preserve">
    <value>Roman numerals (Uppercase)</value>
  </data>
  <data name="HotkeyType_ClipboardViewer" xml:space="preserve">
    <value>Clipboard viewer</value>
  </data>
  <data name="HotkeyType_InspectWindow" xml:space="preserve">
    <value>Inspect window</value>
  </data>
  <data name="BorderStyle_Solid" xml:space="preserve">
    <value>Solid</value>
  </data>
  <data name="BorderStyle_Dash" xml:space="preserve">
    <value>Dash</value>
  </data>
  <data name="BorderStyle_Dot" xml:space="preserve">
    <value>Dot</value>
  </data>
  <data name="BorderStyle_DashDot" xml:space="preserve">
    <value>DashDot</value>
  </data>
  <data name="BorderStyle_DashDotDot" xml:space="preserve">
    <value>DashDotDot</value>
  </data>
  <data name="ToastClickAction_CloseNotification" xml:space="preserve">
    <value>Close notification</value>
  </data>
  <data name="ToastClickAction_AnnotateImage" xml:space="preserve">
    <value>Edit image</value>
  </data>
  <data name="ToastClickAction_CopyImageToClipboard" xml:space="preserve">
    <value>Copy image</value>
  </data>
  <data name="ToastClickAction_CopyFile" xml:space="preserve">
    <value>Copy file</value>
  </data>
  <data name="ToastClickAction_CopyFilePath" xml:space="preserve">
    <value>Copy file path</value>
  </data>
  <data name="ToastClickAction_CopyUrl" xml:space="preserve">
    <value>Copy link</value>
  </data>
  <data name="ToastClickAction_OpenFile" xml:space="preserve">
    <value>Open file</value>
  </data>
  <data name="ToastClickAction_OpenFolder" xml:space="preserve">
    <value>Open folder</value>
  </data>
  <data name="ToastClickAction_OpenUrl" xml:space="preserve">
    <value>Open link</value>
  </data>
  <data name="ToastClickAction_Upload" xml:space="preserve">
    <value>Upload file</value>
  </data>
  <data name="ContentAlignment_TopLeft" xml:space="preserve">
    <value>Top left</value>
  </data>
  <data name="ContentAlignment_TopCenter" xml:space="preserve">
    <value>Top center</value>
  </data>
  <data name="ContentAlignment_TopRight" xml:space="preserve">
    <value>Top right</value>
  </data>
  <data name="ContentAlignment_MiddleLeft" xml:space="preserve">
    <value>Middle left</value>
  </data>
  <data name="ContentAlignment_MiddleCenter" xml:space="preserve">
    <value>Middle center</value>
  </data>
  <data name="ContentAlignment_MiddleRight" xml:space="preserve">
    <value>Middle right</value>
  </data>
  <data name="ContentAlignment_BottomLeft" xml:space="preserve">
    <value>Bottom left</value>
  </data>
  <data name="ContentAlignment_BottomCenter" xml:space="preserve">
    <value>Bottom center</value>
  </data>
  <data name="ContentAlignment_BottomRight" xml:space="preserve">
    <value>Bottom right</value>
  </data>
  <data name="URLSharingServices_BingVisualSearch" xml:space="preserve">
    <value>Bing visual search</value>
  </data>
  <data name="EDataType_Default" xml:space="preserve">
    <value>Default</value>
  </data>
  <data name="EDataType_File" xml:space="preserve">
    <value>File</value>
  </data>
  <data name="EDataType_Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="EDataType_Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="EDataType_URL" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="RegionCaptureAction_CaptureLastRegion" xml:space="preserve">
    <value>Capture last region</value>
  </data>
  <data name="HotkeyType_StopScreenRecording" xml:space="preserve">
    <value>Stop screen recording</value>
  </data>
  <data name="HotkeyType_ToggleTrayMenu" xml:space="preserve">
    <value>Toggle tray menu</value>
  </data>
  <data name="ThumbnailViewClickAction_Default" xml:space="preserve">
    <value>Default</value>
  </data>
  <data name="ThumbnailViewClickAction_EditImage" xml:space="preserve">
    <value>Edit image</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFile" xml:space="preserve">
    <value>Open file</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenFolder" xml:space="preserve">
    <value>Open folder</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenImageViewer" xml:space="preserve">
    <value>Open image viewer</value>
  </data>
  <data name="ThumbnailViewClickAction_OpenURL" xml:space="preserve">
    <value>Open URL</value>
  </data>
  <data name="ThumbnailViewClickAction_Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="ImagePreviewLocation_Bottom" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="ImagePreviewLocation_Side" xml:space="preserve">
    <value>Side</value>
  </data>
  <data name="ImagePreviewVisibility_Automatic" xml:space="preserve">
    <value>Automatic</value>
  </data>
  <data name="ImagePreviewVisibility_Hide" xml:space="preserve">
    <value>Hide</value>
  </data>
  <data name="ImagePreviewVisibility_Show" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="TaskViewMode_ListView" xml:space="preserve">
    <value>List view</value>
  </data>
  <data name="TaskViewMode_ThumbnailView" xml:space="preserve">
    <value>Thumbnail view</value>
  </data>
  <data name="ThumbnailTitleLocation_Bottom" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="ThumbnailTitleLocation_Top" xml:space="preserve">
    <value>Top</value>
  </data>
  <data name="HotkeyType_ImageViewer" xml:space="preserve">
    <value>Image viewer</value>
  </data>
  <data name="HotkeyType_OCR" xml:space="preserve">
    <value>OCR</value>
  </data>
  <data name="HotkeyType_BorderlessWindow" xml:space="preserve">
    <value>Borderless window</value>
  </data>
  <data name="AfterCaptureTasks_PinToScreen" xml:space="preserve">
    <value>Pin to screen</value>
  </data>
  <data name="ToastClickAction_PinToScreen" xml:space="preserve">
    <value>Pin to screen</value>
  </data>
  <data name="ShareXImageViewer" xml:space="preserve">
    <value>ShareX - Image viewer</value>
  </data>
  <data name="HotkeyType_PinToScreen" xml:space="preserve">
    <value>Pin to screen</value>
  </data>
  <data name="CutOutEffectType_None" xml:space="preserve">
    <value>No effect</value>
  </data>
  <data name="CutOutEffectType_TornEdge" xml:space="preserve">
    <value>Torn edges</value>
  </data>
  <data name="CutOutEffectType_Wave" xml:space="preserve">
    <value>Wave</value>
  </data>
  <data name="CutOutEffectType_ZigZag" xml:space="preserve">
    <value>Sawtooth</value>
  </data>
  <data name="ShapeType_ToolCutOut" xml:space="preserve">
    <value>Cut out (X)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromClipboard" xml:space="preserve">
    <value>Pin to screen (From clipboard)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromFile" xml:space="preserve">
    <value>Pin to screen (From file)</value>
  </data>
  <data name="HotkeyType_PinToScreenFromScreen" xml:space="preserve">
    <value>Pin to screen (From screen)</value>
  </data>
  <data name="HotkeyType_PauseScreenRecording" xml:space="preserve">
    <value>Pause screen recording</value>
  </data>
  <data name="ShapeType_DrawingFreehandArrow" xml:space="preserve">
    <value>Freehand arrow</value>
  </data>
  <data name="HotkeyType_ImageBeautifier" xml:space="preserve">
    <value>Image beautifier</value>
  </data>
  <data name="AfterCaptureTasks_BeautifyImage" xml:space="preserve">
    <value>Beautify image</value>
  </data>
  <data name="Check" xml:space="preserve">
    <value>Check</value>
  </data>
  <data name="HotkeyType_CustomWindow" xml:space="preserve">
    <value>Capture pre configured window</value>
  </data>
  <data name="UpdateChannel_Dev" xml:space="preserve">
    <value>Dev</value>
  </data>
  <data name="UpdateChannel_PreRelease" xml:space="preserve">
    <value>Pre-release</value>
  </data>
  <data name="UpdateChannel_Release" xml:space="preserve">
    <value>Release</value>
  </data>
  <data name="DownloaderForm_FileDownloader_ProgressChanged_Progress" xml:space="preserve">
    <value>Progress</value>
  </data>
  <data name="DownloaderForm_FileDownloader_ProgressChanged_DownloadSpeed" xml:space="preserve">
    <value>Download speed</value>
  </data>
  <data name="DownloaderForm_FileDownloader_ProgressChanged_FileSize" xml:space="preserve">
    <value>File size</value>
  </data>
  <data name="HotkeyType_ActiveWindowBorderless" xml:space="preserve">
    <value>Make active window borderless</value>
  </data>
  <data name="HotkeyType_ActiveWindowTopMost" xml:space="preserve">
    <value>Make active window top most</value>
  </data>
  <data name="HotkeyType_PinToScreenCloseAll" xml:space="preserve">
    <value>Pin to screen (Close all)</value>
  </data>
  <data name="ImageBeautifierBackgroundType_Color" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="ImageBeautifierBackgroundType_Desktop" xml:space="preserve">
    <value>Desktop</value>
  </data>
  <data name="ImageBeautifierBackgroundType_Gradient" xml:space="preserve">
    <value>Gradient</value>
  </data>
  <data name="ImageBeautifierBackgroundType_Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="ImageBeautifierBackgroundType_Transparent" xml:space="preserve">
    <value>Transparent</value>
  </data>
  <data name="HotkeyType_Metadata" xml:space="preserve">
    <value>Metadata</value>
  </data>
  <data name="HotkeyType_StripMetadata" xml:space="preserve">
    <value>Strip metadata</value>
  </data>
  <data name="HotkeyType_QRCodeScanRegion" xml:space="preserve">
    <value>QR code (Scan region)</value>
  </data>
  <data name="ScrollMethod_DownArrow" xml:space="preserve">
    <value>Down arrow</value>
  </data>
  <data name="ScrollMethod_MouseWheel" xml:space="preserve">
    <value>Mouse wheel</value>
  </data>
  <data name="ScrollMethod_PageDown" xml:space="preserve">
    <value>Page down</value>
  </data>
  <data name="ScrollMethod_ScrollMessage" xml:space="preserve">
    <value>Scroll message</value>
  </data>
  <data name="ToastClickAction_DeleteFile" xml:space="preserve">
    <value>Delete file locally</value>
  </data>
</root>
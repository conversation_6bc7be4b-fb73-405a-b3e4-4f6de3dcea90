﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ShapeType_RegionFreehand" xml:space="preserve">
    <value>Région : Main levée</value>
  </data>
  <data name="ReplCodeMenuEntry_w_Current_week_name__Local_language_" xml:space="preserve">
    <value>Nom de la semaine en cours (dans la langue locale)</value>
  </data>
  <data name="ExportImportControl_tsmiExportClipboard_Click_Settings_copied_to_your_clipboard_" xml:space="preserve">
    <value>Paramètres copiés dans votre presse-papier.</value>
  </data>
  <data name="ImgurThumbnailType_Big_Square" xml:space="preserve">
    <value>Gros carré</value>
  </data>
  <data name="ReplCodeMenuEntry_s_Current_second" xml:space="preserve">
    <value>Seconde actuelle</value>
  </data>
  <data name="TextDestination_CustomTextUploader" xml:space="preserve">
    <value>Service de mise en ligne de textes personnalisé</value>
  </data>
  <data name="ProxyMethod_None" xml:space="preserve">
    <value>Aucun</value>
  </data>
  <data name="ReplCodeMenuEntry_mo_Current_month" xml:space="preserve">
    <value>Mois en cours</value>
  </data>
  <data name="CssFileNameEditor_EditValue_Browse_for_a_Cascading_Style_Sheet___" xml:space="preserve">
    <value>Parcourir et chercher une feuille de style en cascade...</value>
  </data>
  <data name="Extensions_AddContextMenu_Redo" xml:space="preserve">
    <value>Répéter</value>
  </data>
  <data name="HotkeyType_VideoThumbnailer" xml:space="preserve">
    <value>Générateur de miniatures de vidéo</value>
  </data>
  <data name="ShapeType_EffectBlur" xml:space="preserve">
    <value>Effet : Flou</value>
  </data>
  <data name="AfterCaptureTasks_ShowQuickTaskMenu" xml:space="preserve">
    <value>Afficher le menu des tâches rapides</value>
  </data>
  <data name="CustomUploaderDestinationType_URLShortener" xml:space="preserve">
    <value>Raccourcisseur d'URL</value>
  </data>
  <data name="ReplCodeMenuEntry_uln_User_login_name" xml:space="preserve">
    <value>Identifiant de connexion de l'utilisateur</value>
  </data>
  <data name="HotkeyType_ImageEffects" xml:space="preserve">
    <value>Effets d'image</value>
  </data>
  <data name="ShapeType_DrawingImageScreen" xml:space="preserve">
    <value>Dessin : Image (Écran)</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_A_newer_version_of_ShareX_is_available" xml:space="preserve">
    <value>Une nouvelle version de {0} est disponible</value>
  </data>
  <data name="AfterUploadTasks_ShowQRCode" xml:space="preserve">
    <value>Afficher la fenêtre code QR</value>
  </data>
  <data name="ShapeType_DrawingSpeechBalloon" xml:space="preserve">
    <value>Dessin : Bulle de dialogue</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_ShareX_is_up_to_date" xml:space="preserve">
    <value>{0} est à jour</value>
  </data>
  <data name="HotkeyType_Category_ScreenRecord" xml:space="preserve">
    <value>Enregistrer l'écran</value>
  </data>
  <data name="PastebinExpiration_H1" xml:space="preserve">
    <value>1 heure</value>
  </data>
  <data name="HotkeyType_ScrollingCapture" xml:space="preserve">
    <value>Capture avec défilement</value>
  </data>
  <data name="ReplCodeMenuEntry_iAa_Auto_increment_alphanumeric_all" xml:space="preserve">
    <value>Incrémentation alphanumérique automatique sensible à la casse. Ajoutez un 0 au début avec {n}</value>
  </data>
  <data name="ReplCodeMenuEntry_t_Title_of_active_window" xml:space="preserve">
    <value>Titre de la fenêtre active</value>
  </data>
  <data name="AfterCaptureTasks_SendImageToPrinter" xml:space="preserve">
    <value>Imprimer l'image</value>
  </data>
  <data name="ShapeType_RegionRectangle" xml:space="preserve">
    <value>Région : Rectangle</value>
  </data>
  <data name="HotkeyType_ToggleActionsToolbar" xml:space="preserve">
    <value>Activer la barre d'outils des actions</value>
  </data>
  <data name="AfterCaptureTasks_PerformActions" xml:space="preserve">
    <value>Effectuer des actions</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfCanvas" xml:space="preserve">
    <value>Pourcentage du canevas</value>
  </data>
  <data name="ReplCodeMenuCategory_Date_and_Time" xml:space="preserve">
    <value>Date et heure</value>
  </data>
  <data name="HotkeyType_ImageCombiner" xml:space="preserve">
    <value>Combineur d'images</value>
  </data>
  <data name="HotkeyType_RectangleTransparent" xml:space="preserve">
    <value>Capturer une région (Transparent)</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Download_completed_" xml:space="preserve">
    <value>Téléchargement terminé.</value>
  </data>
  <data name="YouTubeVideoPrivacy_Private" xml:space="preserve">
    <value>Privé</value>
  </data>
  <data name="AfterUploadTasks_ShareURL" xml:space="preserve">
    <value>Partager l'URL</value>
  </data>
  <data name="CustomUploaderDestinationType_FileUploader" xml:space="preserve">
    <value>Service de mise en ligne de fichiers</value>
  </data>
  <data name="ReplCodeMenuEntry_h_Current_hour" xml:space="preserve">
    <value>Heure actuelle</value>
  </data>
  <data name="UpdateCheckerLabel_UpdateControls_Update_check_failed" xml:space="preserve">
    <value>Vérification de mise à jour echouée</value>
  </data>
  <data name="ReplCodeMenuEntry_ms_Current_millisecond" xml:space="preserve">
    <value>Milliseconde actuelle</value>
  </data>
  <data name="DownloaderForm_fileDownloader_DownloadCompleted_Install" xml:space="preserve">
    <value>Installer</value>
  </data>
  <data name="AfterCaptureTasks_UploadImageToHost" xml:space="preserve">
    <value>Mettre en ligne l'image vers l'hôte</value>
  </data>
  <data name="ReplCodeMenuEntry_ix_Auto_increment_hexadecimal" xml:space="preserve">
    <value>Incrémentation hexadécimal automatique . Ajoutez un 0 au début avec {n}</value>
  </data>
  <data name="CMYK_ToString_Cyan___0_0_0____Magenta___1_0_0____Yellow___2_0_0____Key___3_0_0__" xml:space="preserve">
    <value>Cyan : {0:0.0}% - Magenta : {1:0.0}% - Jaune : {2:0.0}% - Noir : {3:0.0}%</value>
  </data>
  <data name="HotkeyType_FolderUpload" xml:space="preserve">
    <value>Mise en ligne d'un dossier</value>
  </data>
  <data name="ReplCodeMenuEntry_mi_Current_minute" xml:space="preserve">
    <value>Minute actuelle</value>
  </data>
  <data name="ShapeType_EffectPixelate" xml:space="preserve">
    <value>Effet : Pixeliser</value>
  </data>
  <data name="ReplCodeMenuEntry_d_Current_day" xml:space="preserve">
    <value>Journée en cours</value>
  </data>
  <data name="PastebinExpiration_D1" xml:space="preserve">
    <value>1 jour</value>
  </data>
  <data name="ShapeType_DrawingArrow" xml:space="preserve">
    <value>Dessin : Flèche</value>
  </data>
  <data name="ShapeType_DrawingSmartEraser" xml:space="preserve">
    <value>Gomme intelligente</value>
  </data>
  <data name="PastebinPrivacy_Unlisted" xml:space="preserve">
    <value>Non listés</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_update_is_available" xml:space="preserve">
    <value>Mise à jour disponible</value>
  </data>
  <data name="HotkeyType_Category_Upload" xml:space="preserve">
    <value>Mise en ligne</value>
  </data>
  <data name="Extensions_AddContextMenu_Cut" xml:space="preserve">
    <value>Couper</value>
  </data>
  <data name="FileExistAction_Cancel" xml:space="preserve">
    <value>Ne pas enregistrer</value>
  </data>
  <data name="AfterCaptureTasks_CopyImageToClipboard" xml:space="preserve">
    <value>Copier l'image dans le presse-papier</value>
  </data>
  <data name="PNGBitDepth_Bit32" xml:space="preserve">
    <value>32 bits</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFActiveWindow" xml:space="preserve">
    <value>Démarrer l'enregistrement de l'écran (GIF) en utilisant la région de la fenêtre active</value>
  </data>
  <data name="HotkeyType_PrintScreen" xml:space="preserve">
    <value>Capturer tout l'écran</value>
  </data>
  <data name="ImageEditorStartMode_Normal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIFCustomRegion" xml:space="preserve">
    <value>Démarrer l'enregistrement de l'écran (GIF) en utilisant la région personnalisée</value>
  </data>
  <data name="HotkeyType_CustomRegion" xml:space="preserve">
    <value>Capturer une région personnalisée</value>
  </data>
  <data name="ReplCodeMenuCategory_Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="PastebinExpiration_M10" xml:space="preserve">
    <value>10 minutes</value>
  </data>
  <data name="RegionCaptureAction_SwapToolType" xml:space="preserve">
    <value>Changer le type d'outil</value>
  </data>
  <data name="HotkeyType_RectangleRegion" xml:space="preserve">
    <value>Capturer une région</value>
  </data>
  <data name="AfterCaptureTasks_DoOCR" xml:space="preserve">
    <value>Reconnaître un texte (OCR)</value>
  </data>
  <data name="HotkeyType_ExitShareX" xml:space="preserve">
    <value>Quitter ShareX</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_Portable" xml:space="preserve">
    <value>Une nouvelle version de {0} est disponible.
Voulez-vous la télécharger ?</value>
  </data>
  <data name="Helpers_DownloadString_Download_failed_" xml:space="preserve">
    <value>Échec du téléchargement :</value>
  </data>
  <data name="ShapeType_DrawingTextOutline" xml:space="preserve">
    <value>Dessin : Texte (Contour)</value>
  </data>
  <data name="RegionCaptureAction_CaptureActiveMonitor" xml:space="preserve">
    <value>Capturer l'écran actif</value>
  </data>
  <data name="ImgurThumbnailType_Small_Thumbnail" xml:space="preserve">
    <value>Petites miniatures</value>
  </data>
  <data name="PrintForm_LoadSettings_Print" xml:space="preserve">
    <value>Imprimer</value>
  </data>
  <data name="GIFQuality_Bit4" xml:space="preserve">
    <value>Quantificateur d'octree 16 couleurs</value>
  </data>
  <data name="AfterUploadTasks_ShowAfterUploadWindow" xml:space="preserve">
    <value>Afficher la fenêtre « Après la mise en ligne »</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAdjective" xml:space="preserve">
    <value>Adjectif aléatoire</value>
  </data>
  <data name="Extensions_AddContextMenu_SelectAll" xml:space="preserve">
    <value>Tout sélectionner</value>
  </data>
  <data name="FileDestination_CustomFileUploader" xml:space="preserve">
    <value>Service de mise en ligne de fichiers personnalisé</value>
  </data>
  <data name="LinearGradientMode_Vertical" xml:space="preserve">
    <value>Verticale</value>
  </data>
  <data name="ReplCodeMenuCategory_Random" xml:space="preserve">
    <value>Aléatoire</value>
  </data>
  <data name="CustomUploaderDestinationType_ImageUploader" xml:space="preserve">
    <value>Service de mise en ligne d'images</value>
  </data>
  <data name="HotkeyType_HashCheck" xml:space="preserve">
    <value>Vérifier un hash</value>
  </data>
  <data name="HotkeyType_ScreenRecorderActiveWindow" xml:space="preserve">
    <value>Démarrer l'enregistrement de l'écran en utilisant la région de la fenêtre active</value>
  </data>
  <data name="ReplCodeMenuEntry_rn_Random_number_0_to_9" xml:space="preserve">
    <value>Nombre aléatoire de 0 à 9. Recommencez à l'aide de {n}</value>
  </data>
  <data name="HotkeyType_ClipboardUploadWithContentViewer" xml:space="preserve">
    <value>Mise en ligne depuis le presse-papier avec la visualisation du contenu</value>
  </data>
  <data name="YouTubeVideoPrivacy_Public" xml:space="preserve">
    <value>Public</value>
  </data>
  <data name="HSB_ToString_" xml:space="preserve">
    <value>Teinte : {0:0.0}° - Saturation : {1:0.0}% - Luminosité : {2:0.0}%</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="HotkeyType_DragDropUpload" xml:space="preserve">
    <value>Mise en ligne par glisser-déposer</value>
  </data>
  <data name="PastebinExpiration_N" xml:space="preserve">
    <value>Jamais</value>
  </data>
  <data name="HotkeyType_StartScreenRecorder" xml:space="preserve">
    <value>Débuter l'enregistrement de l'écran en utilisant la dernière région</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="HotkeyType_ImageThumbnailer" xml:space="preserve">
    <value>Générateur de miniatures d'image</value>
  </data>
  <data name="ReplCodeMenuEntry_mon_Current_month_name__Local_language_" xml:space="preserve">
    <value>Nom du mois en cours (dans la langue locale)</value>
  </data>
  <data name="GIFQuality_Bit8" xml:space="preserve">
    <value>Quantificateur d'octree 256 couleurs (Encodage lent mais de meilleure qualité)</value>
  </data>
  <data name="ShapeType_DrawingImage" xml:space="preserve">
    <value>Dessin : Image (Fichier)</value>
  </data>
  <data name="ScreenRecordGIFEncoding_NET" xml:space="preserve">
    <value>.NET (Mauvaise qualité)</value>
  </data>
  <data name="ReplCodeMenuEntry_ia_Auto_increment_alphanumeric" xml:space="preserve">
    <value>Incrémentation alphanumérique automatique insensible à la casse. Ajoutez un 0 au début avec {n}</value>
  </data>
  <data name="AfterCaptureTasks_AddImageEffects" xml:space="preserve">
    <value>Ajouter des effets d'image</value>
  </data>
  <data name="AfterCaptureTasks_DeleteFile" xml:space="preserve">
    <value>Supprimer le fichier local</value>
  </data>
  <data name="ExportImportControl_Serialize_Export_failed_" xml:space="preserve">
    <value>Échec de l'exportation.</value>
  </data>
  <data name="ReplCodeMenuCategory_Computer" xml:space="preserve">
    <value>Ordinateur</value>
  </data>
  <data name="FileExistAction_UniqueName" xml:space="preserve">
    <value>Ajouter un numéro au nom de fichier</value>
  </data>
  <data name="ImgurThumbnailType_Large_Thumbnail" xml:space="preserve">
    <value>Grandes miniatures</value>
  </data>
  <data name="ReplCodeMenuEntry_yy_Current_year__2_digits_" xml:space="preserve">
    <value>Année en cours (2 chiffres)</value>
  </data>
  <data name="PNGBitDepth_Automatic" xml:space="preserve">
    <value>Détecter automatiquement</value>
  </data>
  <data name="ImageEditorStartMode_PreviousState" xml:space="preserve">
    <value>État précédent</value>
  </data>
  <data name="ShapeType_RegionEllipse" xml:space="preserve">
    <value>Région : Ellipse</value>
  </data>
  <data name="HotkeyType_ScreenRecorderGIF" xml:space="preserve">
    <value>Enregistrement d'écran (GIF)</value>
  </data>
  <data name="YouTubeVideoPrivacy_Unlisted" xml:space="preserve">
    <value>Non listés</value>
  </data>
  <data name="ObjectListView_ObjectListView_Name" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="HotkeyType_Ruler" xml:space="preserve">
    <value>Règle</value>
  </data>
  <data name="ExportImportControl_tsmiImportURL_Click_URL_to_download_settings_from" xml:space="preserve">
    <value>Télécharger des paramètres à partir de l'URL</value>
  </data>
  <data name="ShapeType_DrawingFreehand" xml:space="preserve">
    <value>Dessin : Main levée</value>
  </data>
  <data name="ReplCodeMenuEntry_pm_Gets_AM_PM" xml:space="preserve">
    <value>Obtient AM / PM</value>
  </data>
  <data name="DirectoryNameEditor_EditValue_Browse_for_a_folder___" xml:space="preserve">
    <value>Parcourir et chercher un dossier...</value>
  </data>
  <data name="LinearGradientMode_BackwardDiagonal" xml:space="preserve">
    <value>Diagonale en arrière</value>
  </data>
  <data name="ShapeType_DrawingCursor" xml:space="preserve">
    <value>Dessin : Curseur</value>
  </data>
  <data name="ImgurThumbnailType_Huge_Thumbnail" xml:space="preserve">
    <value>Énormes miniatures</value>
  </data>
  <data name="LinearGradientMode_Horizontal" xml:space="preserve">
    <value>Horizontal</value>
  </data>
  <data name="HotkeyType_AbortScreenRecording" xml:space="preserve">
    <value>Annuler l'enregistrement d'écran</value>
  </data>
  <data name="ReplCodeMenuEntry_y_Current_year" xml:space="preserve">
    <value>Année en cours</value>
  </data>
  <data name="PastebinExpiration_W2" xml:space="preserve">
    <value>2 semaines</value>
  </data>
  <data name="ImageEditorStartMode_Fullscreen" xml:space="preserve">
    <value>Plein écran</value>
  </data>
  <data name="AfterCaptureTasks_CopyFilePathToClipboard" xml:space="preserve">
    <value>Copier le chemin du fichier dans le presse-papier</value>
  </data>
  <data name="HotkeyType_ScreenRecorder" xml:space="preserve">
    <value>Enregistrement d'écran</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFile" xml:space="preserve">
    <value>Enregistrer l'image comme fichier</value>
  </data>
  <data name="ActionsCodeMenuEntry_OutputFilePath_File_path_without_extension____Output_file_name_extension_" xml:space="preserve">
    <value>Chemin du fichier sans extension + Option « Extension du nom de fichier de sortie »</value>
  </data>
  <data name="URLSharingServices_GoogleImageSearch" xml:space="preserve">
    <value>Recherche d'images Google</value>
  </data>
  <data name="HotkeyType_IndexFolder" xml:space="preserve">
    <value>Indexer le dossier</value>
  </data>
  <data name="ReplCodeMenuEntry_unix_Unix_timestamp" xml:space="preserve">
    <value>Heure Unix</value>
  </data>
  <data name="ScreenRecordGIFEncoding_FFmpeg" xml:space="preserve">
    <value>FFmpeg (Bonne qualité)</value>
  </data>
  <data name="HotkeyType_TweetMessage" xml:space="preserve">
    <value>Tweeter</value>
  </data>
  <data name="DrawImageSizeMode_DontResize" xml:space="preserve">
    <value>Ne pas redimensionner</value>
  </data>
  <data name="HotkeyType_StopUploads" xml:space="preserve">
    <value>Arrêter toutes les mises en ligne en cours</value>
  </data>
  <data name="AfterUploadTasks_OpenURL" xml:space="preserve">
    <value>Ouvrir l'URL</value>
  </data>
  <data name="AfterCaptureTasks_AnnotateImage" xml:space="preserve">
    <value>Modifier l'image</value>
  </data>
  <data name="MyPictureBox_LoadImageAsync_Loading_image___" xml:space="preserve">
    <value>Chargement de l'image...</value>
  </data>
  <data name="HotkeyType_LastRegion" xml:space="preserve">
    <value>Capturer la dernière région</value>
  </data>
  <data name="Helpers_OpenFolder_Folder_not_exist_" xml:space="preserve">
    <value>Le dossier n'existe pas :</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_CurrentVersion" xml:space="preserve">
    <value>Version actuelle</value>
  </data>
  <data name="FileDestination_Email" xml:space="preserve">
    <value>Adresse e-mail</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_" xml:space="preserve">
    <value>Une nouvelle version de {0} est disponible.
Voulez-vous la télécharger et l'installer ?</value>
  </data>
  <data name="WavFileNameEditor_EditValue_Browse_for_a_sound_file___" xml:space="preserve">
    <value>Parcourir et chercher un fichier son...</value>
  </data>
  <data name="Helpers_OpenFile_File_not_exist_" xml:space="preserve">
    <value>Le fichier n'existe pas :</value>
  </data>
  <data name="Helpers_BrowseFolder_Choose_folder" xml:space="preserve">
    <value>Choisir le chemin du dossier</value>
  </data>
  <data name="ExportImportControl_Deserialize_Import_failed_" xml:space="preserve">
    <value>Échec de l'import.</value>
  </data>
  <data name="Extensions_AddContextMenu_Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="Extensions_AddContextMenu_Paste" xml:space="preserve">
    <value>Coller</value>
  </data>
  <data name="HotkeyType_QRCodeDecodeFromScreen" xml:space="preserve">
    <value>Code QR (Décoder depuis l'écran)</value>
  </data>
  <data name="LinearGradientMode_ForwardDiagonal" xml:space="preserve">
    <value>Diagonale en avant</value>
  </data>
  <data name="PNGBitDepth_Bit24" xml:space="preserve">
    <value>24 bits</value>
  </data>
  <data name="ReplCodeMenuEntry_wy_Week_of_year" xml:space="preserve">
    <value>Semaine de l'année</value>
  </data>
  <data name="DrawImageSizeMode_AbsoluteSize" xml:space="preserve">
    <value>Taille absolue</value>
  </data>
  <data name="HotkeyType_OpenImageHistory" xml:space="preserve">
    <value>Afficher la fenêtre « Historique des images »</value>
  </data>
  <data name="ReplCodeMenuCategory_Incremental" xml:space="preserve">
    <value>Incrémentation</value>
  </data>
  <data name="RandomEmojiRepeatUsingN" xml:space="preserve">
    <value>Emoji aléatoire. Répéter avec {n}</value>
  </data>
  <data name="AfterCaptureTasks_SaveThumbnailImageToFile" xml:space="preserve">
    <value>Enregistrer la miniature de l'image comme fichier</value>
  </data>
  <data name="DownloaderForm_StartDownload_Downloading_" xml:space="preserve">
    <value>Téléchargement en cours.</value>
  </data>
  <data name="RegionCaptureAction_RemoveShapeCancelCapture" xml:space="preserve">
    <value>Supprimer une forme ou annuler la capture</value>
  </data>
  <data name="ReplCodeMenuEntry_un_User_name" xml:space="preserve">
    <value>Nom d'utilisateur</value>
  </data>
  <data name="ShapeType_DrawingMagnify" xml:space="preserve">
    <value>Utiliser la loupe</value>
  </data>
  <data name="CodeMenu_Create_Close" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="ShapeType_DrawingSticker" xml:space="preserve">
    <value>Autocollant</value>
  </data>
  <data name="HotkeyType_QRCode" xml:space="preserve">
    <value>Code QR</value>
  </data>
  <data name="PastebinExpiration_W1" xml:space="preserve">
    <value>1 semaine</value>
  </data>
  <data name="CustomUploaderDestinationType_URLSharingService" xml:space="preserve">
    <value>Service de partage d'URL</value>
  </data>
  <data name="ShapeType_EffectHighlight" xml:space="preserve">
    <value>Effet : Surligner</value>
  </data>
  <data name="GIFQuality_Grayscale" xml:space="preserve">
    <value>Palette de quantification en niveaux de gris 256 couleurs</value>
  </data>
  <data name="GIFQuality_Default" xml:space="preserve">
    <value>Encodage par défaut .NET (Encodage rapide mais de qualité moyenne)</value>
  </data>
  <data name="ReplCodeMenuEntry_rx_Random_hexadecimal" xml:space="preserve">
    <value>Caractère hexadécimal aléatoire. Recommencez à l'aide de {n}</value>
  </data>
  <data name="PastebinPrivacy_Private" xml:space="preserve">
    <value>Privé (membres uniquement)</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="CodeMenuEntryFilename_RandomAnimal" xml:space="preserve">
    <value>Animal aléatoire</value>
  </data>
  <data name="URLSharingServices_CustomURLSharingService" xml:space="preserve">
    <value>Service de partage d'URL personnalisé</value>
  </data>
  <data name="RegionCaptureAction_CaptureFullscreen" xml:space="preserve">
    <value>Capture plein écran</value>
  </data>
  <data name="ReplCodeMenuEntry_pn_Process_name_of_active_window" xml:space="preserve">
    <value>Nom du processus de la fenêtre active</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Waiting_" xml:space="preserve">
    <value>En attente.</value>
  </data>
  <data name="HotkeyType_ImageEditor" xml:space="preserve">
    <value>Éditeur d'image</value>
  </data>
  <data name="URLSharingServices_Email" xml:space="preserve">
    <value>Adresse e-mail</value>
  </data>
  <data name="HotkeyType_OpenHistory" xml:space="preserve">
    <value>Afficher la fenêtre de l'historique</value>
  </data>
  <data name="ShapeType_ToolSelect" xml:space="preserve">
    <value>Sélectionner et déplacer (M)</value>
  </data>
  <data name="ReplCodeMenuEntry_ib_Auto_increment_base_alphanumeric" xml:space="preserve">
    <value>Incrémentation automatique en base {n} (1 &lt; n &lt; 63)</value>
  </data>
  <data name="HotkeyType_CaptureWebpage" xml:space="preserve">
    <value>Capturer une page Web</value>
  </data>
  <data name="RegionCaptureAction_CancelCapture" xml:space="preserve">
    <value>Annuler la capture</value>
  </data>
  <data name="AfterCaptureTasks_ScanQRCode" xml:space="preserve">
    <value>Scanner code QR</value>
  </data>
  <data name="HotkeyType_RectangleLight" xml:space="preserve">
    <value>Capturer une région (Simple)</value>
  </data>
  <data name="ProxyMethod_Automatic" xml:space="preserve">
    <value>Automatique</value>
  </data>
  <data name="HotkeyType_FileUpload" xml:space="preserve">
    <value>Mise en ligne d'un fichier</value>
  </data>
  <data name="ReplCodeMenuEntry_guid_Random_guid" xml:space="preserve">
    <value>GUID aléatoire</value>
  </data>
  <data name="ShapeType_DrawingLine" xml:space="preserve">
    <value>Dessin : Ligne</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_value" xml:space="preserve">
    <value>Copier valeur</value>
  </data>
  <data name="AfterCaptureTasks_ShowBeforeUploadWindow" xml:space="preserve">
    <value>Afficher la fenêtre « Avant la mise en ligne »</value>
  </data>
  <data name="AfterCaptureTasks_ShowInExplorer" xml:space="preserve">
    <value>Afficher le fichier dans l'explorateur</value>
  </data>
  <data name="ImageDestination_CustomImageUploader" xml:space="preserve">
    <value>Service de mise en ligne d'images personnalisé</value>
  </data>
  <data name="HotkeyType_Category_ScreenCapture" xml:space="preserve">
    <value>Capture d'écran</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_No" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="HotkeyType_ActiveWindow" xml:space="preserve">
    <value>Capturer la fenêtre active</value>
  </data>
  <data name="ShapeType_DrawingStep" xml:space="preserve">
    <value>Dessin : Etape</value>
  </data>
  <data name="ReplCodeMenuEntry_i_Auto_increment_number" xml:space="preserve">
    <value>Incrémentation automatique du numéro. Ajoutez un 0 au début avec {n}</value>
  </data>
  <data name="HotkeyType_ClipboardUpload" xml:space="preserve">
    <value>Mise en ligne depuis le presse-papier</value>
  </data>
  <data name="ReplCodeMenuEntry_n_New_line" xml:space="preserve">
    <value>Nouvelle ligne</value>
  </data>
  <data name="ReplCodeMenuEntry_mon2_Current_month_name__English_" xml:space="preserve">
    <value>Nom du mois en cours (en anglais)</value>
  </data>
  <data name="HotkeyType_OpenScreenshotsFolder" xml:space="preserve">
    <value>Ouvrir le dossier des captures d'écran</value>
  </data>
  <data name="ReplCodeMenuEntry_width_Gets_image_width" xml:space="preserve">
    <value>Largeur de l'image</value>
  </data>
  <data name="ReplCodeMenuEntry_w2_Current_week_name__English_" xml:space="preserve">
    <value>Nom de la semaine en cours (en anglais)</value>
  </data>
  <data name="ExeFileNameEditor_EditValue_Browse_for_executable___" xml:space="preserve">
    <value>Parcourir et chercher un exécutable...</value>
  </data>
  <data name="ImageDestination_FileUploader" xml:space="preserve">
    <value>Service de mise en ligne de fichiers</value>
  </data>
  <data name="ImageEditorStartMode_AutoSize" xml:space="preserve">
    <value>Dimensionnement automatique</value>
  </data>
  <data name="HotkeyType_None" xml:space="preserve">
    <value>Aucun</value>
  </data>
  <data name="PNGBitDepth_Default" xml:space="preserve">
    <value>Par défaut</value>
  </data>
  <data name="Helpers_CreateDirectoryIfNotExist_Create_failed_" xml:space="preserve">
    <value>Impossible de créer le dossier. Vérifiez votre chemin d'accès.</value>
  </data>
  <data name="ProxyMethod_Manual" xml:space="preserve">
    <value>Manuel</value>
  </data>
  <data name="DownloaderForm_ChangeStatus_Status___0_" xml:space="preserve">
    <value>Statut : {0}</value>
  </data>
  <data name="HotkeyType_StartScreenRecorderGIF" xml:space="preserve">
    <value>Débuter l'enregistrement de l'écran (GIF) en utilisant la dernière région</value>
  </data>
  <data name="ImgurThumbnailType_Small_Square" xml:space="preserve">
    <value>Petit carré</value>
  </data>
  <data name="HotkeyType_MonitorTest" xml:space="preserve">
    <value>Test d'écran</value>
  </data>
  <data name="Extensions_AddContextMenu_Copy" xml:space="preserve">
    <value>Copier</value>
  </data>
  <data name="AfterUploadTasks_UseURLShortener" xml:space="preserve">
    <value>Raccourcir l'URL</value>
  </data>
  <data name="ReplCodeMenuEntry_rf_Random_line_from_file" xml:space="preserve">
    <value>Ligne aléatoire d'un fichier. Utilisez {chemin} pour indiquer le fichier</value>
  </data>
  <data name="DownloaderForm_StartDownload_Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="HotkeyType_Category_Tools" xml:space="preserve">
    <value>Outils</value>
  </data>
  <data name="FileDestination_SharedFolder" xml:space="preserve">
    <value>Dossier partagé</value>
  </data>
  <data name="HotkeyType_ActiveMonitor" xml:space="preserve">
    <value>Capturer l'écran actif</value>
  </data>
  <data name="DownloaderForm_StartDownload_Getting_file_size_" xml:space="preserve">
    <value>Obtention de la taille du fichier.</value>
  </data>
  <data name="HotkeyType_Category_Other" xml:space="preserve">
    <value>Autre</value>
  </data>
  <data name="DownloaderForm_DownloaderForm_Filename___0_" xml:space="preserve">
    <value>Nom du fichier : {0}</value>
  </data>
  <data name="ShapeType_DrawingEllipse" xml:space="preserve">
    <value>Dessin : Ellipse</value>
  </data>
  <data name="HotkeyType_ColorPicker" xml:space="preserve">
    <value>Sélecteur de couleurs</value>
  </data>
  <data name="Stop" xml:space="preserve">
    <value>Arrêter</value>
  </data>
  <data name="TextDestination_FileUploader" xml:space="preserve">
    <value>Service de mise en ligne de fichiers</value>
  </data>
  <data name="MyPictureBox_pbMain_LoadProgressChanged_Loading_image___0__" xml:space="preserve">
    <value>Chargement de l'image : {0}%</value>
  </data>
  <data name="ReplCodeMenuEntry_ra_Random_alphanumeric_char" xml:space="preserve">
    <value>Caractère alphanumérique aléatoire. Recommencez à l'aide de {n}</value>
  </data>
  <data name="ObjectListView_ObjectListView_Value" xml:space="preserve">
    <value>Valeur</value>
  </data>
  <data name="HotkeyType_DisableHotkeys" xml:space="preserve">
    <value>Activer/Désactiver les raccourcis clavier</value>
  </data>
  <data name="RegionCaptureAction_None" xml:space="preserve">
    <value>Ne rien faire</value>
  </data>
  <data name="AfterCaptureTasks_SaveImageToFileWithDialog" xml:space="preserve">
    <value>Enregistrer l'image comme fichier en tant que...</value>
  </data>
  <data name="ObjectListView_ObjectListView_Copy_name" xml:space="preserve">
    <value>Copier nom</value>
  </data>
  <data name="RegionCaptureAction_RemoveShape" xml:space="preserve">
    <value>Supprimer une forme</value>
  </data>
  <data name="ActionsCodeMenuEntry_FilePath_File_path" xml:space="preserve">
    <value>Chemin du fichier</value>
  </data>
  <data name="SupportedLanguage_Automatic" xml:space="preserve">
    <value>Automatique</value>
  </data>
  <data name="HotkeyType_VideoConverter" xml:space="preserve">
    <value>Convertisseur vidéo</value>
  </data>
  <data name="MyMessageBox_MyMessageBox_Cancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="FolderSelectDialog_Title_Select_a_folder" xml:space="preserve">
    <value>Sélectionner un dossier</value>
  </data>
  <data name="HotkeyType_OpenMainWindow" xml:space="preserve">
    <value>Ouvrir la fenêtre principale</value>
  </data>
  <data name="HotkeyType_ScreenColorPicker" xml:space="preserve">
    <value>Sélecteur de couleurs de l'écran</value>
  </data>
  <data name="PrintTextForm_LoadSettings_Name___0___Size___1_" xml:space="preserve">
    <value>Nom : {0}, taille : {1}</value>
  </data>
  <data name="HotkeyType_AutoCapture" xml:space="preserve">
    <value>Capture automatique</value>
  </data>
  <data name="ShapeType_DrawingRectangle" xml:space="preserve">
    <value>Dessin : Rectangle</value>
  </data>
  <data name="ImageEditorStartMode_Maximized" xml:space="preserve">
    <value>Agrandi</value>
  </data>
  <data name="HotkeyType_ScreenRecorderCustomRegion" xml:space="preserve">
    <value>Démarrer l'enregistrement de l'écran en utilisant la région personnalisée</value>
  </data>
  <data name="ScreenRecordGIFEncoding_OctreeQuantizer" xml:space="preserve">
    <value>Quantification Octree (Qualité moyenne)</value>
  </data>
  <data name="Helpers_BrowseFile_Choose_file" xml:space="preserve">
    <value>Choisir un fichier</value>
  </data>
  <data name="ReplCodeMenuEntry_height_Gets_image_height" xml:space="preserve">
    <value>Hauteur de l'image</value>
  </data>
  <data name="PastebinExpiration_M1" xml:space="preserve">
    <value>1 mois</value>
  </data>
  <data name="ShapeType_DrawingTextBackground" xml:space="preserve">
    <value>Dessin : Texte (Arrière-plan)</value>
  </data>
  <data name="RandomNonAmbiguousAlphanumericCharRepeatUsingN" xml:space="preserve">
    <value>Caractères alphanumériques aléatoires non ambigus. Répéter en utilisant {n}</value>
  </data>
  <data name="UrlShortenerType_CustomURLShortener" xml:space="preserve">
    <value>Raccourcisseur d'URL personnalisé</value>
  </data>
  <data name="PastebinPrivacy_Public" xml:space="preserve">
    <value>Public</value>
  </data>
  <data name="FileExistAction_Overwrite" xml:space="preserve">
    <value>Écraser le fichier</value>
  </data>
  <data name="DrawImageSizeMode_PercentageOfWatermark" xml:space="preserve">
    <value>Pourcentage de l'image</value>
  </data>
  <data name="HotkeyType_ShortenURL" xml:space="preserve">
    <value>Raccourcir l'URL</value>
  </data>
  <data name="CustomUploaderDestinationType_TextUploader" xml:space="preserve">
    <value>Service de mise en ligne de textes</value>
  </data>
  <data name="FileExistAction_Ask" xml:space="preserve">
    <value>Demander ce qu'il faut faire</value>
  </data>
  <data name="UpdateMessageBox_UpdateMessageBox_LatestVersion" xml:space="preserve">
    <value>Dernière version</value>
  </data>
  <data name="AfterCaptureTasks_ShowAfterCaptureWindow" xml:space="preserve">
    <value>Afficher la fenêtre « Après la capture »</value>
  </data>
  <data name="HotkeyType_UploadText" xml:space="preserve">
    <value>Mettre en ligne un texte</value>
  </data>
  <data name="ShapeType_ToolCrop" xml:space="preserve">
    <value>Rogner l'image (C)</value>
  </data>
  <data name="HotkeyType_UploadURL" xml:space="preserve">
    <value>Mise en ligne depuis l'URL</value>
  </data>
  <data name="HotkeyType_ImageSplitter" xml:space="preserve">
    <value>Découpeur d'image</value>
  </data>
  <data name="AfterUploadTasks_CopyURLToClipboard" xml:space="preserve">
    <value>Copier l'URL dans le presse-papier</value>
  </data>
  <data name="ReplCodeMenuEntry_cn_Computer_name" xml:space="preserve">
    <value>Nom de l'ordinateur</value>
  </data>
  <data name="HotkeyType_StartAutoCapture" xml:space="preserve">
    <value>Débuter la capture automatique en utilisant la dernière région</value>
  </data>
  <data name="ImgurThumbnailType_Medium_Thumbnail" xml:space="preserve">
    <value>Moyennes miniatures</value>
  </data>
  <data name="Extensions_AddContextMenu_Undo" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="AfterCaptureTasks_CopyFileToClipboard" xml:space="preserve">
    <value>Copier le fichier dans le presse-papier</value>
  </data>
  <data name="ResultOfFirstFile" xml:space="preserve">
    <value>Résultat du premier fichier :</value>
  </data>
  <data name="ResultOfSecondFile" xml:space="preserve">
    <value>Résultat du second fichier :</value>
  </data>
  <data name="Result" xml:space="preserve">
    <value>Résultat :</value>
  </data>
  <data name="Target" xml:space="preserve">
    <value>Cible :</value>
  </data>
  <data name="ArrowHeadDirection_End" xml:space="preserve">
    <value>Fin</value>
  </data>
  <data name="ArrowHeadDirection_Start" xml:space="preserve">
    <value>Début</value>
  </data>
  <data name="ArrowHeadDirection_Both" xml:space="preserve">
    <value>Les deux</value>
  </data>
  <data name="HotkeyType_Metadata" xml:space="preserve">
    <value />
  </data>
</root>
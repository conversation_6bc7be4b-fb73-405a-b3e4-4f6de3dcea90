services:
  backend:
    build: 
      context: ./backend
    ports:
      - "3001:3001"
    volumes:
      - ./storage:/app/storage
    environment:
      - NODE_ENV=production
      - DB_PATH=/app/storage/database.sqlite
      - ENCRYPTION_KEY=change_this_to_a_secure_key_in_production
      - PORT=3001
    networks:
      - app-network
    restart: unless-stopped

  latex-service:
    build:
      context: ./latex-service
    ports:
      - "3002:3002"
    volumes:
      - ./storage:/app/storage
    environment:
      - BACKEND_URL=http://backend:3001
      - PORT=3002
    networks:
      - app-network
    depends_on:
      - backend
      
  ai-service:
    build:
      context: ./ai-service
    ports:
      - "3003:3003"
    volumes:
      - ./storage:/app/storage
    environment:
      - NODE_ENV=production
      - PORT=3003
      - BACKEND_URL=http://backend:3001
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - OPENAI_API_KEY=${OPENAI_API_KEY:-demo_key_placeholder}
      - LOG_LEVEL=info
      - MAX_CONCURRENT_JOBS=3
      - MAX_FILE_SIZE=104857600
    networks:
      - app-network
    depends_on:
      - backend
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - app-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  web-app:
    build:
      context: ./web-app
    ports:
      - "80:80"
    networks:
      - app-network
    depends_on:
      - backend
      - ai-service
    environment:
      - REACT_APP_API_URL=http://localhost:3001
      - REACT_APP_AI_API_URL=http://localhost:3003

networks:
  app-network:
    driver: bridge

volumes:
  storage:
  redis-data:

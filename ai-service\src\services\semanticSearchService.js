const OpenAI = require('openai');
const winston = require('winston');
const { v4: uuidv4 } = require('uuid');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  defaultMeta: { service: 'semantic-search-service' },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: '/app/logs/semantic-search.log' })
  ]
});

class SemanticSearchService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.embeddingModel = 'text-embedding-3-small';
    this.maxTokens = 8000;
  }

  /**
   * Criar embedding de conteúdo
   */
  async createContentEmbedding(text) {
    try {
      if (!text || text.trim().length === 0) {
        throw new Error('Texto vazio para embedding');
      }

      // Truncar texto se necessário
      const truncatedText = text.substring(0, this.maxTokens);

      const response = await this.openai.embeddings.create({
        model: this.embeddingModel,
        input: truncatedText,
        encoding_format: 'float'
      });

      return {
        embedding: response.data[0].embedding,
        model: this.embeddingModel,
        tokens: response.usage.total_tokens,
        originalLength: text.length,
        truncated: text.length > this.maxTokens
      };

    } catch (error) {
      logger.error('Erro ao criar embedding:', error);
      throw new Error(`Falha na criação de embedding: ${error.message}`);
    }
  }

  /**
   * Buscar conteúdo similar usando embeddings
   */
  async findSimilarContent(query, options = {}) {
    const sessionId = uuidv4();
    const startTime = Date.now();

    try {
      logger.info(`Iniciando busca semântica [${sessionId}]`, { 
        query: query.substring(0, 100),
        options 
      });

      // Criar embedding da consulta
      const queryEmbedding = await this.createContentEmbedding(query);

      // Configurar filtros
      const filters = {
        minSimilarity: options.minSimilarity || 0.7,
        maxResults: options.maxResults || 20,
        dateFrom: options.dateFrom,
        dateTo: options.dateTo,
        userFilter: options.userFilter,
        typeFilter: options.typeFilter
      };

      // Buscar no banco de dados
      const aiDatabase = require('../database/aiDatabase');
      const results = await aiDatabase.searchSimilarContent(queryEmbedding.embedding, filters);

      // Processar e enriquecer resultados
      const enrichedResults = await this.enrichSearchResults(results, query);

      const response = {
        sessionId,
        query,
        results: enrichedResults,
        totalFound: results.length,
        processingTime: Date.now() - startTime,
        filters,
        embedding: {
          model: queryEmbedding.model,
          tokens: queryEmbedding.tokens
        }
      };

      logger.info(`Busca semântica concluída [${sessionId}]`, {
        totalFound: results.length,
        processingTime: response.processingTime
      });

      return response;

    } catch (error) {
      logger.error(`Erro na busca semântica [${sessionId}]:`, error);
      throw new Error(`Falha na busca semântica: ${error.message}`);
    }
  }

  /**
   * Enriquecer resultados da busca
   */
  async enrichSearchResults(results, originalQuery) {
    return results.map(result => {
      // Calcular score de relevância
      const relevanceScore = this.calculateRelevanceScore(result, originalQuery);
      
      // Extrair snippet relevante
      const snippet = this.extractRelevantSnippet(result.transcription, originalQuery);
      
      // Identificar palavras-chave
      const keywords = this.extractKeywords(result.transcription);

      return {
        recordingId: result.recording_id,
        filename: result.filename,
        similarity: Math.round(result.similarity * 100) / 100,
        relevanceScore,
        snippet,
        keywords: keywords.slice(0, 10),
        metadata: {
          createdAt: result.created_at,
          userName: result.user_name,
          purpose: result.purpose,
          duration: result.duration,
          wordCount: result.word_count
        },
        highlights: this.highlightMatches(snippet, originalQuery)
      };
    });
  }

  /**
   * Calcular score de relevância
   */
  calculateRelevanceScore(result, query) {
    let score = result.similarity;

    // Boost para correspondências exatas
    const queryWords = query.toLowerCase().split(/\s+/);
    const transcription = result.transcription.toLowerCase();
    
    queryWords.forEach(word => {
      if (transcription.includes(word)) {
        score += 0.1;
      }
    });

    // Boost para gravações recentes
    const daysSinceCreation = (Date.now() - new Date(result.created_at)) / (1000 * 60 * 60 * 24);
    if (daysSinceCreation < 30) {
      score += 0.05;
    }

    return Math.min(1, score);
  }

  /**
   * Extrair snippet relevante
   */
  extractRelevantSnippet(text, query, maxLength = 200) {
    const queryWords = query.toLowerCase().split(/\s+/);
    const sentences = text.split(/[.!?]+/);
    
    // Encontrar sentença mais relevante
    let bestSentence = sentences[0] || '';
    let bestScore = 0;

    sentences.forEach(sentence => {
      const lowerSentence = sentence.toLowerCase();
      let score = 0;
      
      queryWords.forEach(word => {
        if (lowerSentence.includes(word)) {
          score++;
        }
      });

      if (score > bestScore) {
        bestScore = score;
        bestSentence = sentence;
      }
    });

    // Truncar se necessário
    if (bestSentence.length > maxLength) {
      bestSentence = bestSentence.substring(0, maxLength) + '...';
    }

    return bestSentence.trim();
  }

  /**
   * Extrair palavras-chave
   */
  extractKeywords(text) {
    // Palavras comuns a serem ignoradas
    const stopWords = new Set([
      'o', 'a', 'os', 'as', 'um', 'uma', 'uns', 'umas',
      'de', 'do', 'da', 'dos', 'das', 'em', 'no', 'na', 'nos', 'nas',
      'para', 'por', 'com', 'sem', 'sob', 'sobre', 'entre',
      'e', 'ou', 'mas', 'que', 'se', 'não', 'sim', 'como',
      'este', 'esta', 'estes', 'estas', 'esse', 'essa', 'esses', 'essas',
      'aquele', 'aquela', 'aqueles', 'aquelas', 'seu', 'sua', 'seus', 'suas'
    ]);

    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3 && !stopWords.has(word));

    // Contar frequência
    const wordCount = {};
    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    // Retornar palavras mais frequentes
    return Object.entries(wordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20)
      .map(([word, count]) => ({ word, count }));
  }

  /**
   * Destacar correspondências no texto
   */
  highlightMatches(text, query) {
    const queryWords = query.toLowerCase().split(/\s+/);
    let highlightedText = text;

    queryWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      highlightedText = highlightedText.replace(regex, `<mark>$&</mark>`);
    });

    return highlightedText;
  }

  /**
   * Buscar documentos similares
   */
  async findSimilarDocuments(recordingId, options = {}) {
    try {
      // Buscar embedding do documento original
      const aiDatabase = require('../database/aiDatabase');
      const originalEmbedding = await aiDatabase.getRecordingEmbedding(recordingId);

      if (!originalEmbedding) {
        throw new Error('Embedding não encontrado para a gravação');
      }

      const filters = {
        minSimilarity: options.minSimilarity || 0.6,
        maxResults: options.maxResults || 10,
        excludeId: recordingId
      };

      const results = await aiDatabase.searchSimilarContent(originalEmbedding, filters);

      return {
        originalRecordingId: recordingId,
        similarDocuments: results,
        totalFound: results.length
      };

    } catch (error) {
      logger.error('Erro ao buscar documentos similares:', error);
      throw new Error(`Falha na busca de documentos similares: ${error.message}`);
    }
  }

  /**
   * Agrupar documentos por similaridade
   */
  async clusterDocuments(recordingIds, options = {}) {
    try {
      const aiDatabase = require('../database/aiDatabase');
      const embeddings = await aiDatabase.getMultipleEmbeddings(recordingIds);

      // Implementar clustering simples baseado em similaridade
      const clusters = [];
      const processed = new Set();

      for (const embedding of embeddings) {
        if (processed.has(embedding.recording_id)) continue;

        const cluster = {
          centroid: embedding,
          members: [embedding],
          avgSimilarity: 0
        };

        // Encontrar documentos similares para este cluster
        const similar = await aiDatabase.searchSimilarContent(
          embedding.embedding,
          { minSimilarity: options.clusterThreshold || 0.8, maxResults: 50 }
        );

        similar.forEach(sim => {
          if (!processed.has(sim.recording_id) && sim.recording_id !== embedding.recording_id) {
            cluster.members.push(sim);
            processed.add(sim.recording_id);
          }
        });

        if (cluster.members.length > 1) {
          cluster.avgSimilarity = cluster.members.reduce((sum, m) => sum + (m.similarity || 0), 0) / cluster.members.length;
          clusters.push(cluster);
        }

        processed.add(embedding.recording_id);
      }

      return {
        clusters: clusters.sort((a, b) => b.avgSimilarity - a.avgSimilarity),
        totalClusters: clusters.length,
        processedDocuments: processed.size
      };

    } catch (error) {
      logger.error('Erro no clustering de documentos:', error);
      throw new Error(`Falha no clustering: ${error.message}`);
    }
  }

  /**
   * Sugerir tags baseadas no conteúdo
   */
  async suggestTags(text, existingTags = []) {
    try {
      const prompt = `
      Analise o seguinte texto de uma gravação de cartório e sugira 5-10 tags relevantes:

      "${text.substring(0, 2000)}"

      Tags existentes no sistema: ${existingTags.join(', ')}

      Retorne apenas uma lista de tags separadas por vírgula, focando em:
      - Tipo de documento/procedimento
      - Área do direito
      - Partes envolvidas (sem nomes específicos)
      - Ações realizadas
      - Temas jurídicos relevantes
      `;

      const response = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.3,
        max_tokens: 200
      });

      const suggestedTags = response.choices[0].message.content
        .split(',')
        .map(tag => tag.trim().toLowerCase())
        .filter(tag => tag.length > 0)
        .slice(0, 10);

      return suggestedTags;

    } catch (error) {
      logger.error('Erro ao sugerir tags:', error);
      throw new Error(`Falha na sugestão de tags: ${error.message}`);
    }
  }

  /**
   * Processar gravação para busca semântica
   */
  async processRecordingForSearch(recordingId, transcription) {
    try {
      // Criar embedding
      const embeddingData = await this.createContentEmbedding(transcription);
      
      // Extrair palavras-chave
      const keywords = this.extractKeywords(transcription);
      
      // Sugerir tags
      const suggestedTags = await this.suggestTags(transcription);

      // Salvar no banco de dados
      const aiDatabase = require('../database/aiDatabase');
      await aiDatabase.saveEmbedding(recordingId, {
        embedding: embeddingData.embedding,
        keywords,
        suggestedTags,
        model: embeddingData.model,
        tokens: embeddingData.tokens
      });

      return {
        recordingId,
        embeddingCreated: true,
        keywords: keywords.slice(0, 10),
        suggestedTags,
        tokens: embeddingData.tokens
      };

    } catch (error) {
      logger.error('Erro ao processar gravação para busca:', error);
      throw new Error(`Falha no processamento: ${error.message}`);
    }
  }
}

  /**
   * Buscar por período de tempo
   */
  async searchByTimeRange(startDate, endDate, options = {}) {
    try {
      const aiDatabase = require('../database/aiDatabase');
      const results = await aiDatabase.getRecordingsByTimeRange(startDate, endDate);

      if (options.query) {
        // Aplicar busca semântica nos resultados filtrados por tempo
        return await this.findSimilarContent(options.query, {
          ...options,
          dateFrom: startDate,
          dateTo: endDate
        });
      }

      return {
        results,
        totalFound: results.length,
        timeRange: { startDate, endDate }
      };

    } catch (error) {
      logger.error('Erro na busca por período:', error);
      throw new Error(`Falha na busca por período: ${error.message}`);
    }
  }
}

module.exports = SemanticSearchService;

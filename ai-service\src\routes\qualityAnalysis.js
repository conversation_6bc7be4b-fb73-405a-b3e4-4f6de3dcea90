const express = require('express');
const QualityAnalysisService = require('../services/qualityAnalysisService');
const queueManager = require('../services/queueManager');

const router = express.Router();
const qualityAnalysisService = new QualityAnalysisService();

/**
 * POST /api/ai/quality/analyze
 * Analisar qualidade de gravação e transcrição
 */
router.post('/analyze', async (req, res) => {
  try {
    const { videoPath, transcription, metadata } = req.body;

    if (!videoPath && !transcription) {
      return res.status(400).json({
        success: false,
        message: 'Pelo menos um dos campos (videoPath ou transcription) é obrigatório'
      });
    }

    const result = await qualityAnalysisService.analyzeRecordingQuality(
      videoPath,
      transcription,
      metadata
    );

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro na análise de qualidade',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/quality/recording/:recordingId
 * Analisar qualidade de uma gravação específica
 */
router.post('/recording/:recordingId', async (req, res) => {
  try {
    const { recordingId } = req.params;
    
    // Buscar dados da gravação e transcrição
    const axios = require('axios');
    const backendUrl = process.env.BACKEND_URL || 'http://backend:3001';
    
    const [recordingResponse, transcriptionResponse] = await Promise.all([
      axios.get(`${backendUrl}/api/recordings/${recordingId}`),
      axios.get(`http://localhost:3003/api/ai/transcription/recording/${recordingId}`)
        .catch(() => ({ data: null })) // Transcrição é opcional
    ]);

    const recording = recordingResponse.data;
    const transcriptionData = transcriptionResponse.data?.data;

    if (!recording) {
      return res.status(404).json({
        success: false,
        message: 'Gravação não encontrada'
      });
    }

    const result = await qualityAnalysisService.analyzeRecordingQuality(
      recording.file_path,
      transcriptionData?.text,
      {
        filename: recording.filename,
        created_at: recording.created_at,
        user_name: recording.user_name,
        file_size: recording.file_size,
        duration: recording.duration
      }
    );

    // Salvar resultado no banco de dados
    const aiDatabase = require('../database/aiDatabase');
    await aiDatabase.saveQualityAnalysis(recordingId, result);

    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro na análise de qualidade da gravação',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/quality/batch
 * Analisar qualidade de múltiplas gravações
 */
router.post('/batch', async (req, res) => {
  try {
    const { recordingIds } = req.body;

    if (!recordingIds || !Array.isArray(recordingIds)) {
      return res.status(400).json({
        success: false,
        message: 'Lista de IDs de gravações é obrigatória'
      });
    }

    // Adicionar trabalhos à fila
    const jobs = [];
    for (const recordingId of recordingIds) {
      const job = await queueManager.addQualityAnalysisJob(recordingId);
      jobs.push({
        recordingId,
        jobId: job.id,
        status: 'queued'
      });
    }

    res.json({
      success: true,
      message: `${jobs.length} trabalhos de análise de qualidade adicionados à fila`,
      data: jobs
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao processar lote de análises de qualidade',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/quality/recording/:recordingId
 * Obter análise de qualidade de uma gravação
 */
router.get('/recording/:recordingId', async (req, res) => {
  try {
    const { recordingId } = req.params;
    
    const aiDatabase = require('../database/aiDatabase');
    const analysis = await aiDatabase.getQualityAnalysis(recordingId);

    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Análise de qualidade não encontrada'
      });
    }

    res.json({
      success: true,
      data: analysis
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao buscar análise de qualidade',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/quality/report
 * Gerar relatório de qualidade
 */
router.get('/report', async (req, res) => {
  try {
    const { 
      recordingIds, 
      period = '30d',
      startDate,
      endDate 
    } = req.query;

    let ids = recordingIds;
    if (typeof recordingIds === 'string') {
      ids = recordingIds.split(',');
    }

    const report = await qualityAnalysisService.generateQualityReport(ids, period);

    res.json({
      success: true,
      data: report
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao gerar relatório de qualidade',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/quality/dashboard
 * Dashboard de qualidade com métricas principais
 */
router.get('/dashboard', async (req, res) => {
  try {
    const aiDatabase = require('../database/aiDatabase');
    
    const [
      totalAnalyses,
      recentAnalyses,
      qualityDistribution,
      averageScore,
      commonIssues
    ] = await Promise.all([
      aiDatabase.getTotalQualityAnalyses(),
      aiDatabase.getRecentQualityAnalyses(7),
      aiDatabase.getQualityDistribution(),
      aiDatabase.getAverageQualityScore(),
      aiDatabase.getCommonQualityIssues(10)
    ]);

    const dashboard = {
      summary: {
        totalAnalyses,
        recentAnalyses: recentAnalyses.length,
        averageScore: Math.round(averageScore * 100) / 100,
        qualityRate: recentAnalyses.filter(a => a.overall_score >= 80).length / Math.max(recentAnalyses.length, 1)
      },
      qualityDistribution,
      commonIssues: commonIssues.slice(0, 5),
      trends: {
        last7Days: recentAnalyses.map(a => ({
          date: a.created_at,
          score: a.overall_score,
          qualityLevel: a.quality_level
        }))
      },
      alerts: recentAnalyses
        .filter(a => a.overall_score < 60)
        .slice(0, 5)
        .map(a => ({
          recordingId: a.recording_id,
          score: a.overall_score,
          qualityLevel: a.quality_level,
          date: a.created_at,
          mainIssues: a.analysis?.recommendations?.slice(0, 2) || []
        }))
    };

    res.json({
      success: true,
      data: dashboard
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao carregar dashboard de qualidade',
      error: error.message
    });
  }
});

/**
 * GET /api/ai/quality/recommendations/:recordingId
 * Obter recomendações específicas para uma gravação
 */
router.get('/recommendations/:recordingId', async (req, res) => {
  try {
    const { recordingId } = req.params;
    
    const aiDatabase = require('../database/aiDatabase');
    const analysis = await aiDatabase.getQualityAnalysis(recordingId);

    if (!analysis) {
      return res.status(404).json({
        success: false,
        message: 'Análise de qualidade não encontrada'
      });
    }

    const recommendations = analysis.analysis?.recommendations || [];
    
    // Priorizar recomendações
    const prioritizedRecommendations = recommendations.sort((a, b) => {
      const priorityOrder = { alta: 3, media: 2, baixa: 1 };
      return (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
    });

    res.json({
      success: true,
      data: {
        recordingId,
        overallScore: analysis.overall_score,
        qualityLevel: analysis.quality_level,
        recommendations: prioritizedRecommendations,
        actionPlan: this.generateActionPlan(prioritizedRecommendations)
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao buscar recomendações',
      error: error.message
    });
  }
});

/**
 * POST /api/ai/quality/compare
 * Comparar qualidade entre múltiplas gravações
 */
router.post('/compare', async (req, res) => {
  try {
    const { recordingIds } = req.body;

    if (!recordingIds || !Array.isArray(recordingIds) || recordingIds.length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Pelo menos 2 IDs de gravações são necessários para comparação'
      });
    }

    const aiDatabase = require('../database/aiDatabase');
    const analyses = await Promise.all(
      recordingIds.map(id => aiDatabase.getQualityAnalysis(id))
    );

    const validAnalyses = analyses.filter(a => a !== null);

    if (validAnalyses.length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Pelo menos 2 análises válidas são necessárias'
      });
    }

    const comparison = {
      recordings: validAnalyses.map(a => ({
        recordingId: a.recording_id,
        overallScore: a.overall_score,
        qualityLevel: a.quality_level,
        audioScore: a.analysis?.audioQuality?.score || null,
        transcriptionScore: a.analysis?.transcriptionQuality?.score || null
      })),
      bestPerforming: validAnalyses.reduce((best, current) => 
        current.overall_score > best.overall_score ? current : best
      ),
      worstPerforming: validAnalyses.reduce((worst, current) => 
        current.overall_score < worst.overall_score ? current : worst
      ),
      averageScore: validAnalyses.reduce((sum, a) => sum + a.overall_score, 0) / validAnalyses.length,
      scoreRange: {
        min: Math.min(...validAnalyses.map(a => a.overall_score)),
        max: Math.max(...validAnalyses.map(a => a.overall_score))
      }
    };

    res.json({
      success: true,
      data: comparison
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Erro ao comparar qualidade das gravações',
      error: error.message
    });
  }
});

/**
 * Gerar plano de ação baseado nas recomendações
 */
function generateActionPlan(recommendations) {
  const actionPlan = {
    immediate: recommendations.filter(r => r.priority === 'alta'),
    shortTerm: recommendations.filter(r => r.priority === 'media'),
    longTerm: recommendations.filter(r => r.priority === 'baixa')
  };

  return {
    ...actionPlan,
    totalActions: recommendations.length,
    estimatedImpact: recommendations.length > 0 ? 'medio' : 'baixo'
  };
}

module.exports = router;

using System;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using SharpAvi;
using SharpAvi.Codecs;
using SharpAvi.Output;
using NAudio.Wave;

namespace CartorioDesktopApp
{
    public class ScreenRecorder
    {
        private AviWriter? _writer;
        private IAviVideoStream? _videoStream;
        private IAviAudioStream? _audioStream;
        private Thread? _recordingThread;
        private WaveInEvent? _waveIn;
        private CancellationTokenSource? _cancellationTokenSource;
        private Rectangle _recordingRegion;
        private string _outputFilePath = "";
        private bool _recordAudio = true;
        private bool _isRecording = false;
        
        // Configurações padrão
        private int _frameRate = 30;
        private int _quality = 70;
        
        /// <summary>
        /// Configura a gravação para tela inteira
        /// </summary>
        public void SetupFullScreenRecording()
        {
            _recordingRegion = Screen.PrimaryScreen.Bounds;
        }
        
        /// <summary>
        /// Configura a gravação para uma janela específica
        /// </summary>
        public void SetupWindowRecording(IntPtr windowHandle)
        {
            if (!GetWindowRect(windowHandle, out RECT rect))
                throw new InvalidOperationException("Não foi possível obter as dimensões da janela");
            
            _recordingRegion = new Rectangle(rect.Left, rect.Top, rect.Right - rect.Left, rect.Bottom - rect.Top);
        }
        
        /// <summary>
        /// Configura a gravação para uma região específica da tela
        /// </summary>
        public void SetupRegionRecording(Rectangle region)
        {
            _recordingRegion = region;
        }
        
        /// <summary>
        /// Inicia a gravação
        /// </summary>
        public async Task<string> StartRecordingAsync()
        {
            if (_isRecording)
                throw new InvalidOperationException("Uma gravação já está em andamento");
            
            // Gerar nome do arquivo de saída
            _outputFilePath = Path.Combine(
                AppSettings.RecordingsDirectory,
                $"Recording_{DateTime.Now:yyyyMMdd_HHmmss}.avi");
            
            // Criar o gravador AVI
            _writer = new AviWriter(_outputFilePath)
            {
                FramesPerSecond = _frameRate,
                EmitIndex1 = true
            };
            
            // Configurar stream de vídeo
            _videoStream = _writer.AddVideoStream(
                width: _recordingRegion.Width, 
                height: _recordingRegion.Height, 
                bitsPerPixel: 24, 
                codec: KnownFourCCs.Codecs.Xvid, 
                quality: _quality);
            
            // Configurar stream de áudio (se necessário)
            if (_recordAudio)
            {
                _audioStream = _writer.AddAudioStream(
                    channelCount: 2,
                    samplesPerSecond: 44100,
                    bitsPerSample: 16);
                
                // Iniciar captura de áudio
                _waveIn = new WaveInEvent
                {
                    WaveFormat = new WaveFormat(44100, 16, 2)
                };
                
                _waveIn.DataAvailable += (s, e) =>
                {
                    if (_audioStream != null && _isRecording)
                    {
                        _audioStream.WriteBlock(e.Buffer, 0, e.BytesRecorded);
                    }
                };
                
                _waveIn.StartRecording();
            }
            
            // Iniciar thread de gravação
            _cancellationTokenSource = new CancellationTokenSource();
            _isRecording = true;
            
            _recordingThread = new Thread(RecordingThread)
            {
                IsBackground = true,
                Name = "Screen Recording Thread"
            };
            
            _recordingThread.Start(_cancellationTokenSource.Token);
            
            return _outputFilePath;
        }
        
        /// <summary>
        /// Para a gravação atual
        /// </summary>
        public async Task<string> StopRecordingAsync()
        {
            if (!_isRecording)
                throw new InvalidOperationException("Nenhuma gravação em andamento");
            
            _isRecording = false;
            _cancellationTokenSource?.Cancel();
            
            // Aguardar a thread de gravação terminar
            if (_recordingThread != null && _recordingThread.IsAlive)
            {
                await Task.Run(() => _recordingThread.Join(5000));
            }
            
            // Parar a captura de áudio
            if (_waveIn != null)
            {
                _waveIn.StopRecording();
                _waveIn.Dispose();
                _waveIn = null;
            }
            
            // Finalizar o arquivo AVI
            _writer?.Close();
            _writer = null;
            
            return _outputFilePath;
        }
        
        /// <summary>
        /// Thread de gravação de tela
        /// </summary>
        private void RecordingThread(object? state)
        {
            if (state == null || _videoStream == null)
                return;
            
            var cancellationToken = (CancellationToken)state;
            var frameInterval = TimeSpan.FromSeconds(1.0 / _frameRate);
            var buffer = new byte[_recordingRegion.Width * _recordingRegion.Height * 4];
            
            DateTime lastFrameTime = DateTime.Now;
            
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    // Capturar frame da tela
                    using (var bitmap = new Bitmap(_recordingRegion.Width, _recordingRegion.Height))
                    using (var graphics = Graphics.FromImage(bitmap))
                    {
                        graphics.CopyFromScreen(
                            _recordingRegion.Left, 
                            _recordingRegion.Top, 
                            0, 0, 
                            bitmap.Size, 
                            CopyPixelOperation.SourceCopy);
                        
                        // Converter bitmap para o formato do stream
                        using (var stream = new MemoryStream())
                        {
                            bitmap.Save(stream, System.Drawing.Imaging.ImageFormat.Bmp);
                            stream.Position = 0;
                            
                            // Pular cabeçalho BMP
                            stream.Seek(54, SeekOrigin.Begin);
                            
                            // Ler os dados da imagem
                            stream.Read(buffer, 0, buffer.Length);
                            
                            // Enviar frame para o stream de vídeo
                            _videoStream.WriteFrame(true, buffer, 0, buffer.Length);
                        }
                    }
                    
                    // Controlar a taxa de frames
                    TimeSpan elapsed = DateTime.Now - lastFrameTime;
                    if (elapsed < frameInterval)
                    {
                        int sleepTime = (int)(frameInterval - elapsed).TotalMilliseconds;
                        if (sleepTime > 0)
                            Thread.Sleep(sleepTime);
                    }
                    
                    lastFrameTime = DateTime.Now;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro na thread de gravação: {ex.Message}", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        #region Windows API
        
        [DllImport("user32.dll")]
        [return: MarshalAs(UnmanagedType.Bool)]
        static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);
        
        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }
        
        #endregion
    }
}

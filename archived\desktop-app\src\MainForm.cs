using System;
using System.Drawing;
using System.Windows.Forms;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace CartorioDesktopApp
{
    public partial class MainForm : Form
    {
        private ScreenRecorder? _recorder;
        private Rectangle? _selectedRegion;
        private readonly HttpClient _httpClient;
        
        public MainForm()
        {
            InitializeComponent();
            
            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri(AppSettings.ApiUrl);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", $"CartorioDesktopApp/{AppSettings.AppVersion}");
            
            // Configurar os controles na inicialização
            SetupControls();
        }

        private void SetupControls()
        {
            // Inicializar componentes UI (esta parte seria implementada no designer)
            // Aqui estamos apenas simulando a configuração programática
            
            this.Text = "Sistema de Gravação para Cartório";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Icon = new Icon(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "app_icon.ico"));
            
            // Tabs para diferentes modos de gravação
            var tabControl = new TabControl();
            tabControl.Dock = DockStyle.Fill;
            
            // Tab para gravação de tela inteira
            var tabFullScreen = new TabPage("Tela Inteira");
            var btnRecordFullScreen = new Button();
            btnRecordFullScreen.Text = "Iniciar Gravação";
            btnRecordFullScreen.Size = new Size(150, 40);
            btnRecordFullScreen.Location = new Point(20, 20);
            btnRecordFullScreen.Click += (s, e) => StartRecording(RecordingMode.FullScreen);
            tabFullScreen.Controls.Add(btnRecordFullScreen);
            
            // Tab para gravação de janela específica
            var tabWindow = new TabPage("Janela Específica");
            var btnRecordWindow = new Button();
            btnRecordWindow.Text = "Iniciar Gravação";
            btnRecordWindow.Size = new Size(150, 40);
            btnRecordWindow.Location = new Point(20, 20);
            btnRecordWindow.Click += (s, e) => StartRecording(RecordingMode.Window);
            tabWindow.Controls.Add(btnRecordWindow);
            
            // Tab para gravação de região específica
            var tabRegion = new TabPage("Região Específica");
            var btnSelectRegion = new Button();
            btnSelectRegion.Text = "Selecionar Região";
            btnSelectRegion.Size = new Size(150, 40);
            btnSelectRegion.Location = new Point(20, 20);
            btnSelectRegion.Click += (s, e) => SelectRegion();
            tabRegion.Controls.Add(btnSelectRegion);
            
            var btnRecordRegion = new Button();
            btnRecordRegion.Text = "Iniciar Gravação";
            btnRecordRegion.Size = new Size(150, 40);
            btnRecordRegion.Location = new Point(20, 70);
            btnRecordRegion.Click += (s, e) => StartRecording(RecordingMode.Region);
            tabRegion.Controls.Add(btnRecordRegion);
            
            // Tab para configurações
            var tabSettings = new TabPage("Configurações");
            var lblApiUrl = new Label();
            lblApiUrl.Text = "URL da API:";
            lblApiUrl.Location = new Point(20, 20);
            tabSettings.Controls.Add(lblApiUrl);
            
            var txtApiUrl = new TextBox();
            txtApiUrl.Text = AppSettings.ApiUrl;
            txtApiUrl.Size = new Size(300, 25);
            txtApiUrl.Location = new Point(20, 45);
            tabSettings.Controls.Add(txtApiUrl);
            
            var btnSaveSettings = new Button();
            btnSaveSettings.Text = "Salvar";
            btnSaveSettings.Size = new Size(100, 30);
            btnSaveSettings.Location = new Point(20, 80);
            btnSaveSettings.Click += (s, e) =>
            {
                AppSettings.UpdateApiUrl(txtApiUrl.Text);
                MessageBox.Show("Configurações salvas com sucesso!", "Configurações", MessageBoxButtons.OK, MessageBoxIcon.Information);
            };
            tabSettings.Controls.Add(btnSaveSettings);
            
            // Adicionar tabs ao controle
            tabControl.TabPages.Add(tabFullScreen);
            tabControl.TabPages.Add(tabWindow);
            tabControl.TabPages.Add(tabRegion);
            tabControl.TabPages.Add(tabSettings);
            
            this.Controls.Add(tabControl);
        }

        private async void StartRecording(RecordingMode mode)
        {
            // Exibir formulário de consentimento LGPD
            var consentForm = new LGPDConsentForm();
            if (consentForm.ShowDialog() != DialogResult.OK)
            {
                // Usuário não consentiu
                MessageBox.Show("A gravação não pode ser iniciada sem consentimento.", "Consentimento LGPD", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                // Configurar gravação de acordo com o modo selecionado
                _recorder = new ScreenRecorder();
                
                switch (mode)
                {
                    case RecordingMode.FullScreen:
                        _recorder.SetupFullScreenRecording();
                        break;
                    case RecordingMode.Window:
                        var windowSelector = new WindowSelectorForm();
                        if (windowSelector.ShowDialog() == DialogResult.OK)
                        {
                            _recorder.SetupWindowRecording(windowSelector.SelectedWindow);
                        }
                        else
                        {
                            return; // Usuário cancelou a seleção
                        }
                        break;
                    case RecordingMode.Region:
                        if (_selectedRegion.HasValue)
                        {
                            _recorder.SetupRegionRecording(_selectedRegion.Value);
                        }
                        else
                        {
                            MessageBox.Show("Por favor, selecione uma região primeiro.", "Seleção de Região", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            return;
                        }
                        break;
                    default:
                        throw new NotImplementedException("Modo de gravação não suportado");
                }

                // Iniciar gravação
                string filePath = await _recorder.StartRecordingAsync();

                // Exibir controles de gravação (botão de parar, etc.)
                ShowRecordingControls();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao iniciar gravação: {ex.Message}", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SelectRegion()
        {
            // Usar um formulário transparente para selecionar a região
            var regionSelector = new RegionSelectorForm();
            if (regionSelector.ShowDialog() == DialogResult.OK)
            {
                _selectedRegion = regionSelector.SelectedRegion;
                MessageBox.Show("Região selecionada com sucesso!", "Seleção de Região", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ShowRecordingControls()
        {
            // Esta implementação seria mais complexa em um aplicativo real
            // Aqui estamos apenas criando um formulário simples com botão de parar
            
            var recordingForm = new Form();
            recordingForm.Text = "Gravação em Andamento";
            recordingForm.Size = new Size(300, 150);
            recordingForm.StartPosition = FormStartPosition.CenterScreen;
            recordingForm.FormBorderStyle = FormBorderStyle.FixedDialog;
            recordingForm.MaximizeBox = false;
            recordingForm.MinimizeBox = false;
            
            var lblRecording = new Label();
            lblRecording.Text = "Gravação em andamento...";
            lblRecording.AutoSize = true;
            lblRecording.Location = new Point(50, 30);
            recordingForm.Controls.Add(lblRecording);
            
            var btnStopRecording = new Button();
            btnStopRecording.Text = "Parar Gravação";
            btnStopRecording.Size = new Size(150, 40);
            btnStopRecording.Location = new Point(75, 70);
            btnStopRecording.Click += async (s, e) =>
            {
                try
                {
                    string filePath = await StopRecording();
                    recordingForm.Close();
                    
                    // Perguntar se deseja enviar para o servidor
                    if (MessageBox.Show("Gravação concluída com sucesso! Deseja enviar para o servidor?",
                                        "Gravação Concluída", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        await UploadRecordingAsync(filePath);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Erro ao parar gravação: {ex.Message}", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };
            recordingForm.Controls.Add(btnStopRecording);
            
            recordingForm.Show();
        }

        private async Task<string> StopRecording()
        {
            if (_recorder == null)
                throw new InvalidOperationException("Nenhuma gravação em andamento");
            
            string filePath = await _recorder.StopRecordingAsync();
            _recorder = null;
            
            return filePath;
        }

        private async Task UploadRecordingAsync(string filePath)
        {
            try
            {
                // Mostrar diálogo de progresso
                var progressForm = new Form();
                progressForm.Text = "Enviando Gravação";
                progressForm.Size = new Size(300, 100);
                progressForm.StartPosition = FormStartPosition.CenterScreen;
                progressForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                progressForm.MaximizeBox = false;
                progressForm.MinimizeBox = false;
                
                var progressBar = new ProgressBar();
                progressBar.Style = ProgressBarStyle.Marquee;
                progressBar.Size = new Size(250, 25);
                progressBar.Location = new Point(25, 20);
                progressForm.Controls.Add(progressBar);
                
                progressForm.Show();
                
                // Preparar dados para upload
                var fileInfo = new FileInfo(filePath);
                
                // Calcular hash do arquivo
                string hash = SecurityUtils.CalculateFileHash(filePath);
                
                // Criar um MultipartFormDataContent
                using (var content = new MultipartFormDataContent())
                {
                    // Adicionar o arquivo
                    var fileContent = new ByteArrayContent(await File.ReadAllBytesAsync(filePath));
                    content.Add(fileContent, "video", Path.GetFileName(filePath));
                    
                    // Adicionar metadados
                    content.Add(new StringContent(DateTime.Now.ToString("o")), "consent_timestamp");
                    content.Add(new StringContent("127.0.0.1"), "consent_ip"); // Normalmente seria o IP real
                    content.Add(new StringContent(SecurityUtils.GetDeviceInfo()), "device_info");
                    content.Add(new StringContent("horizontal"), "orientation");
                    content.Add(new StringContent($"CartorioDesktopApp/{AppSettings.AppVersion}"), "consent_user_agent");
                    
                    // Enviar para o servidor
                    var response = await _httpClient.PostAsync("/api/recordings", content);
                    
                    progressForm.Close();
                    
                    if (response.IsSuccessStatusCode)
                    {
                        var responseContent = await response.Content.ReadAsStringAsync();
                        var result = JsonConvert.DeserializeObject<Dictionary<string, string>>(responseContent);
                        
                        if (result != null && result.TryGetValue("id", out string? recordingId))
                        {
                            MessageBox.Show($"Gravação enviada com sucesso!\nID: {recordingId}\nHash: {hash}", 
                                           "Upload Concluído", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            
                            // Perguntar se deseja gerar relatório
                            if (MessageBox.Show("Deseja gerar um relatório em PDF para esta gravação?",
                                              "Gerar Relatório", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                            {
                                await GenerateReportAsync(recordingId);
                            }
                        }
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        throw new Exception($"Erro HTTP {(int)response.StatusCode}: {errorContent}");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao enviar gravação: {ex.Message}", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task GenerateReportAsync(string recordingId)
        {
            try
            {
                // Mostrar diálogo de progresso
                var progressForm = new Form();
                progressForm.Text = "Gerando Relatório";
                progressForm.Size = new Size(300, 100);
                progressForm.StartPosition = FormStartPosition.CenterScreen;
                progressForm.FormBorderStyle = FormBorderStyle.FixedDialog;
                progressForm.MaximizeBox = false;
                progressForm.MinimizeBox = false;
                
                var progressBar = new ProgressBar();
                progressBar.Style = ProgressBarStyle.Marquee;
                progressBar.Size = new Size(250, 25);
                progressBar.Location = new Point(25, 20);
                progressForm.Controls.Add(progressBar);
                
                progressForm.Show();
                
                // Solicitar geração de relatório
                var response = await _httpClient.PostAsync($"/api/reports/{recordingId}", null);
                
                progressForm.Close();
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var result = JsonConvert.DeserializeObject<Dictionary<string, string>>(responseContent);
                    
                    if (result != null && result.TryGetValue("id", out string? reportId))
                    {
                        MessageBox.Show($"Relatório gerado com sucesso!\nID: {reportId}", 
                                       "Relatório Gerado", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    throw new Exception($"Erro HTTP {(int)response.StatusCode}: {errorContent}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erro ao gerar relatório: {ex.Message}", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    /// <summary>
    /// Modos de gravação suportados
    /// </summary>
    public enum RecordingMode
    {
        FullScreen,
        Window,
        Region
    }
}

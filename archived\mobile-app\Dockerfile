FROM ubuntu:22.04

# Evitar perguntas interativas durante a instalação
ENV DEBIAN_FRONTEND=noninteractive

# Instalar dependências
RUN apt-get update && apt-get install -y \
    curl \
    git \
    unzip \
    xz-utils \
    zip \
    libglu1-mesa \
    openjdk-11-jdk \
    && rm -rf /var/lib/apt/lists/*

# Definir variáveis de ambiente para o Flutter
ENV FLUTTER_HOME=/flutter
ENV PATH=$FLUTTER_HOME/bin:$PATH

# Baixar e configurar o Flutter
RUN git clone https://github.com/flutter/flutter.git $FLUTTER_HOME && \
    cd $FLUTTER_HOME && \
    git checkout stable && \
    flutter precache && \
    flutter doctor

# Configurar o diretório de trabalho
WORKDIR /app

# Copiar o pubspec para instalar dependências primeiro
COPY pubspec.* ./
RUN flutter pub get

# Copiar o restante do código
COPY . .

# Executar novamente para garantir que todas as dependências sejam instaladas
RUN flutter pub get

# Iniciar o servidor web do Flutter (para desenvolvimento)
EXPOSE 8080
CMD ["flutter", "run", "--web-port", "8080", "--web-hostname", "0.0.0.0"]

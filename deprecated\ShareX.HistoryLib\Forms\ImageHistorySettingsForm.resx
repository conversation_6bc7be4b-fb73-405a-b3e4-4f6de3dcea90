﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lblThumbnailSize.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lblThumbnailSize.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 16</value>
  </data>
  <data name="lblThumbnailSize.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 13</value>
  </data>
  <data name="lblThumbnailSize.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblThumbnailSize.Text" xml:space="preserve">
    <value>Thumbnail size:</value>
  </data>
  <data name="&gt;&gt;lblThumbnailSize.Name" xml:space="preserve">
    <value>lblThumbnailSize</value>
  </data>
  <data name="&gt;&gt;lblThumbnailSize.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblThumbnailSize.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblThumbnailSize.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="lblMaximumImageLimit.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblMaximumImageLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 64</value>
  </data>
  <data name="lblMaximumImageLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>105, 13</value>
  </data>
  <data name="lblMaximumImageLimit.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lblMaximumImageLimit.Text" xml:space="preserve">
    <value>Maximum image limit:</value>
  </data>
  <data name="&gt;&gt;lblMaximumImageLimit.Name" xml:space="preserve">
    <value>lblMaximumImageLimit</value>
  </data>
  <data name="&gt;&gt;lblMaximumImageLimit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblMaximumImageLimit.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblMaximumImageLimit.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="nudThumbnailSize.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 32</value>
  </data>
  <data name="nudThumbnailSize.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 20</value>
  </data>
  <data name="nudThumbnailSize.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="nudThumbnailSize.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudThumbnailSize.Name" xml:space="preserve">
    <value>nudThumbnailSize</value>
  </data>
  <data name="&gt;&gt;nudThumbnailSize.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudThumbnailSize.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudThumbnailSize.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="nudMaximumImageLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 80</value>
  </data>
  <data name="nudMaximumImageLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 20</value>
  </data>
  <data name="nudMaximumImageLimit.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="nudMaximumImageLimit.TextAlign" type="System.Windows.Forms.HorizontalAlignment, System.Windows.Forms">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;nudMaximumImageLimit.Name" xml:space="preserve">
    <value>nudMaximumImageLimit</value>
  </data>
  <data name="&gt;&gt;nudMaximumImageLimit.Type" xml:space="preserve">
    <value>System.Windows.Forms.NumericUpDown, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;nudMaximumImageLimit.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;nudMaximumImageLimit.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lblThumbnailSizeUnit.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblThumbnailSizeUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>85, 36</value>
  </data>
  <data name="lblThumbnailSizeUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>18, 13</value>
  </data>
  <data name="lblThumbnailSizeUnit.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lblThumbnailSizeUnit.Text" xml:space="preserve">
    <value>px</value>
    <comment>@Invariant</comment>
  </data>
  <data name="&gt;&gt;lblThumbnailSizeUnit.Name" xml:space="preserve">
    <value>lblThumbnailSizeUnit</value>
  </data>
  <data name="&gt;&gt;lblThumbnailSizeUnit.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;lblThumbnailSizeUnit.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblThumbnailSizeUnit.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="cbRememberSearchText.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbRememberSearchText.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="cbRememberSearchText.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 136</value>
  </data>
  <data name="cbRememberSearchText.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 17</value>
  </data>
  <data name="cbRememberSearchText.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="cbRememberSearchText.Text" xml:space="preserve">
    <value>Remember search input</value>
  </data>
  <data name="&gt;&gt;cbRememberSearchText.Name" xml:space="preserve">
    <value>cbRememberSearchText</value>
  </data>
  <data name="&gt;&gt;cbRememberSearchText.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbRememberSearchText.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbRememberSearchText.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="cbFilterMissingFiles.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbFilterMissingFiles.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 112</value>
  </data>
  <data name="cbFilterMissingFiles.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 17</value>
  </data>
  <data name="cbFilterMissingFiles.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="cbFilterMissingFiles.Text" xml:space="preserve">
    <value>Filter missing files</value>
  </data>
  <data name="&gt;&gt;cbFilterMissingFiles.Name" xml:space="preserve">
    <value>cbFilterMissingFiles</value>
  </data>
  <data name="&gt;&gt;cbFilterMissingFiles.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbFilterMissingFiles.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbFilterMissingFiles.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cbRememberWindowState.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cbRememberWindowState.Location" type="System.Drawing.Point, System.Drawing">
    <value>16, 160</value>
  </data>
  <data name="cbRememberWindowState.Size" type="System.Drawing.Size, System.Drawing">
    <value>142, 17</value>
  </data>
  <data name="cbRememberWindowState.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="cbRememberWindowState.Text" xml:space="preserve">
    <value>Remember window state</value>
  </data>
  <data name="&gt;&gt;cbRememberWindowState.Name" xml:space="preserve">
    <value>cbRememberWindowState</value>
  </data>
  <data name="&gt;&gt;cbRememberWindowState.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cbRememberWindowState.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cbRememberWindowState.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>96, 96</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>334, 211</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>ShareX - Image history settings</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>ImageHistorySettingsForm</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>